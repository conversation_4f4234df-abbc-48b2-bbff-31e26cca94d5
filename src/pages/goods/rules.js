let wine = {
    // eslint-disable-next-line camelcase
    goods_name: [
        {
            required: true,
            message: "请输入中文品名",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_goods_name: [
        {
            required: true,
            message: "请输入英文品名",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    short_code: [
        {
            required: true,
            message: "请输入简码",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_years: [
        {
            // type: "date",
            required: true,
            message: "请选择年份",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_type: [
        {
            required: true,
            message: "请选择商品类型",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_unit: [
        {
            required: true,
            message: "请选择商品单位",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_form: [
        {
            required: true,
            message: "请选择商品形态",
            trigger: "change"
        }
    ],
    capacity: [
        {
            required: true,
            message: "请填写规格",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    bar_code: [
        {
            required: true,
            message: "请输入条码",
            trigger: "change"
        }
    ],
    country: [
        {
            required: true,
            message: "请输入商品国家",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    producing_area: [
        {
            required: false,
            message: "请输入产区",
            trigger: "blur"
        }
    ],
    chateau: [
        {
            required: false,
            message: "请输入酒庄",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    shelf_life: [
        {
            required: true,
            message: "请填写保质期",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    produce_date: [
        {
            required: true,
            message: "请选择灌装日期",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    box_gauge: [
        {
            required: false,
            message: "请填写箱规",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    is_giveaway: [
        {
            required: true,
            message: "请选择是否赠品",
            trigger: "change"
        }
    ],
    length: [
        {
            required: false,
            message: "请输入长度",
            trigger: "change"
        }
    ],
    width: [
        {
            required: false,
            message: "请输入宽度",
            trigger: "change"
        }
    ],
    high: [
        {
            required: false,
            message: "请输入高度",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    positive_img: [
        {
            required: false,
            message: "请正确上传正标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    back_img: [
        {
            required: false,
            message: "请正确上传中文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_back_img: [
        {
            required: false,
            message: "请正确上传英文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    package_img: [
        {
            required: false,
            message: "请正确上传包装图",
            trigger: "blur"
        }
    ],
    annex: [
        {
            required: false,
            message: "请正确上传附件",
            trigger: "blur"
        }
    ],
    cost: [
        {
            required: true,
            message: "请输入成本",
            trigger: "blur"
        }
    ]
};
let drinks = {
    // eslint-disable-next-line camelcase
    goods_name: [
        {
            required: true,
            message: "请输入中文品名",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_goods_name: [
        {
            required: false,
            message: "请输入英文品名",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    short_code: [
        {
            required: true,
            message: "请输入简码",
            trigger: "blur"
        }
    ],

    // eslint-disable-next-line camelcase
    goods_type: [
        {
            required: true,
            message: "请选择商品类型",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_unit: [
        {
            required: true,
            message: "请选择商品单位",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_form: [
        {
            required: true,
            message: "请选择商品形态",
            trigger: "change"
        }
    ],
    capacity: [
        {
            required: true,
            message: "请填写规格",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    bar_code: [
        {
            required: true,
            message: "请输入条码",
            trigger: "change"
        }
    ],
    country: [
        {
            required: true,
            message: "请输入商品国家",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    shelf_life: [
        {
            required: true,
            message: "请填写保质期",
            trigger: "change"
        }
    ],

    // eslint-disable-next-line camelcase
    box_gauge: [
        {
            required: false,
            message: "请填写箱规",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    is_giveaway: [
        {
            required: true,
            message: "请选择是否赠品",
            trigger: "change"
        }
    ],
    length: [
        {
            required: false,
            message: "请输入长度",
            trigger: "change"
        }
    ],
    width: [
        {
            required: false,
            message: "请输入宽度",
            trigger: "change"
        }
    ],
    high: [
        {
            required: false,
            message: "请输入高度",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    positive_img: [
        {
            required: false,
            message: "请正确上传正标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    back_img: [
        {
            required: false,
            message: "请正确上传中文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_back_img: [
        {
            required: false,
            message: "请正确上传英文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    package_img: [
        {
            required: false,
            message: "请正确上传包装图",
            trigger: "blur"
        }
    ],
    annex: [
        {
            required: false,
            message: "请正确上传附件",
            trigger: "blur"
        }
    ],
    cost: [
        {
            required: true,
            message: "请输入成本",
            trigger: "blur"
        }
    ]
};
let food = {
    // eslint-disable-next-line camelcase
    goods_name: [
        {
            required: true,
            message: "请输入中文品名",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_goods_name: [
        {
            required: false,
            message: "请输入英文品名",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    short_code: [
        {
            required: true,
            message: "请输入简码",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_type: [
        {
            required: true,
            message: "请选择商品类型",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_unit: [
        {
            required: true,
            message: "请选择商品单位",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_form: [
        {
            required: true,
            message: "请选择商品形态",
            trigger: "change"
        }
    ],
    capacity: [
        {
            required: true,
            message: "请填写规格",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    bar_code: [
        {
            required: true,
            message: "请输入条码",
            trigger: "change"
        }
    ],
    country: [
        {
            required: true,
            message: "请输入商品国家",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    shelf_life: [
        {
            required: true,
            message: "请填写保质期",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    is_giveaway: [
        {
            required: true,
            message: "请选择是否赠品",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    positive_img: [
        {
            required: false,
            message: "请正确上传正标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    back_img: [
        {
            required: false,
            message: "请正确上传中文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_back_img: [
        {
            required: false,
            message: "请正确上传英文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    package_img: [
        {
            required: false,
            message: "请正确上传包装图",
            trigger: "blur"
        }
    ],
    annex: [
        {
            required: false,
            message: "请正确上传附件",
            trigger: "blur"
        }
    ],
    cost: [
        {
            required: true,
            message: "请输入成本",
            trigger: "blur"
        }
    ]
};
let material = {
    // eslint-disable-next-line camelcase
    goods_name: [
        {
            required: true,
            message: "请输入中文品名",
            trigger: "blur"
        }
    ],

    // eslint-disable-next-line camelcase
    short_code: [
        {
            required: true,
            message: "请输入简码",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_type: [
        {
            required: true,
            message: "请选择商品类型",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_unit: [
        {
            required: true,
            message: "请选择商品单位",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_form: [
        {
            required: true,
            message: "请选择商品形态",
            trigger: "change"
        }
    ],
    capacity: [
        {
            required: true,
            message: "请填写规格",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    bar_code: [
        {
            required: true,
            message: "请输入条码",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    is_giveaway: [
        {
            required: true,
            message: "请选择是否赠品",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    positive_img: [
        {
            required: false,
            message: "请正确上传正标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    back_img: [
        {
            required: false,
            message: "请正确上传中文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    en_back_img: [
        {
            required: false,
            message: "请正确上传英文背标图",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    package_img: [
        {
            required: false,
            message: "请正确上传包装图",
            trigger: "blur"
        }
    ],
    annex: [
        {
            required: false,
            message: "请正确上传附件",
            trigger: "blur"
        }
    ],
    cost: [
        {
            required: true,
            message: "请输入成本",
            trigger: "blur"
        }
    ]
};
let virtual = {
    // eslint-disable-next-line camelcase
    goods_name: [
        {
            required: true,
            message: "请输入中文品名",
            trigger: "blur"
        }
    ],

    // eslint-disable-next-line camelcase
    short_code: [
        {
            required: true,
            message: "请输入简码",
            trigger: "blur"
        }
    ],
    // eslint-disable-next-line camelcase
    bar_code: [
        {
            required: true,
            message: "请输入条码",
            trigger: "change"
        }
    ],
    // eslint-disable-next-line camelcase
    goods_type: [
        {
            required: true,
            message: "请选择商品类型",
            trigger: "blur"
        }
    ]
};
const rules = {
    wine,
    drinks,
    material,
    food,
    virtual
};

export default rules;
