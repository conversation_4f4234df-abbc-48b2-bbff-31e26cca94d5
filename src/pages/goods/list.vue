<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-select
                    style="margin-right: 10px"
                    v-model="value"
                    placeholder="请选择类型"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="keyword"
                    clearable
                    @keyup.enter.native="search"
                    placeholder="请输入关键字"
                ></el-input>
                <el-select
                    v-model="type_id"
                    placeholder="一级分类"
                    clearable
                    style="margin-left: 10px"
                    @change="onTypeIdChange"
                >
                    <el-option
                        v-for="item in firstCategoryList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
                <el-select
                    v-if="type_id"
                    v-model="sub_type_id"
                    placeholder="二级分类"
                    clearable
                    style="margin-left: 10px"
                >
                    <el-option
                        v-for="item in secondCategoryList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
                <el-checkbox
                    v-model="is_stock"
                    :true-label="1"
                    :false-label="0"
                    style="margin-left: 10px"
                    >有库存</el-checkbox
                >
                <el-button
                    style="margin-left: 10px"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
                <el-button style="margin-left: 10px" @click="add" type="primary"
                    >新增商品</el-button
                >
            </div>
        </div>

        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-form-item label="商品类型">
                                <span
                                    >{{ props.row.type_name }}-{{
                                        props.row.type1_name
                                    }}</span
                                >
                            </el-form-item>
                            <el-form-item
                                label="采摘年份"
                                v-if="props.row.goods_type == 1"
                            >
                                <span>{{ props.row.goods_years }}年</span>
                            </el-form-item>
                            <el-form-item
                                label="国家"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 3
                                "
                            >
                                <span>{{ props.row.country }}</span>
                            </el-form-item>
                            <el-form-item
                                label="产区"
                                v-if="props.row.goods_type == 1"
                            >
                                <span>{{ props.row.producing_area }}</span>
                            </el-form-item>
                            <el-form-item
                                label="酒庄"
                                v-if="props.row.goods_type == 1"
                            >
                                <span>{{ props.row.chateau }}</span>
                            </el-form-item>
                            <el-form-item
                                label="规格"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 3 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.capacity }}</span>
                            </el-form-item>
                            <el-form-item
                                label="商品单位"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 3 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.unit_name }}</span>
                            </el-form-item>

                            <el-form-item
                                label="保质期"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 3
                                "
                            >
                                <span>{{ props.row.shelf_life }}天</span>
                            </el-form-item>
                            <el-form-item
                                label="灌装日期"
                                v-if="props.row.goods_type == 1"
                            >
                                <span>{{ props.row.produce_date }}</span>
                            </el-form-item>
                            <el-form-item
                                label="箱规"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2
                                "
                            >
                                <span>{{ props.row.box_gauge }}</span>
                            </el-form-item>
                            <el-form-item
                                label="重量"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 3 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.weight }}千克</span>
                            </el-form-item>

                            <el-form-item
                                label="长"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.length }}厘米</span>
                            </el-form-item>
                            <el-form-item
                                label="宽"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.width }}厘米</span>
                            </el-form-item>
                            <el-form-item
                                label="高"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.high }}厘米</span>
                            </el-form-item>

                            <el-form-item
                                label="商品形态"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 3 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{
                                    propertyFormat(
                                        "liquid",
                                        props.row.goods_form
                                    )
                                }}</span>
                            </el-form-item>

                            <el-form-item
                                label="成本"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 3 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{ props.row.price }}</span>
                            </el-form-item>

                            <!-- <el-form-item label="良品实际量">
                                <span>{{ props.row.stock.great_actual }}</span>
                            </el-form-item>
                            <el-form-item label="良品可用量">
                                <span>{{
                                    props.row.stock.great_available
                                }}</span>
                            </el-form-item>
                            <el-form-item label="次品实际量">
                                <span>{{ props.row.stock.defect_actual }}</span>
                            </el-form-item>
                            <el-form-item label="次品可用量">
                                <span>{{
                                    props.row.stock.defect_available
                                }}</span>
                            </el-form-item> -->
                            <el-form-item
                                label="是否赠品"
                                v-if="
                                    props.row.goods_type == 1 ||
                                    props.row.goods_type == 2 ||
                                    props.row.goods_type == 4 ||
                                    props.row.goods_type == 3 ||
                                    props.row.goods_type == 6
                                "
                            >
                                <span>{{
                                    propertyFormat(
                                        "give",
                                        props.row.is_giveaway
                                    )
                                }}</span>
                            </el-form-item>
                            <el-form-item label="指令商品">
                                <span>{{
                                    propertyFormat("give", props.row.addition)
                                }}</span>
                            </el-form-item>
                            <el-form-item label="创建时间">
                                <span>{{ props.row.create_time }}</span>
                            </el-form-item>

                            <el-form-item label="附件" v-if="props.row.annex">
                                <div
                                    v-for="(item, index) in props.row.annex"
                                    :key="index"
                                >
                                    <a
                                        v-if="typeof item == 'string'"
                                        style="display: block"
                                        target="_blank"
                                        :href="props.row.aliurl + item"
                                        >{{ item | fileNameFormat }}</a
                                    >
                                    <a
                                        v-else
                                        style="display: block"
                                        target="_blank"
                                        :href="props.row.aliurl + item.url"
                                        >{{ item.url | fileNameFormat }}</a
                                    >
                                </div>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="bar_code"
                    width="150"
                    label="条码"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    width="150"
                    prop="short_code"
                    label="简码"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="goods_name"
                    label="中文品名"
                    width="350"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="en_goods_name"
                    label="英文品名"
                    align="center"
                >
                    <template slot-scope="row">
                        <span
                            v-if="
                                row.row.goods_type == 1 ||
                                row.row.goods_type == 2 ||
                                row.row.goods_type == 3
                            "
                        >
                            {{ row.row.en_goods_name }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" width="500">
                    <template slot-scope="row">
                        <div class="flex-c">
                            <el-button
                                type="warning"
                                size="mini"
                                @click="viewInventory(row.row)"
                                >查看库存</el-button
                            >
                            <el-button
                                size="mini"
                                type="primary"
                                @click="edit(row.row)"
                                >编辑</el-button
                            >
                            <el-button
                                type="success"
                                size="mini"
                                :disabled="!isHavaImges(row.row)"
                                @click="viewImages(row.row)"
                                >查看图片</el-button
                            >
                            <el-button
                                size="mini"
                                v-if="row.row.bar_code"
                                @click="openRemake(row.row)"
                                >备注</el-button
                            >
                            <el-button
                                :disabled="!row.row.bar_code"
                                size="mini"
                                type="danger"
                                @click="printBarCodeDialogOpen(row.row)"
                                >打印条码</el-button
                            >
                            <el-button
                                size="mini"
                                type="info"
                                @click="clone(row.row)"
                                >复制</el-button
                            >
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog
            title="备注信息"
            width="60%"
            :close-on-click-modal="false"
            center
            :visible.sync="viewRemakeStatus"
        >
            <el-button
                style="margin-bottom: 10px"
                @click="addRemark"
                size="mini"
                type="success"
                >新增备注</el-button
            >
            <el-table
                :data="remakeTableData"
                stripe
                size="mini"
                border
                style="width: 100%"
                ><el-table-column
                    width="100"
                    align="center"
                    prop="admin_name"
                    label="操作人"
                >
                </el-table-column>
                <el-table-column prop="comment" label="备注内容">
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="create_time"
                    label="操作时间"
                    width="150"
                >
                </el-table-column>
            </el-table>
            <el-dialog
                width="30%"
                title="新增备注"
                :close-on-click-modal="false"
                :visible.sync="innerVisible"
                append-to-body
            >
                <div>
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 10 }"
                        placeholder="请输入备注信息"
                        v-model="addRemakeMessage"
                        maxlength="200"
                        show-word-limit
                    >
                    </el-input>
                </div>
                <el-button
                    style="margin-top: 10px"
                    @click="addRemakeMessageSubmit"
                    type="primary"
                >
                    提交
                </el-button>
            </el-dialog>
        </el-dialog>
        <el-dialog
            title="图片预览"
            width="30%"
            center
            :close-on-click-modal="false"
            :visible.sync="viewImagesStatus"
        >
            <el-carousel height="400px" indicator-position="none">
                <el-carousel-item v-for="(item, index) in srcList" :key="item">
                    <el-image :src="item" :preview-src-list="srcList">
                    </el-image>
                    <div>
                        第 {{ index + 1 }} 张 （共计{{ srcList.length }}张）
                    </div>
                </el-carousel-item>
            </el-carousel>
        </el-dialog>

        <el-dialog
            title="打印条码"
            center
            :visible.sync="dialogStatus"
            width="30%"
        >
            <div style="text-align: center">
                <div style="margin-bottom: 20px">
                    条码：<el-tag>
                        {{ bar_code }}
                    </el-tag>
                </div>
                <div>
                    打印数量：
                    <el-input-number
                        v-model="count"
                        :min="1"
                        :step="1"
                    ></el-input-number>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogStatus = false">取 消</el-button>
                <el-button type="primary" @click="printBarCode">打印</el-button>
            </span>
        </el-dialog>
        <el-dialog
            title="查看次品图"
            center
            :visible.sync="viewDefectiveImgDialogStatus"
            width="480px"
        >
            <el-table
                class="stock"
                :data="viewDefectiveImgList"
                border
                highlight-current-row
                style="width: 390px"
            >
                <el-table-column
                    label="类型"
                    width="120"
                    align="center"
                    prop="type"
                >
                </el-table-column>
                <el-table-column
                    label="数量"
                    width="120"
                    prop="num"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="查看图片" width="150" align="center">
                    <template slot-scope="row">
                        <div class="demo-image__preview">
                            <el-image
                                style="width: 80px; height: 80px"
                                :src="row.row.img"
                                :preview-src-list="row.row.img"
                            >
                                <div slot="error" class="image-slot"></div>
                            </el-image>
                            <div>
                                点击图片查看
                                {{ row.row.img.length }}张
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!--  -->
        </el-dialog>
        <el-dialog
            title="商品库存信息"
            center
            :visible.sync="viewInventoryDialogStatus"
            width="70%"
        >
            <el-button
                size="mini"
                type="danger"
                v-if="isWhite == 0"
                style="position: absolute; top: 18px; right: 330px"
                @click="joinOrMoveWhitelistOper(1)"
            >
                加入白名单</el-button
            >
            <el-button
                size="mini"
                type="danger"
                v-if="isWhite == 1"
                style="position: absolute; top: 18px; right: 330px"
                @click="joinOrMoveWhitelistOper(0)"
            >
                移出白名单</el-button
            >
             <el-button
                size="mini"
                type="info"
                style="position: absolute; top: 18px; right: 230px"
                @click="inOutRecordDialogVisible = true"
                >出入口记录</el-button
            >
            <el-button
                size="mini"
                type="danger"
                style="position: absolute; top: 18px; right: 140px"
                @click="transferGDLogDialogVisible = true"
                >调拨记录</el-button
            >
           
            <el-button
                size="mini"
                type="warning"
                style="position: absolute; top: 20px; right: 50px"
                @click="transferGDDialogVisible = true"
                >良次转换</el-button
            >
            <el-collapse v-model="activeNames">
                <el-collapse-item title="库存信息" :name="1">
                    <el-table
                        class="stock"
                        :data="flattenedData"
                        border
                        :row-class-name="tableRowClassName"
                        :span-method="spanMethod"
                        highlight-current-row
                        show-summary
                        :summary-method="getSummaries"
                        style="width: 100%"
                    >
                        <el-table-column label="虚拟仓" align="center">
                            <template slot-scope="row">
                                {{ row.row.fictitious_name }}
                                <div
                                    v-if="
                                        row.row.fictitious_name === '南通次品仓'
                                    "
                                >
                                    <el-button
                                        @click="viewDefectiveImg(row.row)"
                                        size="mini"
                                        >查看次品图</el-button
                                    >
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            prop="goods_count"
                            label="可用数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="corp_name"
                            label="主体"
                            align="center"
                        >
                            <template slot-scope="row">
                                {{
                                    row.row.corp_name === "闪购不可售"
                                        ? row.row.corp_name
                                        : ""
                                }}
                            </template>
                        </el-table-column>
                    </el-table>
                </el-collapse-item>
                <el-collapse-item title="库位信息" :name="2">
                    <el-table
                        :data="viewInventoryData.location"
                        border
                        show-summary
                        stripe
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="location_code"
                            label="库位"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="corp_name"
                            label="货位主体"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="actual_count"
                            label="实际库存"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="available_count"
                            label="可用库存"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="is_lock"
                            label="锁库"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="status"
                            label="状态"
                            align="center"
                        >
                        </el-table-column>
                    </el-table>
                </el-collapse-item>
            </el-collapse>
        </el-dialog>
        <TransferGDLogDialog
            :visible.sync="transferGDLogDialogVisible"
            :barCode="currentBarCode"
        />
        <TransferGDDialog
            :visible.sync="transferGDDialogVisible"
            :barCode="currentBarCode"
        />
        <InOutRecordDialog
            :visible.sync="inOutRecordDialogVisible"
            :barCode="currentBarCode"
        />
        <el-dialog
            title="新增商品"
            top="6vh"
            center
            :close-on-click-modal="false"
            :visible.sync="addGoodsStatus"
            width="70%"
        >
            <add @closeDialog="closeDialog" v-if="addGoodsStatus"></add>
        </el-dialog>
        <el-dialog
            title="编辑商品"
            top="6vh"
            @close="getGoodsList"
            center
            :close-on-click-modal="false"
            :visible.sync="editGoodsStatus"
            width="70%"
        >
            <edit
                v-if="editGoodsStatus"
                @closeDialog="closeDialog"
                :editGoodsData="editGoodsData"
            ></edit>
        </el-dialog>
        <el-dialog
            title="复制商品"
            center
            top="6vh"
            @close="getGoodsList"
            :close-on-click-modal="false"
            :visible.sync="cloneGoodsStatus"
            width="70%"
        >
            <clone
                v-if="cloneGoodsStatus"
                @closeDialog="closeDialog"
                :cloneGoodsData="cloneGoodsData"
            ></clone>
        </el-dialog>
    </div>
</template>
<script>
import edit from "./edit.vue";
import add from "./add.vue";
import clone from "./easyAdd.vue";
import TransferGDDialog from "@/components/goods/TransferGDDialog";
import TransferGDLogDialog from "@/components/goods/TransferGDLogDialog";
import InOutRecordDialog from "@/components/goods/InOutRecordDialog";
export default {
    components: {
        add,
        edit,
        TransferGDLogDialog,
        clone,
        TransferGDDialog,
        InOutRecordDialog,
    },
    computed: {
        flattenedData() {
            const colorList = ["row-color-1", "row-color-2"];
            let colorIndex = 0;

            // 分配颜色
            this.viewInventoryData.fic.forEach((data) => {
                if (!this.warehouseColors[data.fictitious_name]) {
                    console.error(data);
                    this.warehouseColors[data.fictitious_name] =
                        colorList[colorIndex % colorList.length];
                    colorIndex++;
                }
            });
            return this.viewInventoryData.fic.flatMap((warehouse) => {
                return warehouse.inventory.map((entity) => ({
                    fictitious_name: warehouse.fictitious_name,
                    corp_name: entity.corp_name,
                    goods_count: entity.goods_count,
                }));
            });
        },
    },
    data() {
        return {
            warehouseColors: {}, // 用于存储每个warehouse的颜色

            remakeTableData: [],
            cloneGoodsData: {},
            cloneGoodsStatus: false,
            locationActionStatus: false,
            editGoodsData: {},
            editGoodsStatus: false,
            viewDefectiveImgList: [],
            addGoodsStatus: false,
            addRemakeMessage: "",
            viewRemakeStatus: false,
            activeNames: [1, 2],
            viewInventoryData: {
                fic: [],
                location: [],
            },
            viewInventoryDialogStatus: false,

            viewImagesStatus: false,
            srcList: [],
            options: [
                {
                    value: "bar_code",
                    label: "条码",
                },
                {
                    value: "short_code",
                    label: "简码",
                },
                {
                    value: "goods_name",
                    label: "商品名",
                },
            ],
            value: "short_code",
            viewDefectiveImgBarCode: "",
            keyword: "",
            is_stock: 0,
            type_id: "",
            sub_type_id: "",
            firstCategoryList: [],
            secondCategoryList: [],
            page: 1,
            // eslint-disable-next-line camelcase
            bar_code: "",
            dialogStatus: false,
            limit: 10,
            bar_code_remake: "",
            count: 1,
            total: 0,
            tableData: [],
            innerVisible: false,
            viewDefectiveImgDialogStatus: false,
            formLabelWidth: "120px",
            transferGDLogDialogVisible: false,
            transferGDDialogVisible: false,
            inOutRecordDialogVisible: false,
            currentBarCode: "",
            isWhite: 0,
        };
    },
    mounted() {
        this.getGoodsList();
        this.$request.goods.searchTransferGDList({ page: 1, limit: 10 });
        this.loadFirstCategoryList();
    },
    filters: {
        fileNameFormat(val) {
            return val.substring(val.lastIndexOf("/") + 1);
        },
    },
    methods: {
        spanMethod({ row, column, rowIndex, columnIndex }) {
            console.log(row, column);
            if (columnIndex === 0) {
                // 合并虚拟仓
                const currentWarehouse = row.fictitious_name;
                const prevWarehouse =
                    rowIndex > 0
                        ? this.flattenedData[rowIndex - 1].fictitious_name
                        : null;
                let rowspan = 1;
                if (currentWarehouse !== prevWarehouse) {
                    for (
                        let i = rowIndex + 1;
                        i < this.flattenedData.length;
                        i++
                    ) {
                        if (
                            this.flattenedData[i].fictitious_name ===
                            currentWarehouse
                        ) {
                            rowspan++;
                        } else {
                            break;
                        }
                    }
                } else {
                    return { rowspan: 0, colspan: 0 };
                }
                return { rowspan, colspan: 1 };
            }
        },
        joinOrMoveWhitelistOper(option) {
            this.$request.goods
                .jionWhitelistOper({
                    bar_code: this.currentBarCode,
                    oper_type: option,
                })
                .then((res) => {
                    if (res.data.errorCode == 0) {
                        console.log(res.data.data);
                        this.isWhite = option;
                        this.$message.success("操作成功");
                        this.getGoodsList();
                    }
                });
        },
        getSummaries({ columns, data }) {
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "合计";
                    return;
                }
                const values = data.map((item) => item[column.property]);
                if (!values.every((value) => isNaN(value))) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + curr;
                        } else {
                            return prev;
                        }
                    }, 0);
                } else {
                    sums[index] = "";
                }
            });
            return sums;
        },
        // tableRowClassName({ row, rowIndex }) {
        //     console.log(rowIndex, row);
        //     // 根据虚拟仓的名称来区分颜色
        //     // if (row.warehouse.includes("佰醇云酒（南通闪购仓）")) {
        //     //     return "row-color-1";
        //     // } else if (row.warehouse.includes("alvin南通仓")) {
        //     //     return "row-color-2";
        //     // } else if (row.warehouse.includes("松鸢酒业南通仓")) {
        //     //     return "row-color-3";
        //     // }
        //     return "";
        // },

        closeDialog() {
            this.cloneGoodsStatus = false;
            this.addGoodsStatus = false;
            this.editGoodsStatus = false;
            this.getGoodsList();
        },
        viewInventory(row) {
            this.viewDefectiveImgBarCode = "";
            this.currentBarCode = row.bar_code;
            this.isWhite = row.is_whitelist;
            let data = {
                bar_code: row.bar_code,
            };
            this.$request.goods.viewInventory(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res.data.data);
                    if (
                        res.data.data.fic.length == 0 &&
                        res.data.data.location.length == 0
                    ) {
                        this.$message.warning("暂无商品库存信息");
                    } else {
                        this.viewDefectiveImgBarCode = row.bar_code;
                        this.viewInventoryDialogStatus = true;
                        this.viewInventoryData = res.data.data;
                        this.viewInventoryData.location =
                            this.viewInventoryData.location.filter(
                                (item) =>
                                    item.available_count !== 0 ||
                                    item.actual_count !== 0
                            );
                    }
                }
            });
        },
        search() {
            this.page = 1;
            this.getGoodsList();
        },

        add() {
            this.addGoodsStatus = true;
        },
        clone(row) {
            this.cloneGoodsData = row;
            this.cloneGoodsStatus = true;
        },
        locationAction(type) {
            if (type == "hidden") {
                this.locationActionStatus = false;
            } else {
                this.locationActionStatus = true;
            }
        },
        edit(row) {
            this.editGoodsStatus = true;
            this.editGoodsData = row;
        },
        openRemake(row) {
            this.remakeTableData = [];
            this.bar_code_remake = row.bar_code;
            this.goodsCommentList();
        },
        async goodsCommentList() {
            let data = {
                page: 1,
                limit: 999,
                bar_code: this.bar_code_remake,
            };
            const res = await this.$request.goods.goodsCommentList(data);
            if (res.data.errorCode == 0) {
                this.viewRemakeStatus = true;
                this.remakeTableData = res.data.data.list;
            }
        },

        addRemark() {
            this.innerVisible = true;
        },
        async addRemakeMessageSubmit() {
            if (!this.addRemakeMessage) {
                this.$message.warning("请输入商品备注信息");
                return;
            }
            let data = {
                bar_code: this.bar_code_remake,
                comment: this.addRemakeMessage,
            };
            const res = await this.$request.goods.addGoodsComment(data);
            if (res.data.errorCode == 0) {
                this.goodsCommentList();
                this.addRemakeMessage = "";
                this.innerVisible = false;
                this.$message.success("操作成功");
            }
        },
        getGoodsList() {
            let data = {
                page: this.page,
                limit: this.limit,
                is_stock: this.is_stock,
            };
            if (this.type_id) {
                data.type_id = this.type_id;
                if (this.sub_type_id) data.sub_type_id = this.sub_type_id;
            }
            if (this.value === "goods_name") {
                // eslint-disable-next-line camelcase
                data.goods_name = this.keyword.trim();
            } else if (this.value === "bar_code") {
                // eslint-disable-next-line camelcase
                data.bar_code = this.keyword.trim();
            } else if (this.value === "short_code") {
                // eslint-disable-next-line camelcase
                data.short_code = this.keyword.trim();
            }
            this.$request.goods.getGoodsList(data).then((res) => {
                console.log(res.data);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        printBarCodeDialogOpen(row) {
            this.dialogStatus = true;
            console.log(row);
            // eslint-disable-next-line camelcase
            this.bar_code = row.bar_code;
        },
        async viewDefectiveImg(row) {
            console.log(row);
            const data = {
                bar_code: this.viewDefectiveImgBarCode,
            };
            const res = await this.$request.goods.getDefectueousPic(data);
            if (res.data.errorCode == 0) {
                this.viewDefectiveImgList = res.data.data.list;
                this.viewDefectiveImgDialogStatus = true;
                // this.viewImagesStatus = true;

                // this.srcList = [];
                // res.data.data.list.map((item) => {
                //     item.img.map((child) => {
                //         this.srcList.push(child);
                //     });
                // });
            }
        },
        printBarCode() {
            let data = {
                type: "goods",
                data: JSON.stringify([
                    {
                        // eslint-disable-next-line camelcase
                        bar_code: this.bar_code,
                        count: this.count,
                    },
                ]),
            };
            this.$request.order
                .printBarCode(data)
                .then((res) => {
                    console.log(res);
                    if (res.data.errorCode == 0) {
                        this.dialogStatus = false;
                    }
                })
                .catch(() => {
                    this.$message.error("服务器连接异常");
                });
        },
        viewImages(row) {
            this.srcList = [];
            console.log(row.back_img);
            if (row.positive_img) {
                this.srcList.push(row.aliurl + row.positive_img);
            }
            if (row.back_img) {
                this.srcList.push(row.aliurl + row.back_img);
            }
            if (row.en_back_img) {
                this.srcList.push(row.aliurl + row.en_back_img);
            }
            if (row.package_img) {
                this.srcList.push(row.aliurl + row.package_img);
            }

            this.viewImagesStatus = true;
        },
        isHavaImges(row) {
            let status = false;
            if (row.back_img) {
                status = true;
            }
            if (row.en_back_img) {
                status = true;
            }
            if (row.package_img) {
                status = true;
            }
            if (row.positive_img) {
                status = true;
            }
            return status;
        },
        propertyFormat(type, val) {
            if (type === "liquid") {
                if (val) {
                    return "固体";
                } else {
                    return "液体";
                }
            } else {
                if (val) {
                    return "是";
                } else {
                    return "否";
                }
            }
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
            this.page = 1;
            this.limit = val;
            this.getGoodsList();
            console.log(`每页 ${val} 条`);
        },
        tableRowClassName({ row }) {
            console.log(row, this.warehouseColors);
            return this.warehouseColors[row.fictitious_name] || "";
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getGoodsList();
        },
        loadFirstCategoryList() {
            this.$request.goods.getGoodstypeSelect().then((res) => {
                if (res.data.errorCode == 0) {
                    this.firstCategoryList = res.data.data;
                }
            });
        },
        loadSecondCategoryList() {
            this.$request.goods
                .getGoodsSecondCategoryList({ pid: this.type_id })
                .then((res) => {
                    if (res.data.errorCode == 0) {
                        this.secondCategoryList = res.data.data;
                    }
                });
        },
        onTypeIdChange() {
            if (this.type_id) {
                this.sub_type_id = "";
                this.loadSecondCategoryList();
            } else {
                this.sub_type_id = "";
                this.secondCategoryList = [];
            }
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.hidden-location {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.flex-c {
    display: flex;
    justify-content: center;
    align-items: center;
}
// .product-imgs {
//     display: flex;
//     & > div {
//         text-align: center;
//         margin-right: 10px;
//     }
// }

/deep/ .el-table th {
    font-weight: bold !important;
    text-align: center !important;
    color: #fff;
    font-size: 15px;
    background-color: #4dd0e1; /* 表头背景色 */
}
/deep/ .el-table__header-wrapper {
    background-color: #f9f9f9 !important;
}
.area-layout {
    .collapse-tags {
        & > .el-tag {
            margin-right: 10px;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 500;
        }
    }
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
/deep/ .el-image > img {
    object-fit: contain !important;
    border-radius: 6px;
    // max-width: 300px;
    max-height: 300px;
}
/deep/ .el-carousel__item {
    text-align: center !important;
}
/deep/ .row-color-1 {
    background-color: #e0f7fa !important;
}
/deep/ .row-color-2 {
    background-color: #b2ebf2 !important;
}
</style>
