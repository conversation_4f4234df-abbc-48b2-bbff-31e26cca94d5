<template>
    <div class="goods-add-layout">
        <el-steps :active="step" align-center finish-status="success">
            <el-step title="选择商品类别"></el-step>
            <el-step title="填写基本信息资料"></el-step>
            <el-step title="上传图片及附件"></el-step>
        </el-steps>
        <div class="main-form step1" v-if="step == 0">
            <el-radio-group v-model="typeRadio">
                <el-radio
                    v-for="(item, index) in types"
                    :key="index"
                    :label="item.id"
                    >{{ item.name }}</el-radio
                >
            </el-radio-group>
            <div style="margin-top:40px">
                <el-button @click="getPropertyList()" type="primary"
                    >下一步</el-button
                >
            </div>
        </div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            :inline="true"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
        >
            <div class="main-form" v-if="step == 1">
                <el-form-item
                    label="中文品名"
                    v-if="rules.goods_name"
                    :label-width="labelWidth"
                    prop="goods_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.goods_name"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="英文品名"
                    v-if="rules.en_goods_name"
                    :label-width="labelWidth"
                    prop="en_goods_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.en_goods_name"
                    ></el-input>
                </el-form-item>

                <el-form-item
                    label="简码"
                    v-if="rules.short_code"
                    :label-width="labelWidth"
                    prop="short_code"
                >
                    <el-input
                        class="w-280"
                        :onkeyup="
                            (ruleForm.short_code = ruleForm.short_code.replace(
                                /\s+/g,
                                ''
                            ))
                        "
                        v-model.trim="ruleForm.short_code"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="采摘年份"
                    v-if="rules.goods_years"
                    :label-width="labelWidth"
                    prop="goods_years"
                >
                    <el-date-picker
                        value-format="yyyy"
                        v-model="ruleForm.goods_years"
                        type="year"
                        placeholder="不填默认为NV"
                    >
                    </el-date-picker>
                </el-form-item>

                <el-form-item
                    label="商品类型"
                    v-if="rules.goods_type1"
                    :label-width="labelWidth"
                    prop="goods_type1"
                >
                    <el-select
                        v-model="ruleForm.goods_type1"
                        filterable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in typeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="商品单位"
                    v-if="rules.goods_unit"
                    :label-width="labelWidth"
                    prop="goods_unit"
                >
                    <el-select
                        v-model="ruleForm.goods_unit"
                        filterable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in unitList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="商品形态"
                    v-if="rules.goods_form"
                    :label-width="labelWidth"
                    prop="goods_form"
                >
                    <el-radio-group v-model="ruleForm.goods_form">
                        <el-radio
                            v-for="(item, index) in goods_form"
                            :key="index"
                            :label="item.value"
                            >{{ item.name }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="规格"
                    v-if="rules.capacity"
                    :label-width="labelWidth"
                    prop="capacity"
                >
                    <el-input-number
                        v-model="ruleForm.capacity"
                        :min="0"
                        :max="10000000"
                        :precision="2"
                        label="请填写规格"
                    ></el-input-number>
                    {{ ruleForm.goods_form | capacityType }}
                </el-form-item>
                <el-form-item
                    v-if="rules.bar_code"
                    label="条码"
                    :label-width="labelWidth"
                    prop="bar_code"
                >
                    <el-input
                        class="w-280"
                        :onkeyup="
                            (ruleForm.bar_code = ruleForm.bar_code.replace(
                                /\s+/g,
                                ''
                            ))
                        "
                        v-model.trim="ruleForm.bar_code"
                    ></el-input>
                    <el-button
                        type="primary"
                        style="margin-left:10px"
                        size="mini"
                        @click="crateBarCodePrint"
                        >生成条码</el-button
                    >
                </el-form-item>
                <el-form-item
                    label="成本"
                    v-if="rules.price"
                    :label-width="labelWidth"
                    prop="price"
                >
                    <el-input class="w-280" v-model="ruleForm.price"></el-input>
                </el-form-item>
                <el-form-item
                    label="国家"
                    v-if="rules.country"
                    :label-width="labelWidth"
                    prop="country"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.country"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-if="rules.producing_area"
                    label="产区"
                    :label-width="labelWidth"
                    prop="producing_area"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.producing_area"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="酒庄"
                    v-if="rules.chateau"
                    :label-width="labelWidth"
                    prop="chateau"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.chateau"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-if="rules.shelf_life"
                    label="保质期"
                    :label-width="labelWidth"
                    prop="shelf_life"
                >
                    <el-input-number
                        v-model="ruleForm.shelf_life"
                        :min="0"
                        :precision="0"
                        :max="100000"
                        label="保质期（天）"
                    ></el-input-number>
                    天
                </el-form-item>
                <el-form-item
                    label="灌装日期"
                    v-if="rules.produce_date"
                    :label-width="labelWidth"
                    prop="produce_date"
                >
                    <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="ruleForm.produce_date"
                        type="date"
                        placeholder="选择灌装日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item
                    label="重量"
                    v-if="rules.weight"
                    :label-width="labelWidth"
                    prop="weight"
                >
                    <el-input-number
                        v-model="ruleForm.weight"
                        :min="0"
                        :max="100000"
                        :precision="3"
                        label="重量（kg）"
                    ></el-input-number>
                    千克
                </el-form-item>
                <el-form-item
                    v-if="rules.box_gauge"
                    label="箱规"
                    :label-width="labelWidth"
                    prop="box_gauge"
                >
                    <el-input-number
                        v-model="ruleForm.box_gauge"
                        :min="0"
                        :max="100"
                        label="箱规"
                        :precision="0"
                    ></el-input-number>
                    瓶
                </el-form-item>
                <!-- // -->

                <el-form-item
                    v-if="rules.is_giveaway"
                    label="是否赠品"
                    :label-width="labelWidth"
                    prop="is_giveaway"
                >
                    <el-switch
                        v-model="ruleForm.is_giveaway"
                        active-color="#13ce66"
                        inactive-color="#999"
                    >
                    </el-switch>
                </el-form-item>
                <el-form-item
                    v-if="rules.addition"
                    label="指令商品"
                    :label-width="labelWidth"
                    prop="addition"
                >
                    <el-switch
                        v-model="ruleForm.addition"
                        active-color="#13ce66"
                        inactive-color="#999"
                        :active-value="1"
                        :inactive-value="0"
                    >
                    </el-switch>
                </el-form-item>

                <el-form-item
                    label="长"
                    v-if="rules.length"
                    :label-width="labelWidth"
                    prop="length"
                >
                    <el-input-number
                        v-model="ruleForm.length"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="长"
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="宽"
                    v-if="rules.width"
                    :label-width="labelWidth"
                    prop="width"
                >
                    <el-input-number
                        v-model="ruleForm.width"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="宽"
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="高"
                    v-if="rules.high"
                    :label-width="labelWidth"
                    prop="high"
                >
                    <el-input-number
                        v-model="ruleForm.high"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="高"
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="是否同步"
                    v-if="rules.issync"
                    :label-width="labelWidth"
                    prop="issync"
                >
                    <el-select
                        v-model="ruleForm.issync"
                        filterable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in issyncList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </div>
            <div v-show="step == 2" class="main-form">
                <el-form-item
                    label="正标图"
                    v-if="rules.positive_img"
                    :label-width="labelWidth"
                    prop="positive_img"
                >
                    <el-upload
                        class="avatar-uploader"
                        action="/admin/outbound/upload/image"
                        :data="uploadData"
                        :limit="99"
                        :headers="headers"
                        :show-file-list="false"
                        :on-success="onSuccess"
                        :before-upload="beforeAvatarUpload"
                    >
                        <img
                            v-if="viewImg.positive_img"
                            :src="viewImg.positive_img"
                            class="avatar"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item
                    v-if="rules.back_img"
                    label="中文背标图"
                    :label-width="labelWidth"
                    prop="back_img"
                >
                    <el-upload
                        class="avatar-uploader"
                        action="/admin/outbound/upload/image"
                        :data="uploadData"
                        :limit="99"
                        :headers="headers"
                        :show-file-list="false"
                        :on-success="onSuccessBack"
                        :before-upload="beforeAvatarUpload"
                    >
                        <!-- :on-success="onSuccess" -->

                        <img
                            v-if="viewImg.back_img"
                            :src="viewImg.back_img"
                            class="avatar"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- <el-input v-model="ruleForm.image"></el-input> -->
                </el-form-item>
                <el-form-item
                    v-if="rules.en_back_img"
                    label="英文背标图"
                    :label-width="labelWidth"
                    prop="en_back_img"
                >
                    <el-upload
                        class="avatar-uploader"
                        action="/admin/outbound/upload/image"
                        :data="uploadData"
                        :headers="headers"
                        :limit="99"
                        :show-file-list="false"
                        :on-success="onSuccessEnBack"
                        :before-upload="beforeAvatarUpload"
                    >
                        <!-- :on-success="onSuccess" -->

                        <img
                            v-if="viewImg.en_back_img"
                            :src="viewImg.en_back_img"
                            class="avatar"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- <el-input v-model="ruleForm.image"></el-input> -->
                </el-form-item>
                <el-form-item
                    v-if="rules.package_img"
                    label="包装图"
                    :label-width="labelWidth"
                    prop="package_img"
                >
                    <el-upload
                        class="avatar-uploader"
                        action="/admin/outbound/upload/image"
                        :data="uploadData"
                        :limit="99"
                        :headers="headers"
                        :show-file-list="false"
                        :on-success="onSuccessPackage"
                        :before-upload="beforeAvatarUpload"
                    >
                        <!-- :on-success="onSuccess" -->

                        <img
                            v-if="viewImg.package_img"
                            :src="viewImg.package_img"
                            class="avatar"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <!-- <el-input v-model="ruleForm.image"></el-input> -->
                </el-form-item>

                <el-form-item
                    label="附件"
                    v-if="rules.annex"
                    :label-width="labelWidth"
                    prop="annex"
                >
                    <el-upload
                        class="upload-demo"
                        action="/admin/outbound/upload/annex"
                        multiple
                        :data="uploadData"
                        :headers="headers"
                        :limit="99"
                        :on-remove="onRemoveAnnex"
                        :on-success="onSuccessAnnex"
                        :before-upload="beforeAvatarUploadAnnex"
                        :file-list="annexFileList"
                    >
                        <!-- :on-success="onSuccess" -->

                        <el-button size="small" type="primary"
                            >点击上传</el-button
                        >
                    </el-upload>
                    <!-- <el-input v-model="ruleForm.image"></el-input> -->
                </el-form-item>
            </div>
            <div style="text-align: center;">
                <div style="margin-top:40px" v-if="step == 1">
                    <el-button @click="nextStep('ruleForm')" type="primary"
                        >下一步</el-button
                    >
                </div>
                <div v-if="step == 2">
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >立即创建</el-button
                    ><el-button @click="step = 1">上一步</el-button>
                </div>
            </div>
        </el-form>
    </div>
</template>
<script>
import { Loading } from "element-ui";

// import RULESTYPE from "./rules";
import Cookies from "js-cookie";
export default {
    data() {
        return {
            typeRadio: "",
            types: [],
            step: 0,
            // eslint-disable-next-line camelcase
            goods_form: [],
            uploadData: {
                // eslint-disable-next-line camelcase
                file_key: "file"
            },
            unitList: [], // 商品单位
            headers: {
                warehousecheckval: Cookies.get("stock_id"),
                securitycheckval: Cookies.get("token")
            },
            dieUnitList: [
                {
                    id: "g",
                    name: "克"
                },
                {
                    id: "ml",
                    name: "毫升"
                }
            ],
            dieUnitValue: "ml",
            typeList: [], // 商品类型
            issyncList: [], // 是否同步选项
            fileList: [],
            annexFileList: [],
            labelWidth: "140px",
            viewImg: {
                // eslint-disable-next-line camelcase
                en_back_img: "",
                // eslint-disable-next-line camelcase
                positive_img: "",
                // eslint-disable-next-line camelcase
                back_img: "",
                // eslint-disable-next-line camelcase
                package_img: ""
            },
            type1List: [],
            ruleForm: {
                addition: 0,
                price: "",
                // eslint-disable-next-line camelcase
                producing_area: "",
                chateau: "",
                country: "",
                issync: 1,
                // eslint-disable-next-line camelcase
                package_img: "",
                // eslint-disable-next-line camelcase
                back_img: "",
                // eslint-disable-next-line camelcase
                goods_type1: "",
                // eslint-disable-next-line camelcase
                // eslint-disable-next-line camelcase
                goods_unit: "",
                // eslint-disable-next-line camelcase
                goods_name: "",
                // eslint-disable-next-line camelcase
                en_back_img: "",
                // eslint-disable-next-line camelcase
                positive_img: "",
                // eslint-disable-next-line camelcase
                en_goods_name: "",
                // eslint-disable-next-line camelcase
                bar_code: "",
                // eslint-disable-next-line camelcase
                short_code: "",
                // eslint-disable-next-line camelcase
                goods_years: "",
                // eslint-disable-next-line camelcase
                goods_form: "",
                capacity: 750,
                // eslint-disable-next-line camelcase
                shelf_life: 3650,
                // eslint-disable-next-line camelcase
                produce_date: "",
                weight: 0.75,
                // eslint-disable-next-line camelcase
                box_gauge: 6,
                // eslint-disable-next-line camelcase
                is_giveaway: false,
                image: [],
                high: 0,
                width: 0,
                length: 0
            },
            rules: {}
        };
    },
    filters: {
        capacityType(val) {
            if (val) {
                return "千克";
            } else {
                return "毫升";
            }
        }
    },
    mounted() {
        this.getGoodstypeSelect();
    },
    methods: {
        getPropertyList() {
            // 获取商品类型字段列表
            let data = {
                type_id: this.typeRadio
            };
            this.$request.goods.getPropertyList(data).then(res => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.rules = res.data.data.list;
                    this.step = 1;
                    if (this.rules.goods_form) {
                        this.goods_form = this.rules.goods_form[0].select;
                        console.log("形态", this.rules.goods_form[0].select);
                        this.ruleForm.goods_form = this.goods_form[0].value;
                    }
                    if (this.rules.issync) {
                        this.issyncList = this.rules.issync[0].select;
                        console.log("是否同步", this.rules.issync[0].select);
                        this.ruleForm.issync = this.issyncList[1].value;
                    }
                    if (this.rules.goods_type1) {
                        this.typeList = this.rules.goods_type1[0].list;
                        console.log("类型", this.rules.goods_type1[0].list);
                        this.ruleForm.goods_type1 = this.typeList[0].id;
                    }
                    if (this.rules.goods_unit) {
                        this.unitList = this.rules.goods_unit[0].list;
                        this.ruleForm.goods_unit = this.unitList[0].id;

                        console.log("单位", this.rules.goods_unit[0].list);
                    }

                    for (let i in this.rules) {
                        delete this.rules[i][0].character_type;
                        delete this.rules[i][0].length;
                        delete this.rules[i][0].mutual_type;
                        delete this.rules[i][0].list;
                        delete this.rules[i][0].select;
                    }
                    console.log(this.rules);
                }
            });
        },

        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    // eslint-disable-next-line camelcase

                    if (this.ruleForm.is_giveaway) {
                        // eslint-disable-next-line camelcase
                        this.ruleForm.is_giveaway = 1;
                    } else {
                        // eslint-disable-next-line camelcase
                        this.ruleForm.is_giveaway = 0;
                    }
                    let capacity = "";
                    if (this.ruleForm.goods_form) {
                        capacity =
                            Math.floor(this.ruleForm.capacity * 100) / 100 +
                            "kg";
                    } else {
                        capacity =
                            Math.floor(this.ruleForm.capacity * 100) / 100 +
                            "ml";
                    }
                    console.log(capacity);
                    let goods_type1_code = "";
                    let goods_type1_name = "";
                    this.typeList.map(i => {
                        if (i.id == this.ruleForm.goods_type1) {
                            goods_type1_code = i.code;
                            goods_type1_name = i.name;
                        }
                    });
                    // eslint-disable-next-line camelcase
                    let goods_unit_name = "";
                    this.unitList.map(i => {
                        if (i.id == this.ruleForm.goods_unit) {
                            // eslint-disable-next-line camelcase
                            goods_unit_name = i.name;
                        }
                    });

                    let data = {
                        ...this.ruleForm,
                        image: this.fileList,
                        annex: this.annexFileList,
                        goods_unit_name,
                        goods_type: this.typeRadio,
                        goods_type1_code,
                        goods_type1_name,
                        capacity
                    };
                    this.$request.goods.createGoods(data).then(res => {
                        console.log(res);
                        if (res.data.errorCode == 0) {
                            // this.$router.push({ path: "/goods" });
                            this.$emit("closeDialog");
                            this.$message({
                                message: "新增商品成功",
                                type: "success"
                            });
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        getGoodstypeSelect() {
            // 获取商品主类型
            this.$request.goods.getGoodstypeSelect().then(res => {
                if (res.data.errorCode == 0) {
                    console.log(res.data.data);

                    this.types = res.data.data;
                    this.typeRadio = res.data.data[0].id;
                }
            });
        },
        nextStep(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.step = 2;
                }
            });
        },
        crateBarCodePrint() {
            let status = true;
            if (this.typeRadio === 1) {
                if (this.ruleForm.goods_type1 && this.ruleForm.capacity) {
                    console.log("通过");
                } else {
                    status = false;
                }
            }
            if (!status) {
                this.$message.warning("请正确填写商品类型、规格后再生成条码");
                return;
            }
            let goods_type1_code = "";
            this.typeList.map(i => {
                if (i.id == this.ruleForm.goods_type1) {
                    goods_type1_code = i.code;
                }
            });
            let data = {
                // eslint-disable-next-line camelcase
                goods_type: goods_type1_code,
                // eslint-disable-next-line camelcase
                goods_years: this.ruleForm.goods_years,
                capacity: this.ruleForm.capacity
            };
            this.$request.goods.crateGoodsBarCode(data).then(res => {
                console.log(res);

                if (res.data.errorCode == 0) {
                    // eslint-disable-next-line camelcase
                    this.ruleForm.bar_code = res.data.data.bar_code;
                }
            });
        },
        beforeUpload(file) {
            console.log(file.name);
            // eslint-disable-next-line camelcase
            // this.uploadData.file_key = file.name;
        },
        onSuccess(res, file) {
            console.log(file);
            if (res.errorCode == 0) {
                // eslint-disable-next-line camelcase
                this.viewImg.positive_img =
                    file.response.data.file[0].full_path;
                // eslint-disable-next-line camelcase
                this.ruleForm.positive_img = file.response.data.file[0].path;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        onSuccessPackage(res, file) {
            console.log(file);
            if (res.errorCode == 0) {
                // eslint-disable-next-line camelcase
                this.viewImg.package_img = file.response.data.file[0].full_path;
                // eslint-disable-next-line camelcase
                this.ruleForm.package_img = file.response.data.file[0].path;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        onSuccessEnBack(res, file) {
            if (res.errorCode == 0) {
                console.log(file);
                // eslint-disable-next-line camelcase
                this.viewImg.en_back_img = file.response.data.file[0].full_path;
                // eslint-disable-next-line camelcase
                this.ruleForm.en_back_img = file.response.data.file[0].path;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        onRemoveEnBack(file, fileList) {
            console.log(fileList);

            let arr = [];
            fileList.map(i => {
                console.log(i.response.data.file[0].path);
                arr.push(i.response.data.file[0].path);
            });
            this.fileList = arr;
        },
        beforeAvatarUpload(file) {
            const Size3M = file.size / 1024 / 1024 < 10;
            if (!Size3M) {
                this.$message.error("上传图片大小不能超过 10MB!");
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading"
                });
            }
            return Size3M;
        },
        beforeAvatarUploadAnnex(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传附件大小不能超过 10MB!");
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading"
                });
            }
            return isLt10M;
        },
        onSuccessBack(res, file) {
            console.log(file);
            if (res.errorCode == 0) {
                // eslint-disable-next-line camelcase
                this.viewImg.back_img = file.response.data.file[0].full_path;
                // eslint-disable-next-line camelcase
                this.ruleForm.back_img = file.response.data.file[0].path;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        onRemoveBack(file, fileList) {
            console.log(fileList);
            let arr = [];
            fileList.map(i => {
                console.log(i.response.data.file[0].path);
                arr.push(i.response.data.file[0].path);
            });
            this.fileList = arr;
        },
        onRemove(file, fileList) {
            console.log(fileList);
            let arr = [];
            fileList.map(i => {
                console.log(i.response.data.file[0].path);
                arr.push(i.response.data.file[0].path);
            });
            this.fileList = arr;
        },
        onSuccessAnnex(res, file, fileList) {
            console.log("os", res);
            if (res.errorCode == 0) {
                let arr = [];
                fileList.map(i => {
                    arr.push(i.response.data);
                });

                this.annexFileList = arr;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        onRemoveAnnex(file, fileList) {
            console.log(fileList);
            let arr = [];
            fileList.map(i => {
                arr.push(i.response.data);
            });
            this.annexFileList = arr;
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .el-form-item {
    width: 48%;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    border: 1px dashed #d9d9d9;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    object-fit: cover;
    width: 100%;
    display: block;
}
/deep/ .w-280 {
    width: 280px !important;
}
.box-card {
    width: 200px;
}
.main-form {
    margin-top: 20px;
}
.step1 {
    text-align: center;
}
</style>
