<template>
    <div class="pack-layout">
        <div class="pack-form">
            <el-input
                ref="inputContainer"
                class="input-300"
                v-model="container"
                :disabled="orderList.length !== 0"
                placeholder="请输入容器编码"
            ></el-input>
            <el-input
                :disabled="true"
                style="margin-left:10px"
                class="input-300"
                v-if="orderList.length"
                v-model="scanStr"
            ></el-input>
            <div>
                <span> 波次编号：{{ task_no | task_noFormat }}</span>
            </div>
            <div>
                <span>当前订单号：{{ orderNo | orderId_noFormat }}</span>
            </div>
            <div>
                <span>当前工作完成情况：{{ score }}单</span>
            </div>
            <el-checkbox border v-model="isAutoPrint">自动打印</el-checkbox>
        </div>
        <div class="pack-main">
            <div class="table">
                <div v-if="false">
                    <panel title="容器内复核订单">
                        <span style="font-size: 24px"> </span>
                        <el-table
                            :row-class-name="orderClassName"
                            :data="orderList"
                            height="200"
                            border
                            style="width: 100%"
                        >
                            <!-- 容器订单表 -->
                            <el-table-column
                                prop="ordersn"
                                align="center"
                                label="订单号"
                                width="240"
                            >
                                <template slot-scope="row">
                                    <span>{{ row.row.ordersn | wight }}</span>
                                    <span
                                        style="font-size:20px;font-weight:bold;margin-left:2px"
                                        >{{ row.row.ordersn | bold }}</span
                                    >
                                </template>
                            </el-table-column>

                            <el-table-column
                                prop="goods.length"
                                label="SKU数"
                                width="90"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="goods_nums"
                                align="center"
                                label="总数量"
                                width="80"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="orderno"
                                align="center"
                                width="160"
                                label="商家单号"
                            >
                            </el-table-column>
                            <el-table-column
                                label="特殊要求"
                                align="center"
                                width="100"
                            >
                                <template slot-scope="row">
                                    <div v-html="row.row.attach_info"></div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="状态"
                                width="80"
                                align="center"
                            >
                                <template slot-scope="row">
                                    {{ row.row.status | statusFormat }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="是否撤单"
                                width="95"
                                align="center"
                            >
                                <template slot-scope="row">
                                    {{
                                        row.row.is_cancel_order
                                            | is_cancel_orderFormat
                                    }}
                                </template>
                            </el-table-column>

                            <!-- <el-table-column
                                label="操作"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <el-link
                                        type="primary"
                                        v-if="
                                            row.row.status == 4 ||
                                                row.row.status == 3
                                        "
                                        @click="rollBack(row.row)"
                                        >重新复核</el-link
                                    >
                                </template>
                            </el-table-column> -->
                        </el-table>
                    </panel>
                </div>
                <div>
                    <panel title="订单内详情明细">
                        <span style="font-size: 24px"> </span>
                        <el-table
                            :row-class-name="detailsClassName"
                            :data="orderDetailList"
                            border
                            height="200"
                            style="width: 100%"
                        >
                            <!-- 订单详情表 -->
                            <el-table-column
                                align="center"
                                prop="bar_code"
                                label="条码"
                                width="170"
                            >
                                <template slot-scope="row">
                                    {{ row.row.bar_code | wight }}
                                    <span
                                        style="font-size:20px;font-weight:bold;margin-left:2px"
                                        >{{ row.row.bar_code | bold }}</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="center"
                                prop="short_code"
                                label="简码"
                                width="140"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="goods_name"
                                label="名称"
                                align="center"
                            >
                            </el-table-column>

                            <el-table-column
                                align="center"
                                prop="nums"
                                label="总数量"
                                width="80"
                            >
                                <template slot-scope="row">
                                    <span
                                        style="font-weight:bold;font-size:18px"
                                        >{{ row.row.nums }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="已复核数量"
                                width="120"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <span
                                        style="font-weight:bold;font-size:18px"
                                    >
                                        {{ row.row.review_nums }}</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="未复核数量"
                                width="120"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <span
                                        style="font-weight:bold;font-size:18px"
                                        >{{
                                            row.row.nums - row.row.review_nums
                                        }}</span
                                    >
                                </template>
                            </el-table-column>
                        </el-table>
                    </panel>
                </div>
                <div v-if="orderDetailList.length" class="panel-scorll-box">
                    <panel :title="'箱号：' + orderNo + '_' + logisticsId">
                        <span style="font-size: 24px"> </span>
                        <el-table
                            :data="logisticsTable"
                            border
                            style="width: 100%"
                        >
                            <!-- 订单详情表 -->
                            <el-table-column
                                align="center"
                                prop="bar_code"
                                label="条码"
                                width="170"
                            >
                            </el-table-column>
                            <el-table-column
                                align="center"
                                prop="short_code"
                                label="简码"
                                width="140"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="goods_name"
                                label="名称"
                                align="center"
                            >
                            </el-table-column>

                            <el-table-column
                                align="center"
                                prop="numsLogistics"
                                label="箱内复核数量"
                                width="140"
                            >
                            </el-table-column>
                        </el-table>
                    </panel>
                </div>
                <div v-if="boxList.length">
                    <panel title="装箱明细">
                        <el-table
                            :data="boxList"
                            height="200"
                            border
                            style="width: 100%"
                        >
                            <!-- 订单详情表 -->
                            <el-table-column type="expand">
                                <template slot-scope="props">
                                    <el-form
                                        label-position="left"
                                        inline
                                        class="demo-table-expand"
                                    >
                                        <el-form-item
                                            class="tags"
                                            :label="item.goods_name"
                                            v-for="(item, index) in props.row
                                                .data[0].print_data"
                                            :key="index"
                                        >
                                            <el-tag type="danger"
                                                >{{
                                                    item.numsLogistics
                                                }}（数量）</el-tag
                                            >
                                            <el-tag
                                                >{{
                                                    item.bar_code
                                                }}（条码）</el-tag
                                            ><el-tag
                                                >{{
                                                    item.short_code
                                                }}（简码）</el-tag
                                            >
                                        </el-form-item>
                                    </el-form>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="箱号">
                                <template slot-scope="row">
                                    {{ row.row.data[0].carton_no }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </panel>
                </div>
            </div>
            <div class="info" v-if="orderDetailListStatus">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>订单内须复核详情</span>
                    </div>
                    <div class="box-layout">
                        <div>
                            <div>商品名称</div>
                            <div class="goods_name">{{ info.goods_name }}</div>
                        </div>
                        <div>
                            <div>商品条码</div>
                            <div>{{ info.bar_code }}</div>
                        </div>
                        <div>
                            <div>商品简码</div>
                            <div>{{ info.short_code }}</div>
                        </div>
                        <div>
                            <div>复核数量</div>
                            <div>
                                {{ info.checkNumber
                                }}<el-button
                                    style="margin-left: 6px"
                                    size="mini"
                                    v-if="userInfo.allselect"
                                    id="checkAll"
                                    :disabled="!info.goods_name"
                                    @click="checkAll"
                                    >全部</el-button
                                >
                            </div>
                        </div>
                        <div>
                            <div>订单商品总数量</div>
                            <div>{{ info.nums }}</div>
                        </div>
                        <div>
                            <div>快递公司</div>
                            <div>{{ info.logistics_company }}</div>
                        </div>
                        <div>
                            <div>快递公司</div>
                            <div>
                                {{ info.is_topay == 1 ? "到付" : "寄付" }}
                            </div>
                        </div>

                        <div>
                            <div>货主名称</div>
                            <div>{{ info.receiver_name }}</div>
                        </div>
                        <div>
                            <div>包裹数量</div>
                            <div>{{ info.packNumber }}</div>
                        </div>
                        <div>
                            <div>特殊要求</div>
                            <div v-html="info.attach_info"></div>
                        </div>
                    </div>
                </el-card>
            </div>
        </div>
        <el-button-group>
            <el-button
                type="primary"
                id="close-finish"
                :disabled="logisticsTable.length == 0"
                @click="pushFinish(true)"
                >装箱完成</el-button
            >
            <el-button
                type="primary"
                id="reset-resetBox"
                :disabled="orderDetailList.length == 0"
                @click="rollBackOrder"
                >重新复核此订单</el-button
            >
            <el-button
                type="primary"
                id="reset-Box"
                @click="rollBackBox"
                :disabled="logisticsTable.length == 0"
                >重新装箱</el-button
            >
        </el-button-group>
        <el-button-group style="margin:0 20px">
            <el-button type="info" id="resetBoxBtn" @click="resetBox()"
                >打印装箱清单</el-button
            >
            <el-button type="info" id="resetOrderBtn" @click="resetOrder()"
                >打印订单清单</el-button
            >
        </el-button-group>

        <el-button-group>
            <el-button id="close-btn" type="danger" @click="closeOrderList"
                >关闭容器</el-button
            >
        </el-button-group>
        <div class="dialog">
            <el-dialog
                title="是否开始打印订单清单"
                :visible.sync="dialogVisible"
                width="20%"
                :close-on-click-modal="false"
                :show-close="false"
                :close-on-press-escape="false"
            >
                <div class="dialog-footer">
                    <el-button
                        type="primary"
                        id="dialogPrint"
                        @click="dialogPrint"
                    >
                        开始打印
                    </el-button>
                </div>
            </el-dialog>
        </div>
        <div class="dialog">
            <el-dialog
                title="选择需要重打清单的箱号"
                :visible.sync="resetBoxdialogStatus"
                width="40%"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <div>
                    <el-radio
                        v-for="(item, index) in resetBoxList"
                        :key="index"
                        v-model="resetCheckList"
                        :label="item"
                        >{{ item.data[0].carton_no }}</el-radio
                    >
                </div>
                <div class="dialog-footer">
                    <el-button
                        type="primary"
                        id="dialogPrint"
                        @click="resetBoxPrint"
                    >
                        开始打印
                    </el-button>
                </div>
            </el-dialog>
        </div>
        <div class="batchdialog">
            <el-dialog
                title="批量复核"
                :visible.sync="batchCheckDialog"
                width="30%"
                :close-on-click-modal="false"
                :show-close="false"
                :close-on-press-escape="false"
            >
                <span>
                    发现该容器订单中，发现多个与目前订单相同，是否确认一次性批量复核？
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="cancelBatch">不用了</el-button>
                    <el-button type="primary" @click="batchCheck"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
// import axios from "axios";
import { mapState } from "vuex";
import Panel from "../../plugins/panel/Panel.vue";
// import PageOptions from "@/config/PageOptions.vue";
export default {
    components: {
        Panel
    },
    data() {
        return {
            boxList: [],
            isAutoPrint: false, //是否自动打印
            resetBoxdialogStatus: false,
            logisticsTable: [], // 物流箱子复核数据
            batchCheckOrderId: [], // 批量复核的订单队列将要给打印队列的
            IsOpenBatch: false, // 批量复核的开关
            reduceLoadingStatus: false, //减少状态
            batchCheckDialog: false,
            score: 0,
            resetCheckList: [],
            resetBoxList: [],
            resetOrderList: [],
            logisticsId: 0,
            orderNo: "", //订单编号
            containerBind: true,
            // eslint-disable-next-line camelcase
            arr_groups: {},
            // eslint-disable-next-line camelcase
            arr_groups_check: [],
            dialogVisible: false,
            orderId: "",
            id: "", //波次id
            info: {
                is_topay: "",
                orderno: "",
                checkNumber: 1,
                receiver_phone: "",
                // eslint-disable-next-line camelcase
                attach_info: "",
                address: "",
                nums: 0,
                // eslint-disable-next-line camelcase
                short_code: "",
                // eslint-disable-next-line camelcase
                bar_code: "",
                // eslint-disable-next-line camelcase
                receiver_name: "",
                packNumber: 1,
                // eslint-disable-next-line camelcase
                logistics_company: ""
            },
            // eslint-disable-next-line camelcase
            task_no: "", // 波次编号
            orderList: [], // 订单列表
            orderIdList: [],
            orderDetailList: [], // 订单详情列表
            container: "", //容器编码input value
            orderListStatus: false, // 订单列表信息状态
            orderDetailListStatus: false, // 订单详情列表信息状态
            scanStr: "" //扫码枪输入的字符串
        };
    },
    watch: {
        orderDetailList(val) {
            if (val.length == 0) {
                this.orderDetailListStatus = false;
            } else {
                this.orderDetailListStatus = true;
            }
        },
        orderList(val) {
            if (val.length == 0) {
                this.$refs.inputContainer.focus();
                this.orderListStatus = false;
            } else {
                this.orderListStatus = true;
            }
        }
    },
    beforeDestroy() {
        this.unBindKey();
    },
    mounted() {
        this.$refs.inputContainer.focus();
        this.$request.order.getSituationCount().then(res => {
            if (res.data.errorCode == 0) {
                this.score = res.data.data.order_count;
            }
        });
        this.bindKey();
    },
    computed: {
        ...mapState(["userInfo"])
    },

    filters: {
        wight(val) {
            if (val.length > 4) {
                return val.substring(val.length - 4, 0);
            } else {
                return val;
            }
        },
        bold(val) {
            if (val.length > 4) {
                return val.substring(val.length - 4);
            } else {
                val;
            }
        },
        // eslint-disable-next-line camelcase
        task_noFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无波次编号";
            }
        },
        // eslint-disable-next-line camelcase
        orderId_noFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无订单";
            }
        },
        // eslint-disable-next-line camelcase
        is_cancel_orderFormat(val) {
            if (val == 0) {
                return "正常";
            } else if (val == 1) {
                return "已撤单";
            } else if (val == 2) {
                return "已取消";
            } else {
                return "未知";
            }
        },
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未拣货";
                case 1:
                    return "拣货中";
                case 2:
                    return "已拣货";
                case 3:
                    return "复核中";
                case 4:
                    return "已复核";
            }
        }
    },
    methods: {
        deleteStr() {
            this.scanStr = this.scanStr.substring(0, this.scanStr.length - 1);
            console.log(this.scanStr);
        },
        rollBackBox() {
            document.getElementById("reset-Box").blur();
            //重新复核箱子
            console.log(this.logisticsTable);
            let table = [];

            this.logisticsTable.map(i => {
                let obj = {
                    // eslint-disable-next-line camelcase
                    bar_code: i.bar_code,
                    nums: i.numsLogistics
                };
                table.push(obj);
            });
            console.log(table);
            let data = {
                goods: table,
                // eslint-disable-next-line camelcase
                order_id: this.orderId
            };
            this.$request.order.resetOrder(data).then(res => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.info = {};
                    this.$message.success("重新装箱成功");
                    this.getOrderList();
                    this.logisticsTable = [];
                    console.log(data);
                    this.orderDetailList.map(i => {
                        // 将已装箱的商品数量 增加到未复核中
                        // eslint-disable-next-line camelcase
                        i.review_nums = i.review_nums - i.numsLogistics;
                        i.numsLogistics = 0;
                    });
                }
            });
        },
        rollBackOrder() {
            document.getElementById("reset-resetBox").blur();
            //重新复核订单
            let data = {
                // eslint-disable-next-line camelcase
                order_id: this.orderId
            };
            this.$request.order.resetOrder(data).then(res => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.$message.success("重新复核订单成功");
                    this.getOrderList();
                    localStorage.setItem("LogisticsArray", "[]");
                    this.orderDetailList = [];
                    this.orderId = "";
                    this.logisticsTable = [];
                }
            });
        },
        resetBox() {
            document.getElementById("resetBoxBtn").blur();
            this.resetBoxList = JSON.parse(
                localStorage.getItem("LogisticsArray")
            );
            console.log(this.resetBoxList);
            if (this.resetBoxList && this.resetBoxList.length > 0) {
                this.resetBoxdialogStatus = true;
            } else {
                this.$message.error("暂无可重打的装箱清单");
            }
        },
        resetBoxPrint() {
            this.unBindKey();
            const loading = this.$loading({
                lock: true,
                text: "正在准备打印中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
            });

            this.$request.order
                .print(this.resetCheckList)
                .then(res => {
                    console.log(res);
                    this.bindKey();
                    if (res.data.errorCode == 0) {
                        loading.close();
                        this.$message.success("打印装箱清单成功");
                    } else {
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印装箱清单失败，请重新打印</span>'
                        });
                    }
                })
                .catch(err => {
                    console.info("错了。。。");
                    this.$message({
                        dangerouslyUseHTMLString: true,
                        type: "error",
                        duration: 5000,
                        message:
                            '<span style="font-size:36px">打印装箱清单失败，请重新打印</span>'
                    });
                    console.log(err);
                    loading.close();
                    this.bindKey();
                });
        },
        resetOrder() {
            document.getElementById("resetOrderBtn").blur();
            if (
                JSON.parse(localStorage.getItem("LogisticsArray")) &&
                JSON.parse(localStorage.getItem("LogisticsArray")).length > 0
            ) {
                this.unBindKey();
                const loading = this.$loading({
                    lock: true,
                    text: "正在准备打印中...",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)"
                });
                let datas = {
                    // eslint-disable-next-line camelcase
                    express_type: "wl",
                    data: []
                };
                JSON.parse(localStorage.getItem("LogisticsArray")).map(i => {
                    console.log(i.data);
                    datas.data.push(i.data);
                });
                console.warn(JSON.stringify(datas));
                this.$request.order
                    .print(datas)
                    .then(res => {
                        console.log(res);
                        this.bindKey();
                        if (res.data.errorCode == 0) {
                            loading.close();
                            this.$message.success("打印订单清单成功");
                        } else {
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                type: "error",
                                duration: 5000,
                                message:
                                    '<span style="font-size:36px">打印订单清单失败，请重新打印</span>'
                            });
                        }
                    })
                    .catch(err => {
                        console.info("错了。。。");
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印订单清单失败，请重新打印</span>'
                        });
                        console.log(err);
                        loading.close();
                        this.bindKey();
                    });
            } else {
                this.$message.error("暂无可重打的订单清单");
            }
        },

        pushFinish(status) {
            document.getElementById("close-finish").blur();

            if (status) {
                if (this.logisticsTable.length === 0) {
                    this.$message.error("该箱内为空，无法完成该操作");
                    return;
                }
                console.log(status);

                let data = {
                    // eslint-disable-next-line camelcase
                    express_type: "wl",
                    data: [
                        {
                            is_topay: this.info.is_topay,
                            receiver_phone: this.info.receiver_phone,
                            orderno: this.info.orderno,
                            receiver_name: this.info.receiver_name,
                            // eslint-disable-next-line camelcase
                            address: this.info.address,
                            carton_no: this.orderNo + "_" + this.logisticsId,
                            // eslint-disable-next-line camelcase
                            print_data: this.logisticsTable
                        }
                    ]
                };
                let Logistics = JSON.parse(
                    localStorage.getItem("LogisticsArray")
                );
                console.log(Logistics);
                Logistics.push(data);
                localStorage.setItem(
                    "LogisticsArray",
                    JSON.stringify(Logistics)
                );
                this.boxList = JSON.parse(
                    localStorage.getItem("LogisticsArray")
                );

                if (this.isAutoPrint) {
                    const loading = this.$loading({
                        lock: true,
                        text: "正在准备打印中...",
                        spinner: "el-icon-loading",
                        background: "rgba(0, 0, 0, 0.7)"
                    });
                    // localStorage.setItem("Logistics", JSON.stringify(Logistics));
                    this.$request.order
                        .print(data)
                        .then(res => {
                            loading.close();
                            console.log(res);
                            this.logisticsTable = [];
                            this.logisticsId++;
                            this.orderDetailList.map(i => {
                                i.numsLogistics = 0;
                                console.log(i);
                            });
                            if (this.isCheckDetailFinish()) {
                                this.finishCheck(); // 这个时候就用调用复核完成的方法
                                // // 1、优先判断是否有重复订单
                                // let status = false;
                                // Object.keys(this.arr_groups).forEach(
                                //     key => {
                                //         this.arr_groups[key].map(
                                //             orderId => {
                                //                 console.log(orderId);
                                //                 if (
                                //                     this.orderId ===
                                //                         orderId &&
                                //                     this.arr_groups[key]
                                //                         .length > 1
                                //                 ) {
                                //                     this.batchCheckOrderId = this.arr_groups[
                                //                         key
                                //                     ];

                                //                     status = true;
                                //                 }
                                //             }
                                //         );
                                //     }
                                // );
                                // 批量复核开关
                                // if (status && this.IsOpenBatch) {
                                //     this.batchCheckDialog = true;
                                // } else {
                                //     this.finishCheck(); // 这个时候就用调用复核完成的方法
                                // }
                            }
                        })
                        .catch(err => {
                            this.logisticsTable = [];
                            this.logisticsId++;
                            this.orderDetailList.map(i => {
                                i.numsLogistics = 0;
                                console.log(i);
                            });
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                type: "error",
                                duration: 5000,
                                message:
                                    '<span style="font-size:36px">打印装箱清单失败，请重新打印</span>'
                            });
                            console.log(err);
                            loading.close();
                        });

                    console.log(data);
                } else {
                    this.logisticsTable = [];
                    this.logisticsId++;
                    this.orderDetailList.map(i => {
                        i.numsLogistics = 0;
                        console.log(i);
                    });
                }
            } else {
                this.logisticsTable = [];
                this.logisticsId++;
                this.orderDetailList.map(i => {
                    i.numsLogistics = 0;
                    console.log(i);
                });
            }
        },
        cancelBatch() {
            this.batchCheckDialog = false;
            this.finishCheck();
            Object.keys(this.arr_groups).forEach(key => {
                this.arr_groups[key].map((orderId, index) => {
                    console.log(orderId);
                    if (this.orderId === orderId) {
                        this.arr_groups[key].splice(index, 1);
                    }
                });
            });
        },
        batchCheck() {
            this.batchCheckDialog = false;
            this.orderIdList = this.batchCheckOrderId;
            this.batchCheckOrderId = [];
            if (this.isAutoPrint) {
                this.dialogVisible = true;
            }
        },

        detailsClassName({ row }) {
            console.error(row);
            if (row.nums === row.review_nums) {
                return "success-row";
            } else {
                return "warning-row";
            }
        },
        orderClassName({ row }) {
            console.error(row);
            if (row.status === 4) {
                return "success-row";
            } else {
                return "warning-row";
            }
        },
        groups() {
            console.log("xxxxxxx");
            let data = this.orderList;
            console.log(data);
            let filtersOrderDetailList = data.filter(
                item => item.is_cancel_order == 0 && item.status == 2
            ); // 筛选出已撤单的订单（不加入查询商品中）
            console.log(filtersOrderDetailList);
            filtersOrderDetailList.forEach(element => {
                let goods = element["goods"];
                let orderid = element["order_id"];
                let g = "";
                let skus = [];
                goods.forEach(item => {
                    skus.push(`${item["bar_code"]}_${item["nums"]}`);
                });
                skus.sort();

                skus.forEach(sku => {
                    g = g + sku;
                });
                if (this.arr_groups_check.indexOf(g) === -1) {
                    let d = this.arr_groups[g];
                    if (d === undefined) {
                        this.arr_groups[g] = [];
                        d = this.arr_groups[g];
                    }
                    d.push(orderid);
                } else {
                    let d = this.arr_groups[g];
                    d.push(orderid);
                }
                this.arr_groups_check.push(g);
            });

            console.log(this.arr_groups);
        },
        unBindKey() {
            document.onkeyup = undefined;
            document.onkeydown = undefined;
        },
        bindKey() {
            this.getKeyup();
            this.enterKeyup();
        },
        checkAll() {
            if (!this.userInfo.allselect) {
                this.$message.error("暂无权限");
                return;
            }
            document.getElementById("checkAll").blur();
            this.subtract(this.info.bar_code, true);
        },
        enter() {
            console.error(this.scanStr);
            if (this.batchCheckDialog) {
                this.batchCheck();
            } else if (this.dialogVisible) {
                if (this.scanStr.length == 0) {
                    this.dialogPrint();
                    return;
                } else {
                    this.scanStr = "";
                    return;
                }
            } else if (!this.orderListStatus) {
                console.log("容器");
                // 订单列表为空，就去请求订单列表
                // this.container = this.scanStr;
                this.getOrderList(true);
                this.scanStr = "";
            } else if (this.orderListStatus && !this.orderDetailListStatus) {
                console.log("执行减1并且检索");
                this.findRow(this.scanStr);
                this.pushFinish();
                this.logisticsId = 1;
                localStorage.setItem("LogisticsArray", "[]");
                // 订单列表不为空 并且 订单详情为空，就去获取订单详情以及查询扫码对应商品在那个订单的（从上倒下搜索），然后再复核数量+1
                this.scanStr = "";
            } else if (this.orderListStatus && this.orderDetailListStatus) {
                console.log("减少");
                this.subtract(this.scanStr);
                this.scanStr = "";
                // 订单列表不为空 并且 订单详情不为空，扫码对应商品核数量+1
            }
        },
        finishCheck() {
            // 订单详情中所有的数据 已经复核完成的时候调用
            console.log("finish");
            // this.unBindKey();
            this.$request.order.getSituationCount().then(res => {
                if (res.data.errorCode == 0) {
                    this.score = res.data.data.order_count;
                }
            });
            this.dialogVisible = true;
        },
        isCheckDetailFinish() {
            // 检查是否完成，在每次复核的时候调用
            let status = true;
            this.orderDetailList.map(item => {
                if (item.review_nums !== item.nums) {
                    status = false;
                }
            });
            return status;
            // console.log(status);
        },
        closeOrderList() {
            document.getElementById("close-btn").blur();

            this.$confirm("此操作将关闭该容器, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    // eslint-disable-next-line camelcase
                    this.task_no = "";
                    this.orderId = "";
                    this.orderList = [];
                    this.container = "";
                    this.orderDetailList = [];
                    this.logisticsTable = [];
                    this.$message({
                        type: "success",
                        message: "关闭成功"
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消"
                    });
                });
            // eslint-disable-next-line camelcase
        },
        // setExpressData(orderId, type) {
        //     // let data = [];
        // },
        dialogPrint() {
            console.error("xxlxlxlxlxlxlxlxlxlsdasdasdsadsadasdas");
            this.getOrderList(false);
            this.unBindKey();
            this.dialogVisible = false;
            document.getElementById("dialogPrint").blur();
            const loading = this.$loading({
                lock: true,
                text: "正在准备打印中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
            });
            let datas = {
                // eslint-disable-next-line camelcase
                express_type: "wl",
                data: []
            };
            JSON.parse(localStorage.getItem("LogisticsArray")).map(i => {
                console.log(i.data);
                datas.data.push(i.data);
            });

            this.$request.order
                .print(datas)
                .then(res => {
                    console.log(res);
                    this.bindKey();
                    this.orderDetailList = [];
                    this.logisticsTable = [];

                    if (res.data.errorCode == 0) {
                        loading.close();
                        this.$message.success("打印订单清单成功");
                        // localStorage.setItem("LogisticsArray", "[]");
                        let status = true;
                        this.orderList.map(i => {
                            if (i.status !== 4 || i.is_cancel_order !== 0) {
                                status = false;
                            }
                        });
                        console.log(status);
                        if (status) {
                            // eslint-disable-next-line camelcase
                            this.task_no = "";
                            this.orderList = [];
                        } else {
                            if (!this.containerBind) {
                                // eslint-disable-next-line camelcase
                                this.task_no = "";
                                this.orderList = [];
                            }
                        }
                    } else {
                        this.orderDetailList = [];
                        if (!this.containerBind) {
                            // eslint-disable-next-line camelcase
                            this.task_no = "";
                            this.orderList = [];
                        }
                        this.logisticsTable = [];
                        console.info("打印失败。。。");
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印订单清单失败，请重新打印</span>'
                        });
                    }
                })
                .catch(err => {
                    if (!this.containerBind) {
                        // eslint-disable-next-line camelcase
                        this.task_no = "";
                        this.orderList = [];
                    }
                    console.info("错了。。。");
                    this.$message({
                        dangerouslyUseHTMLString: true,
                        type: "error",
                        duration: 5000,
                        message:
                            '<span style="font-size:36px">打印订单清单失败，请重新打印</span>'
                    });
                    console.log(err);
                    loading.close();
                    this.bindKey();
                    this.orderDetailList = [];
                    this.logisticsTable = [];
                });
        },

        print(row) {
            document.getElementById("print").blur();
            // 单个重复打印
            console.log(row, "打印");
        },
        subtract(scanStr, isAll) {
            if (this.reduceLoadingStatus) {
                return;
            }
            console.log("减少");
            let status = true; // 是否找到

            this.orderDetailList.filter(item => {
                console.log(item);
                if (item.bar_code === scanStr.replace(/\s*/g, "")) {
                    // eslint-disable-next-line camelcase

                    if (item.review_nums < item.nums) {
                        console.warn(item);

                        status = false;
                        let logistics = 0;
                        if (isAll) {
                            // eslint-disable-next-line camelcase
                            logistics = item.nums - item.review_nums;
                        } else {
                            // eslint-disable-next-line camelcase
                            logistics = 1;
                        }
                        let data = {
                            id: this.id,
                            // eslint-disable-next-line camelcase
                            bar_code: item.bar_code,
                            // eslint-disable-next-line camelcase
                            order_id: this.orderId,
                            nums: isAll ? item.nums : item.review_nums + 1
                        };
                        // eslint-disable-next-line camelcase
                        this.info.bar_code = item.bar_code;
                        // eslint-disable-next-line camelcase
                        this.info.goods_name = item.goods_name;
                        // eslint-disable-next-line camelcase
                        this.info.short_code = item.short_code;
                        console.info(data);
                        this.reduceLoadingStatus = true;
                        this.$request.order.reviewoper(data).then(res => {
                            this.reduceLoadingStatus = false;
                            console.log(res);
                            if (res.data.errorCode == 0) {
                                if (isAll) {
                                    // eslint-disable-next-line camelcase
                                    item.review_nums = item.nums;
                                } else {
                                    item.review_nums++;
                                }
                                if (isAll) {
                                    console.warn(item);
                                    // eslint-disable-next-line camelcase
                                    item.numsLogistics =
                                        item.numsLogistics + logistics;
                                } else {
                                    item.numsLogistics =
                                        item.numsLogistics + logistics;
                                }

                                this.logisticsTable = this.orderDetailList.filter(
                                    i => i.numsLogistics > 0
                                );
                                this.info.checkNumber = item.review_nums;

                                this.getOrderList(false);
                                // 复核操作

                                // if (this.isCheckDetailFinish()) {
                                //     // 1、优先判断是否有重复订单
                                //     let status = false;
                                //     Object.keys(this.arr_groups).forEach(
                                //         key => {
                                //             this.arr_groups[key].map(
                                //                 orderId => {
                                //                     console.log(orderId);
                                //                     if (
                                //                         this.orderId ===
                                //                             orderId &&
                                //                         this.arr_groups[key]
                                //                             .length > 1
                                //                     ) {
                                //                         this.batchCheckOrderId = this.arr_groups[
                                //                             key
                                //                         ];

                                //                         status = true;
                                //                     }
                                //                 }
                                //             );
                                //         }
                                //     );
                                //     // 批量复核开关
                                //     if (status && this.IsOpenBatch) {
                                //         this.batchCheckDialog = true;
                                //     } else {
                                //         this.finishCheck(); // 这个时候就用调用复核完成的方法
                                //     }
                                // }
                            }
                        });
                    } else {
                        status = false;
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "success",
                            duration: 4000,
                            message:
                                '<span style="font-size:36px">该订单中，该商品类型已全部复核</span>'
                        });
                    }
                }

                return item;
                // return item.bar_code !== scanStr.replace(/\s*/g, "");
            });
            if (status) {
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">该待符合订单明细中，没有找到符合该订单的商品</span>'
                });
            }

            // console.log(result);
        },
        findRow(scanStr) {
            let status = true; // 是否找到
            let filtersOrderDetailList = this.orderList.filter(
                item =>
                    item.is_cancel_order == 0 &&
                    (item.status == 2 || item.status == 3)
            ); // 筛选出已撤单的订单（不加入查询商品中）
            console.log(filtersOrderDetailList);

            filtersOrderDetailList.map((map, index) => {
                if (status) {
                    map.goods.map(find => {
                        if (find.bar_code == scanStr.replace(/\s*/g, "")) {
                            console.log(index);
                            status = false;
                            // eslint-disable-next-line camelcase
                            this.orderIdList = [map.order_id];
                            this.orderId = map.order_id;
                            this.orderNo = map.ordersn;
                            // eslint-disable-next-line camelcase
                            this.info.attach_info = map.attach_info;
                            this.info.receiver_phone = map.receiver_phone;
                            // eslint-disable-next-line camelcase
                            this.info.address = map.address;
                            this.info.receiver_name = map.receiver_name;
                            this.info.is_topay = map.is_topay;
                            this.info.orderno = map.orderno;
                            this.info.nums = map.goods_nums;
                            // eslint-disable-next-line camelcase
                            this.info.logistics_company = map.logistics_company;

                            this.orderDetailList = map.goods;
                            // 找到了之后，再进行减少
                            this.subtract(this.scanStr);
                            return;
                        }
                    });
                    console.log(this.orderDetailList); //返回第一个符合的元素{name:'li',age:10}
                }
            });
            if (status) {
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">该容器订单中，没有找到符合该订单的商品</span>'
                });
            }

            // this.orderDetailList = [1, 1];
        },
        getKeyup() {
            // 获取键盘（扫码枪）输入
            document.onkeydown = even => {
                console.log(even);
                var key = event.which || event.keyCode || event.charCode;

                if (key == 13 || key == 8) {
                    return;
                } else {
                    this.scanStr = this.scanStr + even.key;
                }
                //获取键盘上的字母键盘的字母
            };
        },
        enterKeyup() {
            // 监听回车
            document.onkeyup = e => {
                // 兼容FF和IE和Opera
                var event = e || window.event;
                var key = event.which || event.keyCode || event.charCode;
                if (key == 13) {
                    this.enter();
                    /*Do something. 调用一些方法*/
                }
                if (key == 8) {
                    this.deleteStr();
                }
            };
        },
        getOrderList(status) {
            // this.orderList = [1, 1];
            // eslint-disable-next-line camelcase
            this.$request.order.containerOrderList(this.container).then(res => {
                if (res.data.errorCode == 0) {
                    if (
                        res.data.data.logistics_id == 21 ||
                        res.data.data.logistics_id == 22
                    ) {
                        this.containerBind = true;
                        this.orderList = res.data.data.order;
                        this.containerBind = true;
                        console.log(JSON.stringify(this.orderList));
                        // eslint-disable-next-line camelcase
                        this.task_no = res.data.data.task_no;
                        this.id = res.data.data.id;
                        if (status) {
                            this.groups();
                        }
                    } else {
                        this.$message.error("订单类型错误");
                    }
                } else {
                    console.log(this.orderList.length);
                    if (
                        res.data.msg == "容器编码错误" &&
                        this.orderList.length == 0
                    ) {
                        this.container = "";
                        this.$message.error("容器编码错误");
                        return;
                    }
                    if (res.data.msg == "容器编码错误") {
                        this.container = "";
                        this.containerBind = false;
                        return;
                    }

                    if (res.data.msg == "容器编码不能为空" && status) {
                        // 临时解决方案1
                        this.$message.error("容器编码不能为空");
                        return;
                    }
                    if (
                        res.data.msg == "波次任务未捡货完成" &&
                        this.orderList.length == 0
                    ) {
                        // 临时解决方案

                        this.$message.error("波次任务未捡货完成");
                        return;
                    }
                }
            });
        }
    }
    // ,
    // created() {
    //     PageOptions.pageSidebarMinified = true;
    // },
    // beforeRouteLeave(to, from, next) {
    //     PageOptions.pageSidebarMinified = false;
    //     next();
    // }
};
</script>
<style lang="scss" scoped>
.pack-layout {
    /deep/ .el-table tbody tr:hover > td {
        background-color: transparent;
    }
    .tags {
        .el-tag {
            margin-right: 10px;
        }
    }
    .goods_name {
        width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -moz-box;
        -moz-line-clamp: 2;
        -moz-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
    }
    /deep/ .el-table .warning-row {
        background: #fa4545 !important;
    }

    /deep/ .el-table .success-row {
        background: #97d17a !important;
    }
    /deep/ .el-table .cell,
    .el-table--border td:first-child .cell,
    .el-table--border th:first-child .cell {
        color: #333 !important;
    }
    .pack-form {
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > div > span {
            font-size: 20px;
        }
        .input-300 {
            width: 300px;
        }
    }
    .el-button--primary.is-disabled,
    .el-button--primary.is-disabled:active,
    .el-button--primary.is-disabled:focus,
    .el-button--primary.is-disabled:hover {
        background-color: #e6a23c !important;
        opacity: 0.7;
        border-color: #e6a23c !important;
    }
    /deep/ .panel .panel-heading .panel-title {
        font-size: 18px !important;
    }
    /deep/ .panel .panel-body {
        padding: 0 !important;
    }
    .dialog {
        text-align: center;
        .dialog-footer {
            margin-top: 40px;
        }
    }
    .pack-main {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        .table {
            width: 60%;
            .panel-scorll {
                max-height: 40vh;
                min-height: 15vh;
                overflow: scroll;
            }
            .panel-scorll-box {
                max-height: 30vh;
                min-height: 15vh;
                overflow: scroll;
            }
        }
        .info {
            width: 36%;
            .box-card {
                .box-layout {
                    & > div {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding-bottom: 15px;
                    }
                }
            }
        }
    }
}
</style>
