<template>
    <div class="pack-layout">
        <div class="pack-form">
            <el-input
                ref="inputContainer"
                class="input-200"
                :disabled="orderList.length !== 0"
                v-model="container"
                placeholder="请输入容器编码"
            ></el-input>
            <el-input
                v-if="orderList.length"
                :disabled="true"
                style="margin-left: 10px"
                class="input-300"
                v-model="scanStr"
            >
                <template slot="prepend">{{
                    review_type ? "猫超复核模式" : "普通复核模式"
                }}</template>
            </el-input>
            <div>
                <el-tag>波次编号：{{ task_no | task_noFormat }}</el-tag>
            </div>
            <div>
                <el-tag type="warning"
                    >订单编号：{{ orderNo | orderId_noFormat }}</el-tag
                >
            </div>
            <div>
                <el-tag type="info">工作完成情况：{{ score }}单</el-tag>
            </div>
        </div>
        <div class="pack-main">
            <div class="table">
                <div>
                    <panel title="容器内复核订单">
                        <span style="font-size: 24px"> </span>
                        <el-table
                            height="200"
                            :row-class-name="orderClassName"
                            :data="orderList"
                            border
                            style="width: 100%"
                        >
                            <!-- 容器订单表 -->
                            <el-table-column
                                align="center"
                                label="订单号"
                                min-width="260"
                            >
                                <template slot-scope="row">
                                    <div class="flex-bt">
                                        <div>
                                            <span>{{
                                                row.row.ordersn | wight
                                            }}</span>
                                            <span
                                                style="
                                                    font-size: 20px;
                                                    font-weight: bold;
                                                    margin-left: 2px;
                                                "
                                                >{{
                                                    row.row.ordersn | bold
                                                }}</span
                                            >
                                        </div>
                                        <el-tooltip
                                            class="item"
                                            effect="dark"
                                            content="打印订单清单"
                                            placement="top-start"
                                        >
                                            <el-button
                                                type="primary"
                                                icon="el-icon-s-order"
                                                circle
                                                @click="
                                                    getChecklist(
                                                        row.row.ordersn
                                                    )
                                                "
                                                size="mini"
                                            ></el-button>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="orderno"
                                align="center"
                                width="260"
                                label="商家单号"
                            >
                            </el-table-column>
                            <el-table-column
                                label="运单号"
                                min-width="180"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <p
                                        style="margin-bottom: 6px"
                                        v-for="(item, index) in row.row
                                            .logistics_no"
                                        :key="index"
                                    >
                                        {{ item }}
                                    </p>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="goods.length"
                                label="SKU数"
                                width="90"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="goods_nums"
                                align="center"
                                label="总数量"
                                width="80"
                            >
                            </el-table-column>

                            <!-- <el-table-column
                                label="特殊要求"
                                align="center"
                                width="140"
                            >
                                <template slot-scope="row">
                                    <div v-html="row.row.attach_info"></div>
                                </template>
                            </el-table-column> -->
                            <el-table-column
                                label="状态"
                                width="80"
                                align="center"
                            >
                                <template slot-scope="row">
                                    {{ row.row.status | statusFormat }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="是否撤单"
                                width="95"
                                align="center"
                            >
                                <template slot-scope="row">
                                    {{
                                        row.row.is_cancel_order
                                            | is_cancel_orderFormat
                                    }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="出库状态"
                                width="95"
                                align="center"
                            >
                                <template slot-scope="row">
                                    {{
                                        row.row.outbound_status
                                            | outbound_statusFormat
                                    }}
                                </template>
                            </el-table-column>

                            <!-- <el-table-column
                                label="操作"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <el-button
                                        v-if="
                                            row.row.status == 4 &&
                                                row.row.is_cancel_order == 0
                                        "
                                        id="print"
                                        @click="print(row.row)"
                                        type="text"
                                        >打印运单</el-button
                                    >
                                </template>
                            </el-table-column> -->
                        </el-table>
                    </panel>
                </div>
                <div>
                    <panel title="订单内详情明细">
                        <span style="font-size: 24px"> </span>
                        <el-table
                            :row-class-name="detailsClassName"
                            :data="orderDetailList"
                            border
                            height="200"
                            style="width: 100%"
                        >
                            <!-- 订单详情表 -->
                            <el-table-column
                                align="center"
                                prop="bar_code"
                                label="条码"
                                width="170"
                            >
                                <template slot-scope="row">
                                    {{ row.row.bar_code | wight }}
                                    <span
                                        style="
                                            font-size: 20px;
                                            font-weight: bold;
                                            margin-left: 2px;
                                        "
                                        >{{ row.row.bar_code | bold }}</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="center"
                                prop="short_code"
                                label="简码"
                                width="140"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="goods_name"
                                label="名称"
                                min-width="150"
                                align="center"
                            >
                            </el-table-column>

                            <el-table-column
                                align="center"
                                prop="nums"
                                label="总数量"
                                width="80"
                            >
                                <template slot-scope="row">
                                    <span
                                        style="
                                            font-weight: bold;
                                            font-size: 18px;
                                        "
                                        >{{ row.row.nums }}
                                    </span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                label="已复核数量"
                                width="120"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <span
                                        style="
                                            font-weight: bold;
                                            font-size: 18px;
                                        "
                                    >
                                        {{ row.row.review_nums }}</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="未复核数量"
                                width="120"
                                align="center"
                            >
                                <template slot-scope="row">
                                    <span
                                        style="
                                            font-weight: bold;
                                            font-size: 18px;
                                        "
                                        >{{
                                            row.row.nums - row.row.review_nums
                                        }}</span
                                    >
                                </template>
                            </el-table-column>
                        </el-table>
                    </panel>
                </div>
            </div>
            <div class="info" v-if="orderDetailListStatus">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>订单内复核详情</span>
                    </div>
                    <div class="box-layout">
                        <div>
                            <div>商品名称</div>
                            <div class="goods_name">{{ info.goods_name }}</div>
                        </div>
                        <div>
                            <div>商品条码</div>
                            <span>{{ info.bar_code }}</span>
                        </div>
                        <div>
                            <div>商品简码</div>
                            <div>{{ info.short_code }}</div>
                        </div>
                        <div>
                            <div>复核数量</div>
                            <div>
                                <!--  -->
                                {{ info.checkNumber
                                }}<el-button
                                    v-if="userInfo.allselect"
                                    style="margin-left: 6px"
                                    size="mini"
                                    id="checkAll"
                                    @click="checkAll"
                                    >全部</el-button
                                >
                            </div>
                        </div>
                        <div>
                            <div>订单商品总数量</div>
                            <div>{{ info.nums }}</div>
                        </div>
                        <div>
                            <div>快递公司</div>
                            <div>{{ info.logistics_company }}</div>
                        </div>
                        <div>
                            <div>货主名称</div>
                            <div>{{ info.receiver_name }}</div>
                        </div>
                        <div>
                            <div>地区</div>
                            <div>
                                {{ info.province }}/{{ info.city }}/{{
                                    info.town
                                }}
                            </div>
                        </div>
                        <div>
                            <div>包裹数量</div>
                            <div>{{ info.packNumber }}</div>
                        </div>
                        <div>
                            <div>特殊要求</div>
                            <div v-html="info.attach_info"></div>
                        </div>
                    </div>
                </el-card>
            </div>
        </div>

        <el-button-group>
            <el-button type="primary" @click="addPack">增加包裹</el-button>
            <el-button type="warning" @click="againPack">重打面单</el-button>
            <el-button
                type="success"
                @click="copyDialogStatus = true"
                v-if="copyOrderDetailsList.length !== 0"
                >查看上条订单商品详情</el-button
            >
        </el-button-group>
        <el-button-group style="margin-left: 24px">
            <el-button id="close-btn" type="danger" @click="closeOrderList"
                >关闭容器</el-button
            >
        </el-button-group>
        <div class="dialog">
            <el-dialog
                title="订单包裹数量设置"
                :visible.sync="dialogVisible"
                width="20%"
                :close-on-click-modal="false"
                :show-close="false"
                :close-on-press-escape="false"
            >
                <el-input-number
                    v-model="info.packNumber"
                    :min="1"
                    :step="1"
                    step-strictly
                    :max="1000"
                    label="包裹数量"
                ></el-input-number>
                <div class="dialog-footer">
                    <el-button
                        type="primary"
                        id="dialogPrint"
                        @click="dialogPrint"
                    >
                        开始打印
                    </el-button>
                </div>
            </el-dialog>
        </div>
        <div class="batchdialog">
            <el-dialog
                title="批量复核"
                :visible.sync="batchCheckDialog"
                width="30%"
                :close-on-click-modal="false"
                :show-close="false"
                :close-on-press-escape="false"
            >
                <span>
                    发现该容器订单中，发现多个与目前订单相同，是否确认一次性批量复核？
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="cancelBatch">不用了</el-button>
                    <el-button type="primary" @click="batchCheck"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
        </div>
        <div>
            <el-dialog title="订单明细" :visible.sync="copyDialogStatus">
                <el-table :data="copyOrderDetailsList" border>
                    <!-- 订单详情表 -->
                    <el-table-column
                        align="center"
                        prop="bar_code"
                        label="条码"
                        width="170"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="short_code"
                        label="简码"
                        width="140"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="goods_name"
                        label="名称"
                        align="center"
                    >
                    </el-table-column>

                    <el-table-column
                        align="center"
                        prop="nums"
                        label="总数量"
                        width="80"
                    >
                    </el-table-column>
                </el-table>
            </el-dialog>
        </div>
    </div>
</template>
<script>
// import axios from "axios";
import SCPPrint from "../../plugins/SCPPrint";
import { mapState } from "vuex";
import Panel from "../../plugins/panel/Panel.vue";
// import PageOptions from "@/config/PageOptions.vue";
export default {
    components: {
        Panel,
    },
    data() {
        return {
            LODOP: null,
            printSdk: null,
            batchCheckOrderId: [], // 批量复核的订单队列将要给打印队列的
            IsOpenBatch: false, // 批量复核的开关
            batchCheckDialog: false,
            copyDialogStatus: false,
            orderNo: "",
            // eslint-disable-next-line camelcase
            arr_groups: {},
            // eslint-disable-next-line camelcase
            arr_groups_check: [],
            isPrint: 0,
            loading: null,
            dialogVisible: false,
            containerBind: true,
            copyOrderDetailsList: [],
            orderId: "",
            review_type: 0,
            score: 0,
            id: "", //波次id
            info: {
                city: "",
                town: "",
                province: "",
                logistics_company_type: "",
                checkNumber: 1,
                // eslint-disable-next-line camelcase
                attach_info: "",
                nums: 0,
                // eslint-disable-next-line camelcase
                short_code: "",
                // eslint-disable-next-line camelcase
                bar_code: "",
                // eslint-disable-next-line camelcase
                receiver_name: "",
                packNumber: 1,
                // eslint-disable-next-line camelcase
                logistics_company: "",
            },
            // eslint-disable-next-line camelcase
            task_no: "", // 波次编号
            orderList: [], // 订单列表
            orderIdList: [],
            orderDetailList: [], // 订单详情列表
            reduceLoadingStatus: false, //减少状态
            container: "", //容器编码input value
            orderListStatus: false, // 订单列表信息状态
            orderDetailListStatus: false, // 订单详情列表信息状态
            scanStr: "", //扫码枪输入的字符串
        };
    },
    watch: {
        orderDetailList(newValue, oldValue) {
            console.error(newValue, oldValue);
            if (newValue.length == 0) {
                if (oldValue.length != 0) {
                    this.copyOrderDetailsList = oldValue;
                }
                this.$refs.inputContainer.focus();
                this.orderDetailListStatus = false;
            } else {
                this.copyOrderDetailsList = [];
                this.orderDetailListStatus = true;
            }
        },
        orderList(val) {
            if (val.length == 0) {
                this.orderListStatus = false;
            } else {
                this.orderListStatus = true;
            }
        },
    },
    beforeDestroy() {
        this.unBindKey();
    },
    mounted() {
        this.initSDKPrint();
        this.$refs.inputContainer.focus();
        this.bindKey();
        this.$request.order.getSituationCount().then((res) => {
            if (res.data.errorCode == 0) {
                this.score = res.data.data.order_count;
            }
        });
    },
    computed: {
        ...mapState(["userInfo"]),
    },
    filters: {
        outbound_statusFormat(val) {
            const num = Number(val);
            switch (num) {
                case 0:
                    return "正常";
                case 1:
                    return "终止出库";
                default:
                    return "未知";
            }
        },
        wight(val) {
            if (val.length > 4) {
                return val.substring(val.length - 4, 0);
            } else {
                return val;
            }
        },
        bold(val) {
            if (val.length > 4) {
                return val.substring(val.length - 4);
            } else {
                val;
            }
        },
        // eslint-disable-next-line camelcase
        task_noFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无波次编号";
            }
        },
        // eslint-disable-next-line camelcase
        orderId_noFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无订单";
            }
        },
        // eslint-disable-next-line camelcase
        is_cancel_orderFormat(val) {
            if (val == 0) {
                return "正常";
            } else if (val == 1) {
                return "已撤单";
            } else if (val == 2) {
                return "已取消";
            } else {
                return "未知";
            }
        },
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未拣货";
                case 1:
                    return "拣货中";
                case 2:
                    return "已拣货";
                case 3:
                    return "复核中";
                case 4:
                    return "已复核";
                case 5:
                    return "已出库";
                default:
                    return "未知";
            }
        },
    },

    methods: {
        async getChecklist(ordersn) {
            const loading = this.$loading({
                lock: true,
                text: "正在准备打印中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });

            console.log(loading);
            let data = {
                ordersn,
            };
            const res = await this.$request.order.getChecklist(data);
            if (res.data.errorCode == 0) {
                const expressData = {
                    data: res.data.data.data,
                    express_type: res.data.data.company,
                };
                this.$request.order
                    .print(expressData)
                    .then((res) => {
                        loading.close();
                        if (res.data.errorCode == 0) {
                            this.$message.success("打印清单成功");
                        } else {
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                type: "error",
                                duration: 5000,
                                message:
                                    '<span style="font-size:36px">打印清单失败，请重新打印</span>',
                            });
                        }
                    })
                    .catch(() => {
                        loading.close();
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印清单失败，请重新打印</span>',
                        });
                    });
            } else {
                loading.close();
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">打印清单失败，获取订单信息失败</span>',
                });
            }
        },
        barCode(val) {
            if (val.length > 5) {
                return "****" + val.substring(4);
            } else {
                return val;
            }
        },
        deleteStr() {
            this.scanStr = this.scanStr.substring(0, this.scanStr.length - 1);
            console.log(this.scanStr);
        },
        againPack() {
            window.open("/#/packTool?orderId=" + this.orderNo + "&type=again");
            // this.$router.push({
            //     path: "/packTool",
            //     query: {
            //         orderId: this.orderNo,
            //         type: "again"
            //     }
            // });
            // 重打
        },
        addPack() {
            // 添加包裹
            window.open("/#/packTool?orderId=" + this.orderNo + "&type=add");
            // this.$router.push({
            //     path: "/packTool",
            //     query: {
            //         orderId: this.orderNo,
            //         type: "add"
            //     }
            // });
        },
        cancelBatch() {
            this.batchCheckDialog = false;
            this.finishCheck();
            Object.keys(this.arr_groups).forEach((key) => {
                this.arr_groups[key].map((orderId, index) => {
                    console.log(orderId);
                    if (this.orderId === orderId) {
                        this.arr_groups[key].splice(index, 1);
                    }
                });
            });
        },
        batchCheck() {
            this.batchCheckDialog = false;
            this.orderIdList = this.batchCheckOrderId;
            this.batchCheckOrderId = [];
            this.dialogVisible = true;
        },

        detailsClassName({ row }) {
            console.error(row);
            if (row.nums === row.review_nums) {
                return "success-row";
            } else {
                return "warning-row";
            }
        },
        orderClassName({ row }) {
            console.error(row);
            if (row.status === 5) {
                return "success-row";
            } else {
                return "warning-row";
            }
        },
        groups() {
            console.log("xxxxxxx");
            let data = this.orderList;
            console.log(data);
            let filtersOrderDetailList = data.filter(
                (item) => item.is_cancel_order == 0 && item.status == 2
            ); // 筛选出已撤单的订单（不加入查询商品中）
            console.log(filtersOrderDetailList);
            filtersOrderDetailList.forEach((element) => {
                let goods = element["goods"];
                let orderid = element["order_id"];
                let g = "";
                let skus = [];
                goods.forEach((item) => {
                    skus.push(`${item["bar_code"]}_${item["nums"]}`);
                });
                skus.sort();

                skus.forEach((sku) => {
                    g = g + sku;
                });
                if (this.arr_groups_check.indexOf(g) === -1) {
                    let d = this.arr_groups[g];
                    if (d === undefined) {
                        this.arr_groups[g] = [];
                        d = this.arr_groups[g];
                    }
                    d.push(orderid);
                } else {
                    let d = this.arr_groups[g];
                    d.push(orderid);
                }
                this.arr_groups_check.push(g);
            });

            console.log(this.arr_groups);
        },
        unBindKey() {
            document.onkeyup = undefined;
            document.onkeypress = undefined;
        },
        bindKey() {
            this.getKeyup();
            this.enterKeyup();
        },
        checkAll() {
            if (!this.userInfo.allselect) {
                this.$message.error("暂无权限");
                return;
            }
            document.getElementById("checkAll").blur();
            this.subtract(this.info.bar_code, true);
        },
        enter() {
            if (this.batchCheckDialog) {
                this.batchCheck();
            } else if (this.dialogVisible) {
                console.log(this.scanStr.length);
                if (this.scanStr.length == 0) {
                    this.dialogPrint();
                    return;
                } else {
                    this.scanStr = "";
                    return;
                }
            } else if (!this.orderListStatus) {
                console.log("容器");
                // 订单列表为空，就去请求订单列表
                // this.container = this.scanStr;
                this.getOrderList(true);
                this.scanStr = "";
            } else if (this.orderListStatus && !this.orderDetailListStatus) {
                // console.log("执行减1并且检索");
                if (this.review_type) {
                    this.findRowFaceOrder(this.scanStr); // 猫超
                } else {
                    this.findRow(this.scanStr); // 普通
                }
                // 订单列表不为空 并且 订单详情为空，就去获取订单详情以及查询扫码对应商品在那个订单的（从上倒下搜索），然后再复核数量+1
                this.scanStr = "";
            } else if (this.orderListStatus && this.orderDetailListStatus) {
                console.log("减少");
                this.subtract(this.scanStr);
                // 订单列表不为空 并且 订单详情不为空，扫码对应商品核数量+1
                this.scanStr = "";
            }
        },
        finishCheck() {
            // 订单详情中所有的数据 已经复核完成的时候调用
            // this.getOrderList();
            console.log("finish");
            this.$request.order.getSituationCount().then((res) => {
                if (res.data.errorCode == 0) {
                    this.score = res.data.data.order_count;
                }
            });
            // this.unBindKey();
            this.dialogVisible = true;
            this.info.packNumber = 1;
        },
        isCheckDetailFinish() {
            // 检查是否完成，在每次复核的时候调用
            let status = true;
            this.orderDetailList.map((item) => {
                if (item.review_nums !== item.nums) {
                    status = false;
                }
            });
            return status;
            // console.log(status);
        },
        closeOrderList() {
            this.$confirm("此操作将关闭该容器, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    // eslint-disable-next-line camelcase
                    this.task_no = "";
                    document.getElementById("close-btn").blur();

                    this.orderList = [];
                    this.container = "";
                    this.orderDetailList = [];
                    this.$message({
                        type: "success",
                        message: "关闭成功",
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        // setExpressData(orderId, type) {
        //     // let data = [];
        // },
        delayTime(time) {
            setTimeout(() => {
                this.unBindKey();

                const loading = this.$loading({
                    lock: true,
                    text: "请等待30秒再进行复核工作",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)",
                });
                setTimeout(() => {
                    loading.close();
                    this.bindKey();
                }, time);
                console.log(time);
            }, 400);
        },
        async initSDKPrint() {
            // /admin/outbound/review/getSfConfig
            const res = await this.$request.order.initSDKConfig();
            if (res.data.errorCode == 0) {
                // 引入SDK后初始化实例，仅执行一次
                // eslint-disable-next-line no-unused-vars
                const sdkCallback = (result) => {};
                const sdkParams = {
                    env: res.data.data.env,
                    partnerID: res.data.data.partnerID,
                    callback: sdkCallback,
                    notips: res.data.data.notips,
                };
                this.printSdk = new SCPPrint(sdkParams);
            }
        },
        Def_Print(expressData, orderIndex) {
            this.$request.order
                .print(expressData)
                .then((res) => {
                    if (!this.containerBind) {
                        // eslint-disable-next-line camelcase
                        this.task_no = "";
                        this.orderList = [];
                    }
                    if (orderIndex === this.orderIdList.length) {
                        this.loading.close();
                        console.log(res);
                        this.bindKey();
                        this.orderDetailList = [];
                        if (res.data.errorCode == 0) {
                            this.$message.success("打印运单成功");
                            let status = true;
                            this.orderList.map((i) => {
                                if (i.status !== 4 || i.is_cancel_order !== 0) {
                                    status = false;
                                }
                            });
                            console.log(status);
                            if (status) {
                                // eslint-disable-next-line camelcase
                                this.task_no = "";
                                this.orderList = [];
                            }
                        } else {
                            console.info("打印失败。。。");
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                type: "error",
                                duration: 5000,
                                message:
                                    '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                            });
                        }
                    }
                })
                .catch((err) => {
                    console.info("错了。。。");
                    if (!this.containerBind) {
                        // eslint-disable-next-line camelcase
                        this.task_no = "";
                        this.orderList = [];
                    }
                    this.$message({
                        dangerouslyUseHTMLString: true,
                        type: "error",
                        duration: 5000,
                        message:
                            '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                    });
                    console.log(err);
                    this.loading.close();
                    this.bindKey();
                    this.orderDetailList = [];
                })
                .finally(() => {
                    if (!this.userInfo.allselect) {
                        // this.delayTime(30000);
                        console.log("暂时关闭30秒打包等待");
                    }
                    this.getOrderList();
                });
        },
        dialogPrint() {
            this.unBindKey();
            this.orderIdList.map((i, orderIndex) => {
                console.log(i);
                document.getElementById("dialogPrint").blur();
                // 单个复核完成后打印
                this.dialogVisible = false;

                this.loading = this.$loading({
                    lock: true,
                    text: "正在准备打印中...",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)",
                });
                let data = {
                    // eslint-disable-next-line camelcase
                    order_id: i,
                    // eslint-disable-next-line camelcase
                    package_nums: this.info.packNumber,
                };
                this.$request.order
                    .getPrintInfo(data)
                    .then((res) => {
                        if (res.data.errorCode == 0) {
                            let Base64 = require("js-base64").Base64;
                            console.log(res);

                            let expressData = {
                                // data: res.data.data.data,
                                data:
                                    res.data.data.company === "sf" ||
                                    res.data.data.company === "dpk"
                                        ? Base64.decode(res.data.data.data)
                                        : res.data.data.data,
                                // eslint-disable-next-line camelcase
                                express_type: res.data.data.company,
                            };

                            if (res.data.data.company === "sf") {
                                console.warn(expressData);
                                this.SF_SDK_Print(expressData, orderIndex);
                                console.log("顺丰");
                            } else if (expressData.express_type === "dpk") {
                                this.DPK_Print(expressData.data, orderIndex);
                                console.log("德邦");
                            } else {
                                this.Def_Print(expressData, orderIndex);
                                console.log("普通");
                            }

                            // 打印回掉完成后
                        } else {
                            if (!this.containerBind) {
                                // eslint-disable-next-line camelcase
                                this.task_no = "";
                                this.orderList = [];
                            }
                            this.loading.close();
                            this.bindKey();
                            this.orderDetailList = [];
                        }
                    })
                    .catch((err) => {
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                        });
                        console.log(err);
                        this.loading.close();
                        this.bindKey();
                        this.orderDetailList = [];
                    });
                // console.info();
            });
            this.orderIdList = [];
        },
        DPK_Print(expressData, orderIndex) {
            expressData = JSON.parse(expressData);
            expressData.map((item) => {
                this.$request.order
                    .print(item)
                    .then((res) => {
                        if (!this.containerBind) {
                            // eslint-disable-next-line camelcase
                            this.task_no = "";
                            this.orderList = [];
                        }
                        if (orderIndex === this.orderIdList.length) {
                            this.loading.close();
                            console.log(res);
                            this.bindKey();
                            this.orderDetailList = [];
                            if (res.data.errorCode == 0) {
                                this.$message.success("打印运单成功");
                                let status = true;
                                this.orderList.map((i) => {
                                    if (
                                        i.status !== 4 ||
                                        i.is_cancel_order !== 0
                                    ) {
                                        status = false;
                                    }
                                });
                                console.log(status);
                                if (status) {
                                    // eslint-disable-next-line camelcase
                                    this.task_no = "";
                                    this.orderList = [];
                                }
                            } else {
                                console.info("打印失败。。。");
                                this.$message({
                                    dangerouslyUseHTMLString: true,
                                    type: "error",
                                    duration: 5000,
                                    message:
                                        '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                                });
                            }
                        }
                    })
                    .catch((err) => {
                        console.info("错了。。。");
                        if (!this.containerBind) {
                            // eslint-disable-next-line camelcase
                            this.task_no = "";
                            this.orderList = [];
                        }
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                        });
                        console.log(err);
                        this.loading.close();
                        this.bindKey();
                        this.orderDetailList = [];
                    })
                    .finally(() => {
                        if (!this.userInfo.allselect) {
                            // this.delayTime(30000);
                            console.log("暂时关闭30秒打包等待");
                        }
                        this.getOrderList();
                    });
            });
        },
        SF_SDK_Print(expressData, orderIndex) {
            if (!this.printSdk) {
                this.$message.error("SDK还未准备好");
                return;
            }
            // 调用打印方法
            // eslint-disable-next-line no-unused-vars
            const printCallback = (result) => {
                this.getOrderList();
                if (!this.containerBind) {
                    // eslint-disable-next-line camelcase
                    this.task_no = "";
                    this.orderList = [];
                }
                if (orderIndex === this.orderIdList.length) {
                    this.loading.close();
                    this.bindKey();
                    this.orderDetailList = [];
                    this.$message.success("打印运单成功");
                    let status = true;
                    this.orderList.map((i) => {
                        if (i.status !== 4 || i.is_cancel_order !== 0) {
                            status = false;
                        }
                    });
                    console.log(status);
                    if (status) {
                        // eslint-disable-next-line camelcase
                        this.task_no = "";
                        this.orderList = [];
                    }
                }
            };
            const options = {
                lodopFn: "PRINT", // 默认打印，预览传PREVIEW
            };
            console.warn(expressData);
            const data = JSON.parse(expressData.data);
            this.printSdk.print(data, printCallback, options);
        },

        print(row) {
            document.getElementById("print").blur();
            // 单个重复打印
            console.log(row, "打印");
        },
        subtract(scanStr, isAll) {
            if (this.reduceLoadingStatus) {
                return;
            }
            console.log("减少");
            let status = true; // 是否找到

            this.orderDetailList.filter((item) => {
                console.log(item);
                if (item.bar_code === scanStr.replace(/\s*/g, "")) {
                    // eslint-disable-next-line camelcase

                    if (item.review_nums < item.nums) {
                        status = false;

                        let data = {
                            id: this.id,
                            // eslint-disable-next-line camelcase
                            bar_code: item.bar_code,
                            // eslint-disable-next-line camelcase
                            order_id: this.orderId,
                            nums: isAll ? item.nums : item.review_nums + 1,
                        };
                        // eslint-disable-next-line camelcase
                        this.info.bar_code = item.bar_code;
                        // eslint-disable-next-line camelcase
                        this.info.goods_name = item.goods_name;
                        // eslint-disable-next-line camelcase
                        this.info.short_code = item.short_code;
                        console.info(data);
                        this.reduceLoadingStatus = true;
                        this.$request.order.reviewoper(data).then((res) => {
                            console.log(res);
                            this.reduceLoadingStatus = false;
                            if (res.data.errorCode == 0) {
                                if (isAll) {
                                    // eslint-disable-next-line camelcase
                                    item.review_nums = item.nums;
                                } else {
                                    item.review_nums++;
                                }
                                this.info.checkNumber = item.review_nums;

                                // 复核操作

                                if (this.isCheckDetailFinish()) {
                                    this.getOrderList(false, "已完成");
                                } else {
                                    this.getOrderList(false);
                                }
                            }
                        });
                    } else {
                        status = false;
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "success",
                            duration: 4000,
                            message:
                                '<span style="font-size:36px">该订单中，该商品类型已全部复核</span>',
                        });
                    }
                }
                return item;
                // return item.bar_code !== scanStr.replace(/\s*/g, "");
            });
            if (status) {
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">该待符合订单明细中，没有找到符合该订单的商品</span>',
                });
            }

            // console.log(result);
        },
        findRowFaceOrder(scanStr) {
            let status = true; // 是否找到
            let filtersOrderDetailList = this.orderList.filter(
                (item) =>
                    item.is_cancel_order == 0 &&
                    (item.status == 2 || item.status == 3)
            ); // 筛选出已撤单的订单（不加入查询商品中）
            console.log(filtersOrderDetailList);

            filtersOrderDetailList.map((map, index) => {
                if (status) {
                    map.logistics_no.map((find) => {
                        if (find == scanStr.replace(/\s*/g, "")) {
                            console.log(index);
                            status = false;
                            // eslint-disable-next-line camelcase
                            this.orderIdList = [map.order_id];
                            this.orderNo = map.ordersn;
                            this.orderId = map.order_id;
                            this.info.attach_info = map.attach_info;
                            this.isPrint = map.is_logistics;
                            // eslint-disable-next-line camelcase
                            this.info.city = map.city;
                            this.info.province = map.province;
                            this.info.town = map.town;

                            this.info.receiver_name = map.receiver_name;
                            this.info.nums = map.goods_nums;
                            // eslint-disable-next-line camelcase
                            this.info.logistics_company = map.logistics_company;
                            this.orderDetailList = map.goods;
                            return;
                        }
                    });
                    console.log(this.orderDetailList); //返回第一个符合的元素{name:'li',age:10}
                }
            });
            if (status) {
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">该容器订单中，没有找到符合该面单的订单</span>',
                });
            }
        },
        findRow(scanStr) {
            let status = true; // 是否找到
            let filtersOrderDetailList = this.orderList.filter(
                (item) =>
                    item.is_cancel_order == 0 &&
                    (item.status == 2 || item.status == 3)
            ); // 筛选出已撤单的订单（不加入查询商品中）
            console.log(filtersOrderDetailList);

            filtersOrderDetailList.map((map, index) => {
                if (status) {
                    map.goods.map((find) => {
                        if (find.bar_code == scanStr.replace(/\s*/g, "")) {
                            console.log(index);
                            status = false;
                            // eslint-disable-next-line camelcase
                            this.orderIdList = [map.order_id];
                            this.orderNo = map.ordersn;
                            this.orderId = map.order_id;
                            this.info.attach_info = map.attach_info;
                            this.info.city = map.city;
                            this.info.province = map.province;
                            this.info.town = map.town;
                            this.isPrint = map.is_logistics;
                            // eslint-disable-next-line camelcase
                            this.info.receiver_name = map.receiver_name;
                            this.info.nums = map.goods_nums;
                            // eslint-disable-next-line camelcase
                            this.info.logistics_company = map.logistics_company;
                            this.orderDetailList = map.goods;
                            // 找到了之后，再进行减少
                            this.subtract(this.scanStr);
                            return;
                        }
                    });
                    console.log(this.orderDetailList); //返回第一个符合的元素{name:'li',age:10}
                }
            });
            if (status) {
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">该容器订单中，没有找到符合该订单的商品</span>',
                });
            }

            // this.orderDetailList = [1, 1];
        },
        getKeyup() {
            // 获取键盘（扫码枪）输入
            document.onkeypress = (even) => {
                //获取键盘上的字母键盘的字母
                console.log(even);
                var key = event.which || event.keyCode || event.charCode;

                if (key == 13 || key == 8) {
                    return;
                } else {
                    this.scanStr = this.scanStr + even.key;
                }
            };
        },
        enterKeyup() {
            document.onkeyup = (e) => {
                // 兼容FF和IE和Opera
                var event = e || window.event;
                var key = event.which || event.keyCode || event.charCode;
                console.log(key);
                if (key == 13) {
                    this.enter();
                    /*Do something. 调用一些方法*/
                }
                if (key == 8) {
                    this.deleteStr();
                }
            };
            // 监听回车
        },
        getOrderList(status, checkStatus) {
            // this.orderList = [1, 1];
            // eslint-disable-next-line camelcase
            this.$request.order
                .containerOrderList(this.container)
                .then((res) => {
                    if (res.data.errorCode == 0) {
                        // this.container = "";
                        this.review_type = res.data.data.review_type;
                        // if (
                        //     res.data.data.logistics_id != 21 &&
                        //     res.data.data.logistics_id != 22
                        // ) {
                        this.containerBind = true;
                        this.info.logistics_company_type =
                            res.data.data.logistics_id;

                        this.orderList = res.data.data.order;
                        console.log(JSON.stringify(this.orderList));
                        // eslint-disable-next-line camelcase
                        this.task_no = res.data.data.task_no;
                        this.id = res.data.data.id;
                        if (status) {
                            this.groups();
                        }
                        // } else {
                        //     this.$message.error("订单类型错误");
                        // }
                    } else {
                        console.log(res.data.msg, this.orderList.length);
                        if (
                            res.data.msg == "容器编码错误" &&
                            this.orderList.length == 0
                        ) {
                            this.container = "";
                            this.$message.error("容器编码错误");
                            return;
                        } else if (res.data.msg == "容器编码错误") {
                            this.container = "";
                            this.containerBind = false;
                            return;
                        } else if (res.data.msg == "容器编码不能为空") {
                            this.orderDetailList = [];
                            this.orderList = [];
                            return;
                        } else if (
                            (res.data.msg == "波次任务未捡货完成" ||
                                res.data.msg == "容器编码不能为空") &&
                            this.orderList.length == 0
                        ) {
                            // this.$message.error("容器编码错误");
                            return;
                        }
                    }
                })
                .finally(() => {
                    if (checkStatus) {
                        // 1、优先判断是否有重复订单
                        let status = false;
                        Object.keys(this.arr_groups).forEach((key) => {
                            this.arr_groups[key].map((orderId) => {
                                console.log(orderId);
                                if (
                                    this.orderId === orderId &&
                                    this.arr_groups[key].length > 1
                                ) {
                                    this.batchCheckOrderId =
                                        this.arr_groups[key];

                                    status = true;
                                }
                            });
                        });
                        // 批量复核开关
                        if (status && this.IsOpenBatch) {
                            this.batchCheckDialog = true;
                        } else {
                            this.finishCheck();
                        }
                    }
                });
        },
    },
    // ,
    // created() {
    //     PageOptions.pageSidebarMinified = true;
    // },
    // beforeRouteLeave(to, from, next) {
    //     PageOptions.pageSidebarMinified = false;
    //     next();
    // }
};
</script>
<style lang="scss" scoped>
.pack-layout {
    /deep/ .el-table tbody tr:hover > td {
        background-color: transparent;
    }
    .goods_name {
        width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -moz-box;
        -moz-line-clamp: 2;
        -moz-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
    }
    /deep/ .el-table .warning-row {
        background: #fa4545 !important;
    }

    /deep/ .el-table .success-row {
        background: #97d17a !important;
    }
    /deep/ .el-table .cell,
    .el-table--border td:first-child .cell,
    .el-table--border th:first-child .cell {
        color: #333 !important;
    }
    .flex-bt {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .pack-form {
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > div > span {
            font-size: 20px;
        }
        .input-200 {
            width: 200px;
        }
        .input-300 {
            width: 300px;
        }
    }
    /deep/ .panel .panel-heading .panel-title {
        font-size: 18px !important;
    }
    /deep/ .panel .panel-body {
        padding: 0 !important;
    }
    .dialog {
        text-align: center;
        .dialog-footer {
            margin-top: 40px;
        }
    }
    .pack-main {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        .table {
            width: 65%;
            .panel-scorll {
                max-height: 34vh;
                min-height: 15vh;
                overflow: scroll;
            }
        }
        .info {
            width: 36%;
            .box-card {
                .box-layout {
                    & > div {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding-bottom: 15px;
                    }
                }
            }
        }
    }
}
</style>
