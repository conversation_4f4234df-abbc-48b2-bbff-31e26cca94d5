<template>
    <div>
        <div v-if="show">
            <div class="packTool-form">
                <span style="font-size: 26px">订单编号：</span>
                <el-input
                    class="w-220"
                    v-model="orderId"
                    placeholder="请输入订单编号"
                ></el-input>
                <el-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="print"
                    :disabled="orderId == ''"
                    >打印</el-button
                >

                <el-button
                    style="margin-left: 10px"
                    type="danger"
                    :disabled="orderId == ''"
                    @click="destroyOrder"
                    >作废</el-button
                >
            </div>
            <div class="add">
                <el-dialog
                    title="添加包裹"
                    :visible.sync="dialogStatus"
                    width="30%"
                >
                    <el-input-number
                        v-model="packNumber"
                        :min="1"
                        :step="1"
                        step-strictly
                        :max="1000"
                        label="包裹数量"
                    ></el-input-number>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogStatus = false"
                            >取消</el-button
                        >
                        <el-button type="primary" @click="add">确 定</el-button>
                    </span>
                </el-dialog>
            </div>
            <div class="again">
                <el-dialog
                    title="请选择需要重打的运单号"
                    :visible.sync="dialogStatusAgain"
                    width="30%"
                >
                    <el-checkbox-group v-model="checkList">
                        <el-checkbox
                            v-for="(item, index) in mailNoList"
                            :key="index"
                            :label="item"
                        ></el-checkbox>
                    </el-checkbox-group>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogStatusAgain = false"
                            >取消</el-button
                        >
                        <el-button type="primary" @click="againPrint"
                            >确 定</el-button
                        >
                    </span>
                </el-dialog>
            </div>
        </div>
        <el-dialog
            title="请输入管理员密码"
            :close-on-click-modal="false"
            :show-close="false"
            :close-on-press-escape="false"
            :visible.sync="validatePasswordStatus"
            width="30%"
        >
            <el-input
                v-model="password"
                type="password"
                ref="inputContainer"
                @keyup.enter.native="validatePassword"
            >
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="validatePassword"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import SCPPrint from "../../plugins/SCPPrint";
import Cookies from "js-cookie";
export default {
    computed: {},
    data() {
        return {
            LODOP: null,
            orderId: "",
            validatePasswordStatus: true,
            checkList: [],
            show: false,
            password: "",
            printSdk: null,
            type: "",
            mailNoList: [],
            dialogStatusAgain: false,
            loading: null,
            packNumber: 1,
            dialogStatus: false,
        };
    },
    mounted() {
        this.initSDKPrint();
        if (this.$route.query.orderId) {
            this.orderId = this.$route.query.orderId;
        }
        if (this.$route.query.type) {
            this.type = this.$route.query.type;
        }
        setTimeout(() => {
            this.$refs.inputContainer.focus();
        }, 400);
    },
    methods: {
        async initSDKPrint() {
            // /admin/outbound/review/getSfConfig
            const res = await this.$request.order.initSDKConfig();
            if (res.data.errorCode == 0) {
                // 引入SDK后初始化实例，仅执行一次
                // eslint-disable-next-line no-unused-vars
                const sdkCallback = (result) => {
                    console.log("实例化结果", result);
                };
                const sdkParams = {
                    env: res.data.data.env,
                    partnerID: res.data.data.partnerID,
                    callback: sdkCallback,
                    notips: res.data.data.notips,
                };
                this.printSdk = new SCPPrint(sdkParams);
            }
        },
        destroyOrder() {
            this.$confirm("此操作将作废该订单, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    const data = {
                        order_id: this.orderId,
                    };
                    const res = await this.$request.order.destroyOrder(data);
                    if (res.data.errorCode == 0) {
                        this.$message.success("作废成功");
                    }
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消操作",
                    });
                });
        },
        validatePassword() {
            let data = {
                stock_id: Cookies.get("stock_id"),
                auth_password: this.password,
            };
            this.$request.order.verifyAuthPassword(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.validatePasswordStatus = false;
                    this.show = true;
                }
            });
        },
        add() {
            this.dialogStatus = false;

            let data = {
                // eslint-disable-next-line camelcase
                order_id: this.orderId,
                // eslint-disable-next-line camelcase
                package_nums: this.packNumber,
            };
            this.loading = this.$loading({
                lock: true,
                text: "正在准备打印中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            this.$request.order
                .getPrintInfo(data)
                .then((res) => {
                    console.log(res);
                    if (res.data.errorCode == 0) {
                        let Base64 = require("js-base64").Base64;
                        console.log(res);
                        let expressData = {
                            data:
                                res.data.data.company === "sf" ||
                                res.data.data.company === "dpk"
                                    ? Base64.decode(res.data.data.data)
                                    : res.data.data.data,
                            // eslint-disable-next-line camelcase
                            express_type: res.data.data.company,
                        };
                        console.log(expressData.express_type);
                        if (expressData.express_type === "sf") {
                            this.SF_SDK_Print(expressData);
                            console.log("顺丰");
                        } else if (expressData.express_type === "dpk") {
                            this.DPK_Print(expressData.data);
                            console.log("德邦");
                        } else {
                            this.Def_Print(expressData);
                            console.log("普通");
                        }
                    } else {
                        this.loading.close();
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        },

        DPK_Print(expressData) {
            expressData = JSON.parse(expressData);
            expressData.map((item) => {
                this.$request.order
                    .print(item)
                    .then((res) => {
                        if (res.data.errorCode == 0) {
                            this.loading.close();
                            this.$message.success(
                                "打印运单成功，正在关闭当前页面"
                            );
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        } else {
                            console.info("打印失败。。。");
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                type: "error",
                                duration: 5000,
                                message:
                                    '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                            });
                        }
                    })
                    .catch(() => {
                        this.loading.close();
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                        });
                    });
            });
        },
        SF_SDK_Print(expressData) {
            if (!this.printSdk) {
                this.$message.error("SDK还未准备好");
                return;
            }
            console.log(this.printSdk);
            // 调用打印方法
            // eslint-disable-next-line no-unused-vars
            const printCallback = (result) => {
                console.log("打印结果", result);
                this.loading.close();
                this.$message.success("打印运单成功，正在关闭当前页面");
                setTimeout(() => {
                    window.close();
                }, 3000);
            };
            const options = {
                lodopFn: "PRINT", // 默认打印，预览传PREVIEW
            };
            const data = JSON.parse(expressData.data);
            this.printSdk.print(data, printCallback, options);
        },
        Def_Print(expressData) {
            this.$request.order
                .print(expressData)
                .then((res) => {
                    if (res.data.errorCode == 0) {
                        this.loading.close();
                        this.$message.success("打印运单成功，正在关闭当前页面");
                        setTimeout(() => {
                            window.close();
                        }, 3000);
                    } else {
                        console.info("打印失败。。。");
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                        });
                    }
                })
                .catch(() => {
                    this.loading.close();
                    this.$message({
                        dangerouslyUseHTMLString: true,
                        type: "error",
                        duration: 5000,
                        message:
                            '<span style="font-size:36px">打印运单失败，请重新打印</span>',
                    });
                });
        },
        againPrint() {
            console.log(this.checkList);
            this.loading = this.$loading({
                lock: true,
                text: "正在准备打印中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            let data = {
                // eslint-disable-next-line camelcase
                order_id: this.orderId,
                // eslint-disable-next-line camelcase
                mail_no: this.checkList,
            };
            this.$request.order
                .getFaceSheetFormOrderId(data)
                .then((res) => {
                    console.log(res);
                    if (res.data.errorCode == 0) {
                        let Base64 = require("js-base64").Base64;
                        console.log(res);
                        let expressData = {
                            data:
                                res.data.data.company === "sf" ||
                                res.data.data.company === "dpk"
                                    ? Base64.decode(res.data.data.data)
                                    : res.data.data.data,
                            // eslint-disable-next-line camelcase
                            express_type: res.data.data.company,
                        };
                        if (expressData.express_type === "sf") {
                            this.SF_SDK_Print(expressData);
                            console.log("顺丰");
                        } else if (expressData.express_type === "dpk") {
                            this.DPK_Print(expressData.data);
                            console.log("德邦");
                        } else {
                            this.Def_Print(expressData);
                            console.log("普通");
                        }
                    } else {
                        this.checkList = [];
                        this.loading.close();
                    }
                })
                .catch((err) => {
                    this.checkList = [];
                    console.log(err);
                });
        },
        again() {
            let data = {
                // eslint-disable-next-line camelcase
                order_id: this.orderId,
            };
            console.log(data);
            this.$request.order
                .getHistroyPrint(data)
                .then((res) => {
                    this.dialogStatusAgain = true;

                    console.log(res);
                    this.mailNoList = res.data.data.mail_no;
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        print() {
            if (this.type === "add") {
                //add

                this.dialogStatus = true;
            } else {
                //again
                this.again();
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.packTool-form {
    display: flex;
    align-items: center;
}
.w-220 {
    width: 220px;
}
</style>
