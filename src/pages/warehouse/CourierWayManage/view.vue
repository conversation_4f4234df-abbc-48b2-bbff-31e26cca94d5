<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            :label-width="formLabelWidth"
            size="mini"
        >
            <el-form-item label="模板名称" prop="template_name">
                <el-input
                    placeholder="请输入模板名称"
                    v-model="form.template_name"
                    style="width: 60%"
                    size="mini"
                >
                </el-input>
            </el-form-item>
            <el-form-item
                    label="金额"
                    prop="amount"
                >
                    <el-input-number
                        :min="0"
                        :precision="2"
                        v-model="form.amount"
                    ></el-input-number>  元
                </el-form-item>
            <el-form-item label="快递类型" prop="express_type">
                        <el-radio-group v-model="form.express_type">
                            <el-radio :label="1">顺丰快递</el-radio>
                        </el-radio-group>
                    </el-form-item>
            <el-form-item label="默认业务" prop="default_id">
                <el-radio-group v-model="form.default_id">
                    <el-radio :label="2">陆运包裹</el-radio>
                    <el-radio :label="13">电商标快</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-button
                    type="primary"
                    size="mini"
                    style="margin-left: 10%; margin-bottom: 10px"
                    @click="appointArea()"
                    >特殊规则设置</el-button
                >
                <div v-if="form.rule_data.length">
                    <el-card
                        shadow="always"
                        style="
                            width: 80%;
                            padding-top: 15px;
                            margin: 0 auto;
                            margin-bottom: 30px;
                        "
                        v-for="(apItem, apIndex) in form.rule_data"
                        :key="'apItem_' + apIndex"
                    >
                        <el-form-item label="业务类型" prop="title">
                            <el-select
                                v-model="apItem.business_type"
                                filterable
                                placeholder="请选择"
                                size="mini"
                            >
                                <el-option
                                    v-for="item in courierOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-button
                                type="text"
                                size="mini"
                                style="margin-left: 7%"
                                @click="moveUp(apIndex)"
                                >上移</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                @click="moveDown(apIndex)"
                                >下移</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                style="color: red"
                                @click="delAppointArea(apIndex)"
                                >删除</el-button
                            >
                        </el-form-item>
                        <el-form-item
                label="地区包含"
                prop="area"
            >
        
                <el-select
                    v-model="apItem.area"
                    multiple
                    placeholder="请选择地区"
                    style="width: 300px; margin-bottom: 10px"
                    :disabled="form.allCountry"
                    filterable
                >
                    <el-option
                        v-for="item in optionsArea"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                    >
                    </el-option>
                </el-select>

                <!-- <el-checkbox
                    :disabled="form.area.length != 0"
                    v-model="form.allCountry"
                    style="margin-left: 30px"
                    >全国</el-checkbox
                > -->
            </el-form-item>
                        <el-form-item label="重量小于等于" prop="weight">
                            <el-input
                                placeholder="请输入重量"
                                v-model="apItem.weight"
                                style="width: 150px"
                                size="mini"
                            >
                            </el-input><span> KG</span>
                         </el-form-item>
                       
                    </el-card>
                    <!-- <div
                        style="width: 540px; margin-left: 11%; color: gray"
                        v-if="item.appoint.length != 0"
                    >
                        可设置指定快递方式的条件，若同时输入瓶数、金额和商品属性，则表示达到其中任意一条即满足指定快递方式
                    </div> -->
                </div>
         
            <el-form-item
                style="
                    display: flex;
                    justify-content: center;
                    margin-right: 150px;
                    margin-top: 20px;
                "
            >
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            courierOptions: [],
            goodOptions: [],
           
            EntityVirtualCang: [], //实体和虚拟仓
            // warehouse: [], //选择的实体和虚拟仓
            optionsArea:[],
            form: {
                id:"",
                template_name: "",
                express_type:1,
                default_id: 2,
                rule_data:[
                  
                ],
                status:0,
                amount:0
            },
            formRules: {
                template_name: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur",
                    },
                ],
                amount: [
                    {
                        required: true,
                        message: "请输入金额",
                        trigger: "blur",
                    },
                ],
            },
            formLabelWidth: "100px",
            pageAttr: {
                pid: 1,
                fid: 0,
            },
            isMove: false,
        };
    },
    mounted() {
        this.getCourierwayOptions();
        this.getAreaList();
        this.form.template_name = this.rowData.template_name;
        this.form.status = this.rowData.status;
        this.form.rule_data = this.rowData.rule_data;
        this.form.express_type = this.rowData.express_type;
        this.form.default_id = this.rowData.default_id;
        this.form.id = this.rowData.id;
        this.form.amount = this.rowData.amount;
    },
    methods: {
        // change(a, eIndex) {
        //     // let flag = false;
        //     console.log("新数据", a, eIndex);
        //     let b = JSON.parse(JSON.stringify(a));
        //     console.log("上一次数据", b.splice(0, b.length - 1));
        //     // b.forEach((e, index) => {
        //     //     if (e == a[a.length - 1]) {
        //     //         a.splice(index, 1);
        //     //     }
        //     // });
        // },
        // 快递方式option
        async getCourierwayOptions() {
            let res =
                await this.$request.stock.getbusinessListt();
            console.log("快递方式", res);
            if (res.data.errorCode == 0) {
                this.courierOptions = res.data.data;
            }
        },
         //获取指定地区
         async getAreaList() {
            let data = {
            };
            let res = await this.$request.stock.getAreaList(data);
            if (res.data.errorCode == 0) {
                this.optionsArea = res.data.data;
                // this.options.unshift({ name: "全国", id: 0 });
            }
        },
       
        //指定快递方式增加
        appointArea() {
            let obj = {
                        business_type: 2,
                        area: [],
                        weight:""
                    };
            this.form.rule_data.push(obj);
        },
        // 删除指定快递方式参数
        delAppointArea( apIndex) {
           
            this.form.rule_data.splice(apIndex, 1);
        },
        // 上移
        moveUp(apIndex) {
            console.log(apIndex);
            if (apIndex != 0) {
                this.form.rule_data.splice(
                    apIndex,
                    1,
                    ...this.form.rule_data.splice(
                        apIndex - 1,
                        1,
                        this.form.rule_data[apIndex]
                    )
                );
            }
        },
        // 下移
        moveDown(apIndex) {
            console.log(apIndex);
            if (apIndex != this.form.rule_data.length) {
                this.form.rule_data.splice(
                    apIndex,
                    1,
                    ...this.form.rule_data.splice(
                        apIndex + 1,
                        1,
                        this.form.rule_data[apIndex]
                    )
                );
            }
        },
        closeDiog() {
            this.$emit("closeView");
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate((valid) => {
                if (valid) {
                  
                    this.$request.stock.editExperssTemplate(this.form).then(
                        (res) => {
                            console.log("结果", res);
                            if (res.data.errorCode == 0) {
                                this.$Message.success("修改成功");
                                this.closeDiog();
                            }
                        }
                    );
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep.el-form-item {
    margin-bottom: 0px;
}
::v-deep.el-form-item {
    margin-bottom: 0px;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
