<template>
    <div>
        <el-card shadow="always" style="margin: 10px 10px">
            <el-button type="primary" size="mini" @click="dialogStatus = true"
                >新增模板</el-button
            >
        </el-card>
        <div class="box" style="margin: 10px 10px">
            <el-row :gutter="20">
                <el-col
                    :span="7"
                    v-for="(wItem, index) in WayManageList"
                    :key="'wItem_' + index"
                >
                    <div
                        @click="choose(index, wItem)"
                        style="cursor: pointer; overflow: hidden"
                    >
                        <el-card
                            shadow="always"
                            style=" margin: 10px 0"
                            :class="wItem.status == 1 ? 'card' : ''"
                        >
                            <i
                                class="el-icon-check"
                                style="
                                    font-size: 44px;
                                    float: right;
                                    color: #67c23a;
                                "
                                v-if="wItem.status == 1"
                            ></i>
                            <div style="display: flex; justify-content: left">
                                <h2>{{ wItem.template_name }}</h2>
                                <div style="margin-left: 20px">
                                    <el-button
                                        type="warning"
                                        size="mini"
                                        @click.stop="view(wItem)"
                                        >编辑</el-button
                                    >
                                </div>
                            </div>
                            <div
                                    class="module"
                                    style="
                                        width: 100%;
                                        display: flex;
                                        align-items: center;
                                    "
                                >
                                    <span style="width: 80px">快递公司</span>
                                    <span style="width: 85%">{{
                                        wItem.express_type == 1
                                            ? "顺丰快递"
                                            : ""
                                    }}</span>
                                </div>
                                <!-- 指定快递 -->
                                <div
                                    class="module"
                                    style="
                                        width: 100%;
                                        display: flex;
                                        justify-content: left;
                                    "
                                >
                                    <div
                                        style="width: 80px"
                                    >
                                        默认方式
                                    </div>
                                    <div style="width: 85%">
                                      {{ wItem.default_id == 2 ? "陆运包裹" : "电商标快"}}
                                    </div>
                                  
                                </div>
                                <div
                                    class="module"
                                    style="
                                        width: 100%;
                                        display: flex;
                                        justify-content: left;
                                    "
                                >
                                    <div
                                        style="width: 80px"
                                    >
                                        金额
                                    </div>
                                    <div style="width: 85%">
                                      {{ wItem.amount}}元
                                    </div>
                                  
                                </div>
                              
                        </el-card>
                    </div>
                </el-col>
            </el-row>

            <!-- 新增 -->
            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="新增模板"
                    :visible.sync="dialogStatus"
                    width="50%"
                >
                    <div style="max-height: 600px; overflow-y: auto">
                        <Add ref="add" v-if="dialogStatus" @close="close"></Add>
                    </div>
                </el-dialog>
            </div>
            <!-- 编辑 -->
            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="编辑模板"
                    :visible.sync="dialogStatusView"
                    width="50%"
                >
                    <div style="max-height: 600px; overflow-y: auto">
                        <Views
                            ref="edit"
                            v-if="dialogStatusView"
                            :rowData="rowData"
                            @closeView="closeView"
                        ></Views>
                    </div>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: {
        Add,
        Views,
    },
    data() {
        return {
            rowData: {},
            dialogStatus: false,
            dialogStatusView: false,
            WayManageList: [],
          
            total: 0,
            courierOptions: [],
        };
    },
    mounted() {
        this.getWayManageList();
    },
    methods: {
        // 获取快递方式列表
        async getWayManageList() {
            let res = await this.$request.stock.getWayManageList(
               {}
            );
            console.log("快递方式管理列表", res);
            if (res.data.errorCode == 0) {
                this.WayManageList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        handleClose() {},
        choose(i, wItem) {
            this.$confirm("确定要修改快递方式模板吗？", "提示", {
                confirmButtonText: "确定",
                type: "success",
            })
                .then(() => {
                    this.$request.stock.changeStatus({
                        id: wItem.id,
                        status:1
                    }).then((res) => {
                        console.log("快递方式更新", res);
                        if (res.data.errorCode == 0) {
                            this.$Message.success("更新成功");
                            this.getWayManageList();
                        }
                    });
                })
                .catch(() => {
                    console.log("取消");
                });
        },
        close() {
            this.dialogStatus = false;
            this.getWayManageList();
        },
        closeView() {
            this.dialogStatusView = false;
            this.getWayManageList();
        },
        view(wItem) {
            console.log("编辑", wItem);
            this.dialogStatusView = true;
            this.rowData = wItem;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getWayManageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getWayManageList();
        },
    },
};
</script>
<style lang="scss" scoped>

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
.box {
    .module {
        margin: 20px 0;
    }
   
    .card {
        border: 3px solid #67c23a;
        // border-color: #67c23a;
    }
   
}
</style>
