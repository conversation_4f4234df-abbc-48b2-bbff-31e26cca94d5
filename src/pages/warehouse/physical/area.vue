<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-select v-model="column" placeholder="搜索条件">
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="keyword"
                    clearable
                    @keyup.enter.native="search"
                    placeholder="请输入关键字"
                ></el-input>
                <el-button
                    style="margin-left:10px"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
                <el-button style="margin-left:10px" @click="add" type="primary"
                    >新增分区</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    prop="area_name"
                    label="分区名称"
                    align="center"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    prop="area_code"
                    label="分区编码"
                    width="160"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="alter"
                    label="最后操作人"
                    width="160"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="applicant"
                    label="创建人"
                    width="160"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    align="center"
                    label="创建时间"
                    width="220"
                >
                </el-table-column>
                <el-table-column label="操作" width="140" align="center">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            @click="edit(row.row)"
                            type="primary"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <div class="dialog">
            <el-dialog title="仓库分区信息" :visible.sync="dialogFormVisible">
                <el-form :model="form" ref="form" :rules="rules">
                    <el-form-item
                        label="分区名称"
                        prop="area_name"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.area_name"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="分区编码"
                        :label-width="formLabelWidth"
                        prop="area_code"
                    >
                        <el-input
                            v-model="form.area_code"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submitForm" type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            form: {
                // eslint-disable-next-line camelcase
                area_name: "",
                // eslint-disable-next-line camelcase
                area_code: ""
                // eslint-disable-next-line camelcase
            },
            searchOptions: [
                {
                    value: "area_name",
                    label: "分区名称"
                },
                {
                    value: "area_code",
                    label: "分区编码"
                }
            ],
            column: "area_name",
            keyword: "",
            dialogFormVisible: false,
            type: "",
            page: 1,
            rules: {
                // eslint-disable-next-line camelcase
                area_name: [
                    {
                        required: true,
                        message: "请输入分区名称",
                        trigger: "blur"
                    }
                ],
                // eslint-disable-next-line camelcase
                area_code: [
                    {
                        required: true,
                        message: "请输入分区编码",
                        trigger: "blur"
                    }
                ]
            },
            limit: 10,
            total: 0,
            tableData: [],
            formLabelWidth: "120px"
        };
    },
    mounted() {
        this.getStockAreaList();
    },
    methods: {
        submitForm() {
            this.$refs.form.validate(valid => {
                console.log(valid);
                if (valid) {
                    this.submit();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        async submit() {
            if (this.type === "edit") {
                let data = {
                    ...this.form
                };
                let editRes = await this.$request.stock.updateStockAreaList(
                    data
                );
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockAreaList();
                }
            } else {
                let data = {
                    ...this.form
                };
                let addRes = await this.$request.stock.createStockAreaList(
                    data
                );
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockAreaList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getStockAreaList();
        },
        resetForm() {
            this.form = {
                // eslint-disable-next-line camelcase
                area_name: "",
                // eslint-disable-next-line camelcase
                area_code: ""
            };
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        add() {
            this.type = "add";
            this.resetForm();
            this.dialogFormVisible = true;
        },
        edit(row) {
            console.log(row);
            this.type = "edit";
            // eslint-disable-next-line camelcase
            this.form.area_name = row.area_name;
            // eslint-disable-next-line camelcase
            this.form.area_code = row.area_code;
            // eslint-disable-next-line camelcase
            this.form.area_id = row.area_id;
            this.dialogFormVisible = true;
        },
        getStockAreaList() {
            let data = {
                page: this.page,
                limit: this.limit,
                column: this.column,
                keyword: this.keyword
            };
            this.$request.stock.getStockAreaList(data).then(res => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        handleSizeChange(val) {
            this.limit = val;
            this.page = 1;
            console.log(`每页 ${val} 条`);
            this.getStockAreaList();
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getStockAreaList();
        }
    }
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
