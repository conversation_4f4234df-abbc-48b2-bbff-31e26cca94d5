<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader>
                <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                >
                <el-button style="margin-left:10px" @click="add" type="primary"
                    >新增货位</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="area_id.name" label="分区名称">
                </el-table-column>
                <el-table-column prop="location_id.name" label="库位名称">
                </el-table-column>
                <el-table-column prop="rack_id.name" label="货架名称">
                </el-table-column>
                <el-table-column prop="seat_code" label="货架编码">
                </el-table-column>
                <el-table-column label="存储属性">
                    <template slot-scope="row">
                        {{
                            propertyFormat(row.row.stora_attribute, "stora")[0]
                                .val
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="品质属性">
                    <template slot-scope="row">
                        {{
                            propertyFormat(
                                row.row.quality_attribute,
                                "quality"
                            )[0].val
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="功能属性">
                    <template slot-scope="row">
                        {{
                            propertyFormat(row.row.func_attribute, "func")[0]
                                .val
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="create_time" label="创建时间">
                </el-table-column>
                <el-table-column label="操作" width="80">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            @click="edit(row.row)"
                            type="primary"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <div class="dialog">
            <el-dialog title="仓库货位信息" :visible.sync="dialogFormVisible">
                <el-form :model="form">
                    <el-form-item
                        label="所属货架"
                        :label-width="formLabelWidth"
                    >
                        <el-cascader
                            v-if="cascaderShow"
                            style="margin-right:10px"
                            :options="options"
                            v-model="cascader"
                            filterable
                            @change="getCheckedNodesForm"
                            clearable
                        ></el-cascader>
                    </el-form-item>

                    <el-form-item
                        label="货位编号"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.seat_code"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submit" type="primary">确 定</el-button>
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import { Loading } from "element-ui";
export default {
    data() {
        return {
            options: [],
            cascader: [],
            cascaderShow: true,
            form: {
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_id: "",
                // eslint-disable-next-line camelcase
                rack_id: "",
                // eslint-disable-next-line camelcase
                seat_code: ""
                // eslint-disable-next-line camelcase
            },
            keyword: "",
            dialogFormVisible: false,
            type: "",
            // eslint-disable-next-line camelcase
            area_id: "",
            // eslint-disable-next-line camelcase
            rack_id: "",
            // eslint-disable-next-line camelcase
            location_id: "",
            page: 1,
            typeList: {},
            limit: 10,
            total: 0,
            tableData: [],
            formLabelWidth: "120px"
        };
    },
    mounted() {
        this.getStockStorageType();
        this.getStockPlaceList();
        this.getStockLinkage();
    },
    methods: {
        getStockLinkage() {
            Loading.service();
            this.$request.stock.getStockLinkage(2).then(res => {
                this.options = res.data.data;
                Loading.service().close();
            });
        },
        async getStockStorageType() {
            let res = await this.$request.stock.getStockStorageType();
            if (res.data.errorCode == 0) {
                console.log();
                this.typeList = res.data.data;
            }
        },
        async submit() {
            if (this.type === "edit") {
                let data = {
                    ...this.form
                };
                console.log(data);
                let editRes = await this.$request.stock.updateStockPlaceList(
                    data
                );
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockPlaceList();
                }
            } else {
                let data = {
                    ...this.form
                };
                console.log(data);
                let addRes = await this.$request.stock.createStockPlaceList(
                    data
                );
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockPlaceList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getStockPlaceList();
        },
        resetForm() {
            this.cascader = [];
            this.form = {
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_id: "",
                // eslint-disable-next-line camelcase
                rack_id: "",
                // eslint-disable-next-line camelcase
                seat_code: ""
                // eslint-disable-next-line camelcase
            };
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        propertyFormat(val, type) {
            if (type === "stora") {
                // let a ;
                return this.typeList.stora.filter(type => type.key == val);
                // return a;
                // console.log(a);
            } else if (type === "quality") {
                return this.typeList.quality.filter(type => type.key == val);
            } else if (type === "func") {
                return this.typeList.func.filter(type => type.key == val);
            }
        },
        add() {
            this.type = "add";
            this.resetForm();
            this.dialogFormVisible = true;
        },
        getCheckedNodesForm(val) {
            console.log(val);

            // eslint-disable-next-line camelcase
            this.form.area_id = val[0];
            // eslint-disable-next-line camelcase
            this.form.location_id = val[1];
            // eslint-disable-next-line camelcase
            this.form.rack_id = val[2];
        },
        edit(row) {
            this.cascaderShow = false;
            this.cascader = [
                row.area_id.id,
                row.location_id.id,
                row.rack_id.id
            ];
            setTimeout(() => {
                this.cascaderShow = true;
            }, 100);
            // this.form = row;
            // eslint-disable-next-line camelcase
            this.form.rack_id = row.rack_id.id;
            // eslint-disable-next-line camelcase
            this.form.area_id = row.area_id.id;
            // eslint-disable-next-line camelcase
            this.form.location_id = row.location_id.id;
            // eslint-disable-next-line camelcase
            this.form.seat_id = row.seat_id;

            // eslint-disable-next-line camelcase
            this.form.seat_code = row.seat_code;

            console.log(row);
            this.type = "edit";
            this.dialogFormVisible = true;
        },
        getStockPlaceList() {
            let data = {
                page: this.page,
                limit: this.limit,
                keyword: this.keyword,
                // eslint-disable-next-line camelcase
                area_id: this.area_id,
                // eslint-disable-next-line camelcase
                location_id: this.location_id,
                // eslint-disable-next-line camelcase
                rack_id: this.rack_id
            };
            this.$request.stock.getStockPlaceList(data).then(res => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        getCheckedNodes(val) {
            console.log(val);
            if (val[0]) {
                // eslint-disable-next-line camelcase
                this.area_id = val[0];
            } else {
                // eslint-disable-next-line camelcase
                this.area_id = "";
            }
            if (val[1]) {
                // eslint-disable-next-line camelcase
                this.location_id = val[1];
            } else {
                // eslint-disable-next-line camelcase
                this.location_id = "";
            }
            if (val[2]) {
                // eslint-disable-next-line camelcase
                this.rack_id = val[2];
            } else {
                // eslint-disable-next-line camelcase
                this.rack_id = "";
            }
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getStockPlaceList();
        }
    }
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
