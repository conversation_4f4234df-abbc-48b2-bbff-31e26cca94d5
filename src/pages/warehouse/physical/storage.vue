<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-select
                    style="margin-right: 10px"
                    v-model="area_id"
                    clearable
                    placeholder="请选择所属分区"
                >
                    <el-option
                        v-for="item in areaList"
                        :key="item.area_id"
                        :label="item.area_name"
                        :value="item.area_id"
                    >
                    </el-option>
                </el-select>
                <el-select v-model="column" placeholder="搜索条件">
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    placeholder="请输入关键字"
                    v-model="keyword"
                    @keyup.enter.native="search"
                    clearable
                ></el-input>
                <el-button
                    style="margin-left: 10px"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
                <el-button style="margin-left: 10px" @click="add" type="primary"
                    >新增库位</el-button
                >
                <el-button
                    style="margin-left: 10px"
                    @click="wranDialogstatus = true"
                    :disabled="multipleSelection.length == 0"
                    type="success"
                    >批量修改预警值</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table
                :data="tableData"
                border
                label-width="120px"
                style="width: 100%"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column
                    prop="corp_name"
                    label="公司"
                    align="center"
                    min-width="140"
                >
                </el-table-column>
                <el-table-column
                    prop="area_id.name"
                    label="分区名称"
                    align="center"
                    min-width="120"
                >
                </el-table-column>
                <el-table-column
                    prop="location_code"
                    label="库位编号"
                    align="center"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="可用数量" width="100" align="center">
                    <template slot-scope="row">
                        {{ row.row.goods_available_count | goodsNumberFormat }}
                    </template>
                </el-table-column>

                <el-table-column label="实际数量" width="100" align="center">
                    <template slot-scope="row">
                        {{ row.row.goods_actual_count | goodsNumberFormat }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="warning"
                    label="预警值"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="存储属性" width="90" align="center">
                    <template slot-scope="row">
                        {{
                            propertyFormat(row.row.stora_attribute, "stora")[0]
                                .val
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="品质属性" width="90" align="center">
                    <template slot-scope="row">
                        {{
                            propertyFormat(
                                row.row.quality_attribute,
                                "quality"
                            )[0].val
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="功能属性" width="90" align="center">
                    <template slot-scope="row">
                        {{
                            propertyFormat(row.row.func_attribute, "func")[0]
                                .val
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="alter"
                    label="最后操作人"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="applicant"
                    label="创建人"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    width="170"
                    align="center"
                    label="创建时间"
                >
                </el-table-column>
                <el-table-column label="操作" width="200" align="center">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            @click="edit(row.row)"
                            type="primary"
                            >编辑</el-button
                        >
                        <el-button
                            size="mini"
                            @click="viewGoodsTable(row.row)"
                            type="warning"
                            >查看库位商品</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <div class="dialog">
            <el-dialog title="仓库库位信息" :visible.sync="dialogFormVisible">
                <el-form :model="form" ref="formDom">
                    <el-form-item
                        label="分区"
                        :label-width="formLabelWidth"
                        prop="area_id"
                        :rules="[
                            {
                                required: true,
                                message: '请选择所属分区',
                                trigger: 'change',
                            },
                        ]"
                    >
                        <el-select
                            v-model="form.area_id"
                            placeholder="请选择所属分区"
                        >
                            <el-option
                                v-for="item in areaList"
                                :key="item.area_id"
                                :label="item.area_name"
                                :value="item.area_id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item
                        prop="warning"
                        label="预警值"
                        :label-width="formLabelWidth"
                        :rules="[{ required: true, message: '预警值不能为空' }]"
                    >
                        <el-input-number
                            v-model="form.warning"
                            :step="1"
                            :min="0"
                            :max="1000000"
                            label="预警值"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item
                        prop="location_code"
                        label="库位编码"
                        :rules="[
                            {
                                required: true,
                                message: '库位编码不能为空',
                                trigger: 'change',
                            },
                        ]"
                        v-if="type == 'add'"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.location_code"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        prop="stora_attribute"
                        label="存储属性"
                        :rules="[
                            {
                                required: true,
                                message: '请选择存储属性',
                                trigger: 'change',
                            },
                        ]"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.stora_attribute"
                            placeholder="请选择存储属性"
                        >
                            <el-option
                                v-for="item in typeList.stora"
                                :key="item.key"
                                :label="item.val"
                                :value="item.key"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        prop="quality_attribute"
                        label="品质属性"
                        :rules="[
                            {
                                required: true,
                                message: '请选择品质属性',
                                trigger: 'change',
                            },
                        ]"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.quality_attribute"
                            placeholder="请选择品质属性"
                        >
                            <el-option
                                v-for="item in typeList.quality"
                                :key="item.key"
                                :label="item.val"
                                :value="item.key"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        prop="func_attribute"
                        label="功能属性"
                        :rules="[
                            {
                                required: true,
                                message: '请选择功能属性',
                                trigger: 'change ',
                            },
                        ]"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.func_attribute"
                            placeholder="请选择功能属性"
                        >
                            <el-option
                                v-for="item in typeList.func"
                                :key="item.key"
                                :label="item.val"
                                :value="item.key"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="公司"
                        prop="corp"
                        :label-width="formLabelWidth"
                        :rules="[
                            {
                                required: true,
                                message: '请选择公司',
                                trigger: 'change',
                            },
                        ]"
                    >
                        <CompanySelectGroup
                            v-model="form.corp"
                        ></CompanySelectGroup>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submitForm('formDom')" type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
        <el-dialog
            title="修改预警值"
            :visible.sync="wranDialogstatus"
            width="30%"
            center
        >
            <div style="text-align: center">
                <el-input-number
                    v-model="warnNumber"
                    :min="1"
                    :max="9999999"
                    label="请输入预警值"
                ></el-input-number>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="wranDialogstatus = false">取 消</el-button>
                <el-button type="primary" @click="updateWranSubmit"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="查看商品"
            :visible.sync="viewGoodsDialogStatus"
            width="70%"
            center
        >
            <div class="area-layout">
                <div class="form">
                    <div class="search">
                        <el-select
                            v-model="viewGoods.column"
                            placeholder="搜索条件"
                        >
                            <el-option
                                v-for="item in viewGoods.searchOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <el-input
                            v-model="viewGoods.keyword"
                            clearable
                            placeholder="请输入关键字"
                        ></el-input>
                        <el-button
                            style="margin-left: 10px"
                            type="warning"
                            @click="viewGoodsSearch"
                            >查询</el-button
                        >
                    </div>
                </div>
                <div class="area-main">
                    <el-table
                        :data="viewGoods.tableData"
                        stripe
                        border
                        style="width: 100%"
                    >
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-form
                                    label-position="left"
                                    inline
                                    class="demo-table-expand"
                                >
                                    <el-form-item label="分区名称">
                                        <span>{{ props.row.area_name }}</span>
                                    </el-form-item>
                                    <el-form-item label="分区编号">
                                        <span>{{ props.row.area_code }}</span>
                                    </el-form-item>

                                    <el-form-item label="库位编号">
                                        <span>{{
                                            props.row.location_code
                                        }}</span>
                                    </el-form-item>
                                    <el-form-item label="库位预警值">
                                        <span>{{ props.row.warning }}</span>
                                    </el-form-item>
                                    <!-- <el-form-item label="采摘年份">
                                <span>{{ props.row.goods_years }}年</span>
                            </el-form-item>

                            <el-form-item label="保质期">
                                <span>{{ props.row.shelf_life }}天</span>
                            </el-form-item>
                            <el-form-item label="灌装日期">
                                <span>{{ props.row.produce_date }}</span>
                            </el-form-item>
                            <el-form-item label="容量">
                                <span>{{ props.row.capacity }}</span>
                            </el-form-item> -->
                                    <!-- <el-form-item label="商品形态">
                                <span>{{
                                    propertyFormat(
                                        "liquid",
                                        props.row.goods_form
                                    )
                                }}</span>
                            </el-form-item> -->
                                    <!-- <el-form-item label="是否赠品">
                                <span>{{
                                    propertyFormat(
                                        "give",
                                        props.row.is_giveaway
                                    )
                                }}</span>
                            </el-form-item> -->

                                    <el-form-item label="实际量">
                                        <span>{{
                                            props.row.actual_count
                                        }}</span>
                                    </el-form-item>
                                    <el-form-item label="可用量">
                                        <span>{{
                                            props.row.available_count
                                        }}</span>
                                    </el-form-item>

                                    <el-form-item label="创建时间">
                                        <span>{{ props.row.create_time }}</span>
                                    </el-form-item>
                                </el-form>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="bar_code"
                            width="140"
                            label="条码"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="short_code"
                            width="140"
                            align="center"
                            label="简码"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="actual_count"
                            width="100"
                            align="right"
                            label="实际量"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="goods_name"
                            label="中文品名"
                            min-width="350"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="en_goods_name"
                            label="英文品名"
                            align="center"
                            min-width="200"
                        >
                        </el-table-column>
                    </el-table>
                    <div class="block">
                        <el-pagination
                            background
                            @current-change="viewGoodsHandleCurrentChange"
                            @size-change="viewGoodsHandleSizeChange"
                            :current-page="viewGoods.page"
                            :page-size="viewGoods.limit"
                            :page-sizes="[10, 30, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="viewGoods.total"
                        >
                        </el-pagination>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import CompanySelectGroup from "@/components/CompanySelectGroup";
export default {
    components: {
        CompanySelectGroup,
    },
    filters: {
        goodsNumberFormat(val) {
            if (val) {
                return val;
            } else {
                return 0;
            }
        },
    },
    data() {
        return {
            viewGoods: {
                options: [],
                cascaderValue: [],
                keyword: "",
                searchOptions: [
                    {
                        value: "bar_code",
                        label: "商品条码",
                    },
                    {
                        value: "short_code",
                        label: "商品简码",
                    },
                    {
                        value: "goods_name",
                        label: "商品中文名称",
                    },
                    {
                        value: "en_goods_name",
                        label: "商品英文名称",
                    },
                ],
                column: "bar_code",
                page: 1,
                limit: 10,
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_id: "",
                total: 0,
                tableData: [],
                formLabelWidth: "120px",
            },
            viewGoodsDialogStatus: false,
            areaList: [],
            typeList: {},
            multipleSelection: [],
            form: {
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_code: "",
                // eslint-disable-next-line camelcase
                func_attribute: "",
                // eslint-disable-next-line camelcase
                stora_attribute: "",
                warning: 1,
                // eslint-disable-next-line camelcase
                quality_attribute: "",
                corp: "",
            },
            keyword: "",
            dialogFormVisible: false,
            wranDialogstatus: false,
            type: "",
            page: 1,
            limit: 10,
            total: 0,
            searchOptions: [
                {
                    value: "location_code",
                    label: "库位编号",
                },
                {
                    value: "applicant",
                    label: "创建人",
                },
                {
                    value: "alter",
                    label: "最后操作人",
                },
            ],
            column: "location_code",
            // eslint-disable-next-line camelcase
            area_id: "",
            warnNumber: 1,
            tableData: [],
            formLabelWidth: "120px",
        };
    },
    mounted() {
        this.getStockStorageList();
        this.getStockAreaList();
        this.getStockStorageType();
    },
    methods: {
        viewGoodsSearch() {
            this.page = 1;
            this.getStorageGoodsPhysical();
        },
        getStorageGoodsPhysical() {
            let data = {
                page: this.viewGoods.page,
                limit: this.viewGoods.limit,
                keyword: this.viewGoods.keyword,
                column: this.viewGoods.column,
                // eslint-disable-next-line camelcase
                area_id: this.viewGoods.area_id,
                // eslint-disable-next-line camelcase
                location_id: this.viewGoods.location_id,
            };
            this.$request.goods.getStorageGoodsPhysical(data).then((res) => {
                console.log(res.data);
                if (res.data.errorCode == 0) {
                    this.viewGoods.total = res.data.data.totalnum;
                    this.viewGoods.tableData = res.data.data.list;
                }
            });
        },
        viewGoodsHandleSizeChange(val) {
            console.log(`每页 ${val} 条`);

            this.viewGoods.limit = val;
            this.viewGoods.page = 1;
            this.getStorageGoodsPhysical();
        },
        viewGoodsHandleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.viewGoods.page = val;
            this.getStorageGoodsPhysical();
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.submit();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        viewGoodsTable(row) {
            this.viewGoodsDialogStatus = true;
            this.viewGoods.area_id = row.area_id.id;
            this.viewGoods.location_id = row.location_id;
            this.getStorageGoodsPhysical();
        },
        propertyFormat(val, type) {
            if (type === "stora") {
                // let a ;
                if (this.typeList.stora) {
                    return this.typeList.stora.filter(
                        (type) => type.key == val
                    );
                } else {
                    return "";
                }
                // return a;
                // console.log(a);
            } else if (type === "quality") {
                if (this.typeList.quality) {
                    return this.typeList.quality.filter(
                        (type) => type.key == val
                    );
                } else {
                    return "";
                }
            } else if (type === "func") {
                if (this.typeList.func) {
                    return this.typeList.func.filter((type) => type.key == val);
                } else {
                    return "";
                }
            }
        },
        resetForm() {
            this.form = {
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_code: "",
                warning: 1,
                // eslint-disable-next-line camelcase
                func_attribute: "",
                // eslint-disable-next-line camelcase
                stora_attribute: "",
                // eslint-disable-next-line camelcase
                quality_attribute: "",
                corp: "",
            };
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        updateWranSubmit() {
            let warningArr = [];
            this.multipleSelection.map((i) => {
                let obj = {
                    id: i.location_id,
                    warning: this.warnNumber,
                };
                warningArr.push(obj);
            });
            console.log(warningArr);
            let data = {
                warningArr,
            };
            this.$request.stock.updateWarning(data).then((Res) => {
                console.log(Res);
                if (Res.data.errorCode == 0) {
                    this.wranDialogstatus = false;
                    this.getStockStorageList();
                    this.$message.success("操作成功");
                }
            });

            // this.warnNumber
        },
        async getStockStorageType() {
            let res = await this.$request.stock.getStockStorageType();
            if (res.data.errorCode == 0) {
                console.log();
                this.typeList = res.data.data;
            }
        },
        getStockAreaList() {
            let data = {
                page: 1,
                limit: 9999,
            };
            this.$request.stock.getStockAreaList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.areaList = res.data.data.list;
                }
            });
        },
        async submit() {
            if (this.type === "edit") {
                let data = {
                    ...this.form,
                };
                console.log(data);

                let editRes = await this.$request.stock.updateStockStorageList(
                    data
                );
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockStorageList();
                }
            } else {
                let data = {
                    // eslint-disable-next-line camelcase
                    area_id: this.form.area_id,
                    // eslint-disable-next-line camelcase
                    location_code: this.form.location_code,
                    warning: this.form.warning,
                    // eslint-disable-next-line camelcase
                    stora_attribute: this.form.stora_attribute,
                    // eslint-disable-next-line camelcase
                    quality_attribute: this.form.quality_attribute,
                    // eslint-disable-next-line camelcase
                    func_attribute: this.form.func_attribute,
                    corp: this.form.corp,
                };
                console.log(data);
                let addRes = await this.$request.stock.createStockStorageList(
                    data
                );
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockStorageList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getStockStorageList();
        },
        add() {
            this.resetForm();
            this.type = "add";
            this.dialogFormVisible = true;
        },
        edit(row) {
            console.log(row);
            this.type = "edit";
            // this.form = row;
            // eslint-disable-next-line camelcase
            this.form.area_id = row.area_id.id;
            this.form.warning = row.warning;
            // eslint-disable-next-line camelcase
            this.form.location_code = row.location_code;
            // eslint-disable-next-line camelcase
            this.form.stora_attribute = row.stora_attribute;
            // eslint-disable-next-line camelcase
            this.form.quality_attribute = row.quality_attribute;
            // eslint-disable-next-line camelcase
            this.form.func_attribute = row.func_attribute;
            // eslint-disable-next-line camelcase
            this.form.location_id = row.location_id;
            this.form.corp = row.corp;
            // this.form.area_id = row.area_id.id;

            this.dialogFormVisible = true;
        },
        getStockStorageList() {
            let data = {
                page: this.page,
                limit: this.limit,
                column: this.column,
                // eslint-disable-next-line camelcase
                area_id: this.area_id,
                keyword: this.keyword,
            };
            this.$request.stock.getStockStorageList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);

            this.limit = val;
            this.page = 1;
            this.getStockStorageList();
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getStockStorageList();
        },
    },
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.product-imgs {
    display: flex;
    & > div {
        text-align: center;
        margin-right: 10px;
    }
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
