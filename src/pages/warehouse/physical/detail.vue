<template>
    <div>
        <div style="margin-bottom:10px">
            <el-button @click="getDetails" type="primary"
                >同步更新仓库</el-button
            >
        </div>
        <el-table :data="wareList" border style="width: 100%">
            <el-table-column
                prop="stock_id"
                label="仓库编号"
                width="300"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="stock_name"
                label="仓库名称"
                width="180"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="contacts_name"
                label="联系人"
                min-width="130"
                align="center"
            >
            </el-table-column
            ><el-table-column
                prop="contacts_phone"
                label="联系电话"
                width="140"
                align="center"
            >
            </el-table-column
            ><el-table-column
                prop="contact"
                width="140"
                align="center"
                label="寄件人或公司
"
            >
            </el-table-column
            ><el-table-column
                prop="postCode"
                label="邮政编码"
                width="90"
                align="center"
            >
            </el-table-column
            ><el-table-column
                prop="tel"
                label="寄件电话"
                width="140"
                align="center"
            >
            </el-table-column>
            <el-table-column
                width="220"
                label="所在地（省市区）"
                align="center"
            >
                <template slot-scope="row">
                    <div
                        v-if="
                            row.row.province && row.row.city && row.row.county
                        "
                    >
                        {{ row.row.province }}/{{ row.row.city }}/{{
                            row.row.county
                        }}
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                prop="address"
                label="详细地址"
                min-width="400"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="create_time"
                label="创建时间"
                width="160"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="update_time"
                label="更新时间"
                width="160"
                align="center"
            >
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
                <template slot-scope="row">
                    <el-button type="primary" size="mini" @click="edit(row.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div class="block">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-size="limit"
                background
                :page-sizes="[10, 30, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="编辑仓库"
            :visible.sync="centerDialogVisible"
            width="40%"
            center
        >
            <el-form
                :model="createForm"
                :rules="createrules"
                ref="createrules"
                label-width="140px"
            >
                <el-form-item label="仓库名称" prop="stock_name">
                    <el-input v-model="createForm.stock_name"></el-input>
                </el-form-item>
                <el-form-item label="联系人" prop="contacts_name">
                    <el-input v-model="createForm.contacts_name"></el-input>
                </el-form-item>
                <el-form-item label="联系人电话" prop="contacts_phone">
                    <el-input v-model="createForm.contacts_phone"></el-input>
                </el-form-item>
                <el-form-item label="寄件人或公司" prop="contact">
                    <el-input v-model="createForm.contact"></el-input>
                </el-form-item>
                <el-form-item label="邮编" prop="postCode">
                    <el-input v-model="createForm.postCode"></el-input>
                </el-form-item>
                <el-form-item label="寄件电话" prop="tel">
                    <el-input v-model="createForm.tel"></el-input>
                </el-form-item>
                <el-form-item label="仓库地址" prop="selectedOptions">
                    <el-cascader
                        size="large"
                        :options="options"
                        style="width:300px"
                        v-model="createForm.selectedOptions"
                        @change="handleChange"
                    >
                    </el-cascader>
                </el-form-item>
                <el-form-item label="详细地址" prop="address">
                    <el-input v-model="createForm.address"></el-input>
                </el-form-item>
                <el-form-item label="权限密码" prop="auth_password">
                    <el-input
                        v-model="auth_password"
                        type="password"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="centerDialogVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="save('createrules')"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { regionData, CodeToText, TextToCode } from "element-china-area-data";
export default {
    data() {
        return {
            page: 1,
            total: 0,
            centerDialogVisible: false,
            options: regionData,

            limit: 10,
            createForm: {
                selectedOptions: [],
                authorization_url: window.location.host + "/",
                address: "",
                county: "",
                city: "",
                province: "",
                tel: "",
                postCode: "",
                contact: "",
                contacts_phone: "",
                contacts_name: "",
                stock_name: ""
            },
            auth_password: "",

            wareList: [],
            createrules: {
                selectedOptions: [
                    {
                        type: "array",
                        required: true,
                        message: "请选择收件地",
                        trigger: "change"
                    }
                ],
                address: [
                    {
                        required: true,
                        message: "请输入详细地址",
                        trigger: "blur"
                    }
                ],
                tel: [
                    {
                        required: true,
                        message: "请输入寄件电话",
                        trigger: "blur"
                    }
                ],
                postCode: [
                    {
                        required: true,
                        message: "请输入邮政编码",
                        trigger: "blur"
                    }
                ],
                contact: [
                    {
                        required: true,
                        message: "请输入寄件人或公司",
                        trigger: "blur"
                    }
                ],
                contacts_phone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur"
                    },
                    {
                        pattern: /^1[3-9](\d{9})$/,
                        message: "请输入正确的手机号",
                        trigger: ["change"]
                    },
                    { max: 11, message: "请输入11位手机号", trigger: "change" }
                ],

                contacts_name: [
                    {
                        required: true,
                        message: "请输入仓库联系人",
                        trigger: "blur"
                    }
                ],
                stock_name: [
                    {
                        required: true,
                        message: "请输入仓库名称",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getDetails();
    },
    methods: {
        save(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        ...this.createForm
                    };
                    if (this.auth_password) {
                        data.auth_password = this.auth_password;
                    } else {
                        delete data["auth_password"];
                    }
                    console.log(data);
                    this.$request.stock
                        .updateStockDetailPhysical(data)
                        .then(res => {
                            console.log(res);
                            if (res.data.errorCode == 0) {
                                this.$message.success("操作成功");
                                this.getDetails();
                                this.centerDialogVisible = false;
                            }
                        });
                }
            });
        },
        handleChange(value) {
            value.map((item, index) => {
                console.log(index);
                switch (index) {
                    case 0:
                        this.createForm.province = CodeToText[item];
                        break;
                    case 1:
                        this.createForm.city = CodeToText[item];
                        break;
                    case 2:
                        this.createForm.county = CodeToText[item];
                        break;
                }
            });
        },
        openEditAddress(row) {
            this.createForm.selectedOptions = [];
            // eslint-disable-next-line no-unused-vars
            let province = TextToCode[row.province].code;
            // eslint-disable-next-line no-unused-vars
            let city = TextToCode[row.province][row.city].code;
            // eslint-disable-next-line no-unused-vars
            let county = TextToCode[row.province][row.city][row.county].code;
            console.log(province, city, county);
            this.auth_password = "";
            this.createForm.auth_password = "";
            // [province, city, county];
            this.$set(this.createForm.selectedOptions, 0, province);
            this.$set(this.createForm.selectedOptions, 1, city);
            this.$set(this.createForm.selectedOptions, 2, county);
        },
        edit(row) {
            this.centerDialogVisible = true;
            this.createForm = row;
            this.openEditAddress(row);
        },
        handleSizeChange(val) {
            this.page = 1;
            this.limit = val;
            this.getDetails();
            console.log(`每页 ${val} `);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getDetails();
        },
        async getDetails() {
            let data = {
                page: this.page,
                limit: this.limit
            };
            let res = await this.$request.stock.getStockDetailPhysical(data);
            if (res.data.errorCode == 0) {
                this.total = res.data.data.totalnum;
                this.wareList = res.data.data.list;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.details-main {
    & > div {
        display: flex;
        align-items: center;
        padding: 10px 0;
        justify-content: space-between;
    }
}
.block {
    text-align: center;
    margin-top: 10px;
}
.text {
    font-size: 14px;
}

.item {
    margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}
.clearfix:after {
    clear: both;
}

.box-card {
    width: 800px;
}
</style>
