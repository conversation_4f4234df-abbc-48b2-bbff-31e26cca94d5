<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader>
                <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                >
                <el-button style="margin-left:10px" @click="add" type="primary"
                    >新增货架</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="rack_name" label="货架名称">
                </el-table-column>
                <el-table-column prop="area_id.name" label="分区名称">
                </el-table-column>
                <el-table-column prop="location_id.name" label="库位名称">
                </el-table-column>
                <el-table-column prop="rack_code" label="货架编号">
                </el-table-column>
                <el-table-column prop="create_time" label="创建时间">
                </el-table-column>
                <el-table-column label="操作" width="80">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            @click="edit(row.row)"
                            type="primary"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <div class="dialog">
            <el-dialog title="仓库货架信息" :visible.sync="dialogFormVisible">
                <el-form :model="form">
                    <el-form-item
                        label="所属库位"
                        :label-width="formLabelWidth"
                    >
                        <el-cascader
                            style="margin-right:10px"
                            :options="options"
                            filterable
                            v-if="cascaderShow"
                            v-model="cascader"
                            @change="getCheckedNodesForm"
                            clearable
                        ></el-cascader>
                    </el-form-item>
                    <el-form-item
                        label="货架名称"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.rack_name"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="货架编号"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.rack_code"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submit" type="primary">确 定</el-button>
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import { Loading } from "element-ui";
export default {
    data() {
        return {
            options: [],
            cascaderShow: true,
            cascader: [],
            form: {
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_id: "",
                // eslint-disable-next-line camelcase
                rack_name: "",
                // eslint-disable-next-line camelcase
                rack_code: ""
                // eslint-disable-next-line camelcase
            },
            keyword: "",
            dialogFormVisible: false,
            type: "",
            // eslint-disable-next-line camelcase
            area_id: "",
            // eslint-disable-next-line camelcase
            location_id: "",
            page: 1,
            limit: 10,
            total: 0,
            tableData: [],
            formLabelWidth: "120px"
        };
    },
    mounted() {
        this.getStockShelfList();
        this.getStockLinkage();
    },
    methods: {
        getStockLinkage() {
            Loading.service();
            this.$request.stock.getStockLinkage(1).then(res => {
                this.options = res.data.data;
                Loading.service().close();
            });
        },
        async submit() {
            if (this.type === "edit") {
                let data = {
                    ...this.form
                };
                console.log(data);
                let editRes = await this.$request.stock.updateStockShelfList(
                    data
                );
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockShelfList();
                }
            } else {
                let data = {
                    ...this.form
                };
                console.log(data);
                let addRes = await this.$request.stock.createStockShelfList(
                    data
                );
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getStockShelfList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getStockShelfList();
        },
        resetForm() {
            this.cascader = [];
            this.form = {
                // eslint-disable-next-line camelcase
                area_id: "",
                // eslint-disable-next-line camelcase
                location_id: "",
                // eslint-disable-next-line camelcase
                rack_name: "",
                // eslint-disable-next-line camelcase
                rack_code: ""
                // eslint-disable-next-line camelcase
            };
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        add() {
            this.type = "add";
            this.resetForm();
            this.dialogFormVisible = true;
        },
        getCheckedNodesForm(val) {
            console.log(val);
            // eslint-disable-next-line camelcase
            this.form.area_id = val[0];
            // eslint-disable-next-line camelcase
            this.form.location_id = val[1];
        },
        edit(row) {
            this.cascaderShow = false;
            this.cascader = [row.area_id.id, row.location_id.id];
            setTimeout(() => {
                this.cascaderShow = true;
            }, 100);

            // this.form = row;
            // this.cascader = [];
            // this.$set(this.cascader, 0, row.area_id.id);
            // this.cascader[0] = row.area_id.id;
            // this.cascader[1] = row.location_id.id;
            console.log(this.cascader, "xxx");
            // eslint-disable-next-line camelcase
            this.form.rack_id = row.rack_id;
            // eslint-disable-next-line camelcase
            this.form.area_id = row.area_id.id;
            // eslint-disable-next-line camelcase
            this.form.location_id = row.location_id.id;
            // eslint-disable-next-line camelcase
            this.form.rack_name = row.rack_name;
            // eslint-disable-next-line camelcase
            this.form.rack_code = row.rack_code;
            console.log(row);
            this.type = "edit";
            this.dialogFormVisible = true;
        },
        getStockShelfList() {
            let data = {
                page: this.page,
                limit: this.limit,
                keyword: this.keyword,
                // eslint-disable-next-line camelcase
                area_id: this.area_id,
                // eslint-disable-next-line camelcase
                location_id: this.location_id
            };
            this.$request.stock.getStockShelfList(data).then(res => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        getCheckedNodes(val) {
            if (val[0]) {
                // eslint-disable-next-line camelcase
                this.area_id = val[0];
            } else {
                // eslint-disable-next-line camelcase
                this.area_id = "";
            }
            if (val[1]) {
                // eslint-disable-next-line camelcase
                this.location_id = val[1];
            } else {
                // eslint-disable-next-line camelcase
                this.location_id = "";
            }
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getStockShelfList();
        }
    }
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
