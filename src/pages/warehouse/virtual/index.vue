<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-select v-model="column" placeholder="搜索条件">
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    style="margin-left: 4px"
                    v-model="keyword"
                    clearable
                    @keyup.enter.native="search"
                    placeholder="请输入关键字"
                ></el-input>
                <el-button
                    style="margin-left: 10px"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
                <el-button style="margin-left: 10px" @click="add" type="primary"
                    >新增虚拟仓库</el-button
                >
                <el-button
                    style="margin-left: 10px"
                    @click="openImportStatus = true"
                    type="success"
                    >批量调拨</el-button
                >
            </div>
        </div>
        <el-dialog
            center
            title="请选择需要导入的订单类型"
            :visible.sync="openImportStatus"
            width="40%"
        >
            <el-radio v-model="importValue" label="1"
                >虚拟仓调拨
                <el-link
                    href="https://images.vinehoo.com/wms/order/虚拟仓调拨模版.xlsx"
                    target="_blank"
                    type="warning"
                    >（下载模版<i class="el-icon-download el-icon--right"></i>）
                </el-link>
            </el-radio>

            <div class="el-upload__tip">只能上传xlsx文件，且不超过10MB</div>
            <span
                slot="footer"
                style="display: flex; justify-content: space-evenly"
                class="dialog-footer"
            >
                <div>
                    <el-button @click="openImportStatus = false"
                        >取 消</el-button
                    >
                </div>
                <el-upload
                    class="upload-demo"
                    action="/admin/stock/fictitious/allocationImport"
                    :multiple="false"
                    :show-file-list="false"
                    :data="uploadData"
                    :headers="headers"
                    name="exclFile"
                    :limit="999"
                    :on-success="onSuccessAnnex"
                    :before-upload="beforeAvatarUploadAnnex"
                >
                    <!-- :on-success="onSuccess" -->

                    <el-button type="primary">确 定</el-button>
                </el-upload>
            </span>
        </el-dialog>
        <div class="area-main">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    prop="fictitious_id"
                    label="虚拟仓ID"
                    align="center"
                    min-width="100"
                >
                </el-table-column>
                <el-table-column
                    prop="fictitious_name"
                    label="虚拟仓名称"
                    align="center"
                    min-width="260"
                >
                </el-table-column>
                <el-table-column
                    prop="owner"
                    label="属主"
                    align="center"
                    min-width="90"
                >
                    <template slot-scope="row">
                        {{ row.row.owner == 1 ? "自有" : "三方" }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="platform_ids"
                    label="平台"
                    show-overflow-tooltip
                    align="center"
                    width="200"
                >
                    <template slot-scope="row">
                        {{ platform_idsFormat(row.row.platform_ids) }}
                    </template>
                </el-table-column>
                <el-table-column label="良品总数" width="120" align="center">
                    <template slot-scope="row">
                        {{ row.row.goods_count | countFormat }}
                    </template>
                </el-table-column>
                <el-table-column label="次品总数" width="120" align="center">
                    <template slot-scope="row">
                        {{ row.row.fake_count | countFormat }}
                    </template>
                </el-table-column>

                <el-table-column
                    align="center"
                    prop="contacts_name"
                    label="联系人"
                    width="120"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="contacts_phone"
                    label="手机号"
                    width="130"
                >
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    align="center"
                    label="创建时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column label="操作" width="310" align="center">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            @click="edit(row.row)"
                            type="primary"
                            >编辑</el-button
                        >
                        <el-button
                            size="mini"
                            v-if="row.row.fictitious_pid !== 0"
                            @click="shift(row.row)"
                            type="success"
                            >调拨</el-button
                        >
                        <el-button
                            size="mini"
                            @click="viewGoods(row.row)"
                            type="warning"
                            v-if="row.row.fictitious_pid !== 0"
                            >查看商品</el-button
                        >
                        <el-button
                            size="mini"
                            v-if="row.row.fictitious_pid !== 0"
                            @click="openReductDialog(row.row)"
                            type="danger"
                            >减库</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <div>
            <el-dialog title="虚拟仓库调拨" :visible.sync="shiftDialogStatus">
                <el-form :model="shiftData" ref="shiftData" :rules="shiftRules">
                    <el-form-item
                        label="调拨目的地仓库"
                        prop="allocation_fictitious_id"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            filterable
                            v-model="shiftData.allocation_fictitious_id"
                            placeholder="请选择仓库"
                        >
                            <el-option
                                v-for="item in fictitiousOptions"
                                :key="item.fictitious_id"
                                :disabled="item.fictitious_pid === 0"
                                :label="item.fictitious_name"
                                :value="item.fictitious_id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item
                        label="调拨商品"
                        :label-width="formLabelWidth"
                        prop="fic_goods_id"
                    >
                        <el-select
                            v-model="shiftData.fic_goods_id"
                            filterable
                            style="width: 70%"
                            remote
                            clearable
                            :loading="loadings"
                            reserve-keyword
                            placeholder="商品条码（至少3位）"
                            :remote-method="remoteMethod"
                        >
                            <el-option
                                v-for="item in fic_goods_idOptions"
                                :key="item.fic_goods_id"
                                :label="
                                    item.goods_name +
                                    '/' +
                                    item.corp_name +
                                    '/' +
                                    item.bar_code
                                "
                                :value="item.fic_goods_id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="调拨数量"
                        :label-width="formLabelWidth"
                        prop="number"
                    >
                        <el-input-number
                            v-model="shiftData.number"
                            :min="1"
                            :step="1"
                            label="请输入数量"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item
                        label="公司"
                        :label-width="formLabelWidth"
                        prop="corp"
                    >
                        <CompanySelectGroup
                            v-model="shiftData.corp"
                        ></CompanySelectGroup>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="shiftDialogStatus = false"
                        >取 消</el-button
                    >
                    <el-button @click="shiftSubmit('shiftData')" type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
        <div class="dialog">
            <el-dialog title="虚拟仓库信息" :visible.sync="dialogFormVisible">
                <el-form :model="form" ref="form" :rules="rules">
                    <el-form-item
                        label="虚拟仓库名"
                        prop="fictitious_name"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.fictitious_name"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="属主"
                        prop="owner"
                        :label-width="formLabelWidth"
                    >
                        <el-radio-group v-model="form.owner">
                            <el-radio :label="1">自有</el-radio>
                            <el-radio :label="2">三方</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="平台"
                        prop="platform_ids"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            clearable
                            multiple
                            v-model="form.platform_ids"
                            placeholder="请选择平台"
                        >
                            <el-option
                                v-for="item in platform_idsOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        v-if="type !== 'edit'"
                        label="所属上级"
                        prop="fictitious_pid"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            clearable
                            v-model="form.fictitious_pid"
                            placeholder="不选择默认顶级"
                        >
                            <el-option
                                v-for="item in fictitiousOptions"
                                :key="item.fictitious_id"
                                :disabled="item.fictitious_pid !== 0"
                                :label="item.fictitious_name"
                                :value="item.fictitious_id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="联系人"
                        :label-width="formLabelWidth"
                        prop="contacts_name"
                    >
                        <el-input
                            v-model="form.contacts_name"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="联系电话"
                        :label-width="formLabelWidth"
                        prop="contacts_phone"
                    >
                        <el-input
                            v-model="form.contacts_phone"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submitForm" type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
        <div class="dialog">
            <el-dialog title="减少库存" :visible.sync="reduceStatus">
                <el-form
                    label-width="120px"
                    :model="reduceForm"
                    :rules="ruduceRules"
                    ref="reduceForm"
                >
                    <el-form-item label="商品条码：" prop="bar_code">
                        <el-input
                            style="width: 300px; margin-right: 20px"
                            v-model="reduceForm.bar_code"
                            @blur="getLocationsByBarCode"
                        ></el-input>
                        <!-- <el-radio v-model="quality_attribute" :label="1"
                            >良品</el-radio
                        >
                        <el-radio v-model="quality_attribute" :label="2"
                            >次品</el-radio
                        > -->
                    </el-form-item>
                    <el-form-item label="选择库位：" prop="location_id">
                        <el-select
                            v-model="reduceForm.location_id"
                            placeholder="请先输入商品条码"
                            style="width: 300px"
                            :disabled="!locationOptions.length"
                        >
                            <el-option
                                v-for="item in locationOptions"
                                :key="item.location_id"
                                :label="`${item.location_code} / 可用数量：${item.available_count}`"
                                :value="item.location_id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="减少数量：" prop="nums">
                        <el-input-number
                            v-model="reduceForm.nums"
                            :precision="0"
                            :min="1"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="备注：" prop="remark">
                        <el-input
                            type="textarea"
                            placeholder="请输入内容"
                            show-word-limit
                            maxlength="80"
                            v-model="reduceForm.remark"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="公司" prop="corp">
                        <CompanySelectGroup
                            v-model="reduceForm.corp"
                        ></CompanySelectGroup>
                    </el-form-item>
                    <!-- 增加一个库位的下拉框 -->
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="reduceStatus = false">取 消</el-button>
                    <el-button
                        @click="reduceSubmit('reduceForm')"
                        type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
        <div class="dialog">
            <el-dialog
                title="查看商品"
                width="80%"
                :visible.sync="viewGoodsDialogStatus"
            >
                <div class="area-layout">
                    <div class="form">
                        <div class="search">
                            <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
                            <el-select
                                v-model="viewGoodsData.column"
                                placeholder="搜索条件"
                            >
                                <el-option
                                    v-for="item in viewGoodsData.searchOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-input
                                v-model="viewGoodsData.keyword"
                                clearable
                                placeholder="请输入关键字"
                            ></el-input>
                            <el-button
                                style="margin-left: 10px"
                                type="warning"
                                @click="viewGoodsSearch"
                                >查询</el-button
                            >
                        </div>
                    </div>
                    <div class="area-main">
                        <el-table
                            :data="viewGoodsData.tableData"
                            stripe
                            border
                            style="width: 100%"
                        >
                            <el-table-column type="expand">
                                <template slot-scope="props">
                                    <el-form
                                        label-position="left"
                                        inline
                                        class="demo-table-expand"
                                    >
                                        <!-- <el-form-item label="采摘年份">
                                <span>{{ props.row.goods_years }}年</span>
                            </el-form-item>

                            <el-form-item label="保质期">
                                <span>{{ props.row.shelf_life }}天</span>
                            </el-form-item>
                            <el-form-item label="灌装日期">
                                <span>{{ props.row.produce_date }}</span>
                            </el-form-item>
                            <el-form-item label="规格">
                                <span>{{ props.row.capacity }}</span>
                            </el-form-item> -->
                                        <!-- <el-form-item label="良品数量">
                                            <span>{{
                                                props.row.goods_count
                                            }}</span>
                                        </el-form-item>
                                        <el-form-item label="次品数量">
                                            <span>{{
                                                props.row.fake_count
                                            }}</span>
                                        </el-form-item> -->
                                        <el-form-item
                                            v-for="(item, index) in props.row
                                                .inventory"
                                            :key="index"
                                            :label="item.corp_name"
                                        >
                                            <span
                                                >可用数：{{
                                                    item.goods_count
                                                }}</span
                                            >
                                            <span style="margin-left: 10px"
                                                >在仓数：{{
                                                    item.total_count
                                                }}</span
                                            >
                                        </el-form-item>
                                    </el-form>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="bar_code"
                                width="140"
                                align="center"
                                label="条码"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="short_code"
                                width="140"
                                align="center"
                                label="简码"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="goods_name"
                                label="中文品名"
                                min-width="350"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="en_goods_name"
                                label="英文品名"
                                min-width="200"
                                align="center"
                            >
                            </el-table-column>
                        </el-table>
                        <div class="block">
                            <el-pagination
                                background
                                @current-change="viewGoodsHandleCurrentChange"
                                :current-page="viewGoodsData.page"
                                :page-size="viewGoodsData.limit"
                                @size-change="viewGoodsHandleSizeChange"
                                :page-sizes="[10, 30, 50, 100]"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="viewGoodsData.total"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import { Loading } from "element-ui";
import Cookies from "js-cookie";
import CompanySelectGroup from "@/components/CompanySelectGroup";

export default {
    components: {
        CompanySelectGroup,
    },
    filters: {
        countFormat(val) {
            if (val) {
                return val;
            } else {
                return 0;
            }
        },
    },
    data() {
        return {
            openImportStatus: false,
            platform_idsOptions: [],
            importValue: "1",
            headers: {
                warehousecheckval: Cookies.get("stock_id"),
                securitycheckval: Cookies.get("token"),
            },
            viewGoodsData: {
                options: [],
                keyword: "",
                searchOptions: [
                    {
                        value: "bar_code",
                        label: "商品条码",
                    },
                    {
                        value: "short_code",
                        label: "商品简码",
                    },

                    {
                        value: "en_goods_name",
                        label: "商品英文名称",
                    },
                    {
                        value: "goods_name",
                        label: "商品中文名称",
                    },
                ],
                column: "bar_code",
                page: 1,
                limit: 10,
                // eslint-disable-next-line camelcase
                fictitious_id: "",
                // eslint-disable-next-line camelcase
                total: 0,
                tableData: [],
                formLabelWidth: "120px",
            },
            viewGoodsDialogStatus: false,
            ruduceRules: {
                bar_code: [
                    {
                        required: true,
                        message: "请输入商品条码",
                        trigger: "blur",
                    },
                ],
                location_id: [
                    {
                        required: true,
                        message: "请选择库位",
                        trigger: "change",
                    },
                ],
                remark: [
                    {
                        required: true,
                        message: "请输入备注信息",
                        trigger: "blur",
                    },
                ],
                corp: [
                    {
                        required: true,
                        message: "请选择公司",
                        trigger: "change",
                    },
                ],
            },
            quality_attribute: 1,

            reduceForm: {
                remark: "",
                fictitious_id: "",
                bar_code: "",
                nums: "",
                corp: "",
                location_id: "", // 添加库位ID字段
            },
            locationOptions: [], // 库位选项列表
            reduceStatus: false,
            fictitious_id: "",
            fic_goods_idOptions: [],
            shiftData: {
                fic_goods_id: "",
                allocation_fictitious_id: "",
                number: 1,
                corp: "",
            },
            shiftDialogStatus: false,
            fictitiousOptions: [],
            form: {
                owner: "",
                platform_ids: [],
                // eslint-disable-next-line camelcase
                fictitious_name: "",
                // eslint-disable-next-line camelcase
                fictitious_pid: "",
                // eslint-disable-next-line camelcase
                contacts_name: "",
                // eslint-disable-next-line camelcase
                contacts_phone: "",
                // eslint-disable-next-line camelcase
            },
            searchOptions: [
                {
                    value: "fictitious_name",
                    label: "虚拟仓名称",
                },
                {
                    value: "contacts_name",
                    label: "联系人",
                },
                {
                    value: "contacts_phone",
                    label: "联系人手机",
                },
            ],
            loadings: false,

            column: "fictitious_name",
            keyword: "",
            shiftRules: {
                fic_goods_id: [
                    {
                        required: true,
                        message: "请选择商品",
                        trigger: "blur",
                    },
                ],
                allocation_fictitious_id: [
                    {
                        required: true,
                        message: "请选择虚拟仓库",
                        trigger: "blur",
                    },
                ],
                number: [
                    {
                        required: true,
                        message: "请输入虚拟仓名称",
                        trigger: "blur",
                    },
                ],
                corp: [
                    {
                        required: true,
                        message: "请选择公司",
                        trigger: "blur",
                    },
                ],
            },
            dialogFormVisible: false,
            type: "",
            page: 1,
            rules: {
                // eslint-disable-next-line camelcase
                fictitious_name: [
                    {
                        required: true,
                        message: "请输入虚拟仓名称",
                        trigger: "blur",
                    },
                ],
                platform_ids: [
                    {
                        required: true,
                        message: "请选择平台",
                        trigger: "blur",
                    },
                ],
                owner: [
                    {
                        required: true,
                        message: "请选择属主",
                        trigger: "blur",
                    },
                ],
                // // eslint-disable-next-line camelcase
                // fictitious_pid: [
                //     {
                //         required: true,
                //         message: "选择所属上级",
                //         trigger: "blur"
                //     }
                // ],
                // eslint-disable-next-line camelcase
                contacts_name: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                contacts_phone: [
                    {
                        required: true,
                        message: "请输入联系人手机号",
                        trigger: "blur",
                    },
                ],
            },
            limit: 10,
            total: 0,
            tableData: [],

            formLabelWidth: "130px",
        };
    },
    mounted() {
        this.getPlatformList();
        this.getVirtualList();
    },
    methods: {
        platform_idsFormat(val) {
            if (val.length) {
                let arr = [];
                val.map((i) => {
                    console.log(this.platform_idsOptions);
                    this.platform_idsOptions.find((f) => {
                        if (i == f.id) {
                            arr.push(f.name);
                        }
                    });
                });
                return arr.join("，");
            } else {
                return "-";
            }
        },
        async getPlatformList() {
            let res = await this.$request.order.getPlatformList();
            if (res.data.errorCode == 0) {
                console.log(res);
                this.platform_idsOptions = res.data.data.list;
            }
        },
        viewGoodsHandleSizeChange(val) {
            console.log(`每页 ${val} 条`);

            this.viewGoodsData.limit = val;
            this.viewGoodsData.page = 1;
            this.getStorageGoodsVirtual();
        },
        viewGoodsHandleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.viewGoodsData.page = val;
            this.getStorageGoodsVirtual();
        },
        reduceSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$confirm(
                        "此操作将会造成库存不可逆, 是否继续?",
                        "提示",
                        {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                        }
                    )
                        .then(() => {
                            let data = {
                                ...this.reduceForm,
                                // quality_attribute: this.quality_attribute,
                            };
                            this.$request.stock
                                .reduceCount(data)
                                .then((res) => {
                                    if (res.data.errorCode == 0) {
                                        this.$message.success("操作成功");
                                        this.getVirtualList();
                                        this.reduceStatus = false;
                                        this.quality_attribute = 1;
                                        this.reduceForm = {
                                            remark: "",
                                            fictitious_id: "",
                                            bar_code: "",
                                            nums: "",
                                            corp: "",
                                            location_id: "",
                                        };
                                        this.locationOptions = [];
                                    }
                                });
                        })
                        .catch(() => {
                            this.$message({
                                type: "info",
                                message: "已取消",
                            });
                        });
                    console.log(this.reduceForm);
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        openReductDialog(row) {
            this.reduceForm = {
                remark: "",
                fictitious_id: "",
                bar_code: "",
                nums: "",
                corp: "",
                location_id: "",
            };
            this.locationOptions = []; // 清空库位选项
            console.log(row);
            this.reduceForm.fictitious_id = row.fictitious_id;
            this.reduceStatus = true;
        },
        // 根据条码获取库位信息
        async getLocationsByBarCode() {
            if (!this.reduceForm.bar_code) {
                this.locationOptions = [];
                this.reduceForm.location_id = "";
                return;
            }

            try {
                const res = await this.$request.stock.getLocationInfo({
                    bar_code: this.reduceForm.bar_code,
                });
                if (res.data.errorCode === 0 && res.data.data.location) {
                    this.locationOptions = res.data.data.location;
                    // 如果只有一个库位，自动选中
                    if (this.locationOptions.length === 1) {
                        this.reduceForm.location_id =
                            this.locationOptions[0].location_id;
                    } else {
                        this.reduceForm.location_id = "";
                    }
                } else {
                    this.locationOptions = [];
                    this.reduceForm.location_id = "";
                    this.$message.warning("未找到该条码对应的库位信息");
                }
            } catch (error) {
                console.error("获取库位信息失败:", error);
                this.locationOptions = [];
                this.reduceForm.location_id = "";
                this.$message.error("获取库位信息失败");
            }
        },
        beforeAvatarUploadAnnex(file) {
            const type = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log(type);
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M || type !== "xlsx") {
                if (!isLt10M) {
                    this.$message.error("上传文件大小不能超过 10MB!");
                } else {
                    this.$message.error("上传文件类型错误 请重新上传!");
                }
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading",
                });
            }
            return isLt10M && type === "xlsx";
        },
        onSuccessAnnex(res, file) {
            if (res.errorCode == 0) {
                console.log(file.response.data);
                this.getVirtualList();
                this.$message.success("操作成功");
                this.openImportStatus = false;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        remoteMethod(query) {
            console.log(query);
            if (query.length >= 3) {
                this.loadings = true;
                setTimeout(() => {
                    // let data = {
                    //     page: 1,
                    //     // eslint-disable-next-line camelcase
                    //     bar_code: query,
                    //     limit: 999
                    // };
                    // this.$request.goods.getGoodsList(data).then(res => {
                    //     console.log(res.data);
                    //     this.loadings = false;
                    //     if (res.data.errorCode == 0) {
                    //         this.goodsOptions = res.data.data.list;
                    //         console.log(this.goodsOptions);
                    //     }
                    // });
                    let data = {
                        page: 1,
                        limit: 999,
                        fictitious_id: this.fictitious_id,
                        keyword: query,
                        column: "bar_code",
                    };
                    this.$request.stock
                        .getStorageGoodsVirtual(data)
                        .then((res) => {
                            console.log(res.data);
                            this.loadings = false;

                            if (res.data.errorCode == 0) {
                                this.fic_goods_idOptions =
                                    res.data.data.list.reduce((prev, next) => {
                                        return [
                                            ...prev,
                                            ...next.inventory.map(
                                                ({
                                                    corp_name,
                                                    fic_goods_id,
                                                }) => ({
                                                    goods_name: next.goods_name,
                                                    bar_code: next.bar_code,
                                                    corp_name,
                                                    fic_goods_id,
                                                })
                                            ),
                                        ];
                                    }, []);
                            }
                        });
                }, 300);
            } else {
                this.fic_goods_idOptions = [];
            }
        },
        getStorageGoodsVirtual() {
            let data = {
                page: this.viewGoodsData.page,
                limit: this.viewGoodsData.limit,
                keyword: this.viewGoodsData.keyword,
                column: this.viewGoodsData.column,
                // eslint-disable-next-line camelcase
                fictitious_id: this.viewGoodsData.fictitious_id,
            };
            this.$request.stock.getStorageGoodsVirtual(data).then((res) => {
                console.log(res.data);
                if (res.data.errorCode == 0) {
                    this.viewGoodsData.total = res.data.data.totalnum;
                    this.viewGoodsData.tableData = res.data.data.list;
                }
            });
        },
        viewGoodsSearch() {
            this.viewGoodsData.page = 1;
            this.getStorageGoodsVirtual();
        },
        viewGoods(row) {
            console.log(row.area_id);
            this.viewGoodsDialogStatus = true;
            this.viewGoodsData.fictitious_id = row.fictitious_id;
            this.getStorageGoodsVirtual();
        },
        submitForm() {
            this.$refs.form.validate((valid) => {
                console.log(valid);
                if (valid) {
                    this.submit();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },

        shift(row) {
            this.shiftDialogStatus = true;
            this.fictitious_id = row.fictitious_id;
            this.shiftData.fic_goods_id = "";
            this.shiftData.number = 1;
            this.fic_goods_idOptions = [];
            console.log(row);
        },
        shiftSubmit() {
            this.$refs.shiftData.validate((valid) => {
                console.log(valid);
                if (valid) {
                    let data = {
                        ...this.shiftData,
                    };
                    this.$request.stock.shiftAciton(data).then((res) => {
                        if (res.data.errorCode == 0) {
                            this.$message.success("操作成功");
                            this.shiftDialogStatus = false;
                            this.shiftData = {
                                fic_goods_id: "",
                                number: 1,
                            };
                        }
                        this.getVirtualList();
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        async submit() {
            if (this.type === "edit") {
                let data = {
                    ...this.form,
                };
                let editRes = await this.$request.stock.updateVirtual(data);
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getVirtualList();
                }
            } else {
                if (this.form.fictitious_pid == "") {
                    // eslint-disable-next-line camelcase
                    this.form.fictitious_pid = 0;
                }
                let data = {
                    ...this.form,
                };
                let addRes = await this.$request.stock.addVirtual(data);
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getVirtualList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getVirtualList();
        },
        resetForm() {
            this.form = {
                owner: "",
                platform_ids: [],
                // eslint-disable-next-line camelcase
                fictitious_name: "",
                // eslint-disable-next-line camelcase
                fictitious_pid: "",
                // eslint-disable-next-line camelcase
                contacts_name: "",
                // eslint-disable-next-line camelcase
                contacts_phone: "",
            };
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        add() {
            this.type = "add";
            this.resetForm();
            this.dialogFormVisible = true;
        },
        edit(row) {
            console.log(row);

            this.type = "edit";
            this.form.owner = row.owner;
            this.form.platform_ids = [];
            row.platform_ids.map((i) => {
                this.form.platform_ids.push(Number(i));
            });
            // eslint-disable-next-line camelcase
            this.form.fictitious_name = row.fictitious_name;
            // eslint-disable-next-line camelcase
            this.form.fictitious_pid = row.fictitious_pid;
            // eslint-disable-next-line camelcase
            this.form.contacts_name = row.contacts_name;
            // eslint-disable-next-line camelcase
            this.form.contacts_phone = row.contacts_phone;
            // eslint-disable-next-line camelcase
            this.form.fictitious_id = row.fictitious_id;
            this.dialogFormVisible = true;
        },
        getVirtualList() {
            let data = {
                page: this.page,
                limit: this.limit,
                column: this.column,
                keyword: this.keyword,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                console.log(res);
                this.getVirtualOptions();
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        getVirtualOptions() {
            let data = {
                page: 1,
                limit: 999,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    // this.tableData = res.data.data.list;
                    // let obj = {
                    //     // eslint-disable-next-line camelcase
                    //     fictitious_name: "顶级",
                    //     // eslint-disable-next-line camelcase
                    //     fictitious_id: 0,
                    //     // eslint-disable-next-line camelcase
                    //     fictitious_pid: 0
                    //     // eslint-disable-next-line camelcase
                    // };
                    this.fictitiousOptions = res.data.data.list;
                    // this.fictitiousOptions.unshift(obj);
                }
            });
        },
        handleSizeChange(val) {
            this.page = 1;
            this.limit = val;

            this.getVirtualList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getVirtualList();
        },
    },
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
// /deep/ .demo-table-expand label {
//     width: 90px !important;
//     color: #99a9bf !important;
// }
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.product-imgs {
    display: flex;
    & > div {
        text-align: center;
        margin-right: 10px;
    }
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
// /deep/ .demo-table-expand label {
//     width: 90px !important;
//     color: #99a9bf !important;
// }
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.product-imgs {
    display: flex;
    & > div {
        text-align: center;
        margin-right: 10px;
    }
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
