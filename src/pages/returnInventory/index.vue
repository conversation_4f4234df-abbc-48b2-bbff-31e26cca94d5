<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-input
                    v-model="params.wy_no"
                    @keyup.enter.native="search"
                    clearable
                    placeholder="退回运单号"
                ></el-input>
                <el-date-picker
                    value-format="yyyy-MM-dd"
                    v-model="params.scan_date"
                    type="date"
                    style="margin-left: 10px"
                    placeholder="初次扫码日期"
                >
                </el-date-picker>
                <el-select
                    v-model="params.position"
                    placeholder="存放位置"
                    clearable
                    style="margin-left: 10px"
                >
                    <el-option
                        v-for="(item, index) in positionOptions"
                        :key="index"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="params.return_no"
                    @keyup.enter.native="search"
                    clearable
                    style="margin-left: 10px"
                    placeholder="对应退货单号"
                ></el-input>
                <el-select
                    v-model="params.return_status"
                    placeholder="退货单状态"
                    clearable
                    style="margin-left: 10px"
                >
                    <el-option
                        v-for="item in orderStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="params.time_range"
                    placeholder="到仓时间"
                    clearable
                    style="margin-left: 10px"
                >
                    <el-option
                        v-for="(item, index) in timeRange"
                        :key="index"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    v-model="params.not_return_no"
                    :true-label="1"
                    :false-label="0"
                    @change="search"
                    style="margin-left: 10px"
                    >无退货单号</el-checkbox
                >
                <el-checkbox
                    v-model="params.not_position"
                    :true-label="1"
                    :false-label="0"
                    @change="search"
                    >无存放位置</el-checkbox
                >
                <el-checkbox
                    v-model="params.stagnation"
                    :true-label="1"
                    :false-label="0"
                    @change="search"
                    >滞留包裹</el-checkbox
                >
                <el-button
                    style="margin-left: 10px; margin-top: 10px"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <el-table-column prop="wy_no" label="退回运单号" align="center">
                </el-table-column>
                <el-table-column
                    prop="created_time"
                    label="初次扫码日期"
                    align="center"
                >
                </el-table-column>
                <el-table-column prop="days" label="到仓天数" align="center">
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="position"
                    label="存放位置"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    label="对应退货运单号"
                    width="240"
                >
                    <template slot-scope="row">
                        <div style="max-height: 60px; overflow: auto">
                            <div
                                v-for="(item, index) in row.row.return_order"
                                :key="index"
                            >
                                <el-tooltip
                                    :content="item.status_name"
                                    placement="top"
                                    effect="light"
                                >
                                    <span class="return-code-hover">{{
                                        item.return_code
                                    }}</span>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="退货号状态">
                    <template slot-scope="row">
                        <div style="max-height: 60px; overflow: auto">
                            <div
                                v-for="(item, index) in row.row.return_order"
                                :key="index"
                            >
                                {{ item.status_name }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="查看面单" width="120" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            :disabled="!scope.row.photo_url"
                            @click="showImageDialog(scope.row.photo_url)"
                            >查看面单</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center">
                    <template slot-scope="scope">
                        <el-button
                            v-if="
                                !scope.row.return_order.length &&
                                !scope.row.position
                            "
                            type="primary"
                            size="mini"
                            @click="deleteRow(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="params.page"
                    :page-size="params.limit"
                    :page-sizes="[10, 30, 50, 100, 1000]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>

        <!-- 弹窗Dialog -->
        <el-dialog :visible.sync="dialogVisible" width="50%">
            <img :src="currentImageUrl" style="max-width: 100%" />
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    components: {},
    data() {
        return {
            orderStatusOptions: [
                {
                    label: "待上架",
                    value: 1,
                },
                {
                    label: "部分上架",
                    value: 2,
                },
                {
                    label: "已完成",
                    value: 3,
                },
                {
                    label: "已撤销",
                    value: 4,
                },
                {
                    label: "待确认",
                    value: 5,
                },
                {
                    label: "部分确认",
                    value: 6,
                },
            ],
            params: {
                page: 1,
                limit: 10,
                wy_no: "",
                scan_date: "",
                position: "",
                return_no: "",
                return_status: 1,
                time_range: "",
                not_position: 0,
                stagnation: 0,
                not_return_no: 0,
            },
            timeRange: ["今天", "三天内", "一月内", "一月以上"],
            positionOptions: [],
            tableData: [],
            formLabelWidth: "120px",
            total: 0,
            dialogVisible: false,
            currentImageUrl: "",
        };
    },
    mounted() {
        this.getPosition();
        this.refundOrderList();
    },

    methods: {
        search() {
            this.params.page = 1;
            this.refundOrderList();
        },
        refundOrderList() {
            this.$request.order.returnInventoryList(this.params).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        deleteRow(row) {
            console.log(row);
            this.$confirm("确定删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$request.order
                    .deleteScanRecord({ record_id: row.id })
                    .then((r) => {
                        if (r.data.errorCode == 0) {
                            this.$message.success("操作成功");
                            this.refundOrderList();
                        }
                    });
            });
        },
        getPosition() {
            this.$request.order.getPosition({}).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.positionOptions = res.data.data.list;
                }
            });
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
            this.params.page = 1;

            this.params.limit = val;
            this.refundOrderList();
        },
        close() {
            this.addRefundDialogStatus = false;
            this.refundOrderList();
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.params.page = val;
            this.refundOrderList();
        },
        showImageDialog(photoUrl) {
            this.currentImageUrl = photoUrl;
            this.dialogVisible = true;
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.area-layout {
    .dialog-footer {
        display: flex;
        justify-content: center;
        .el-button {
            margin: 0 10px;
        }
    }
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
.return-code-hover {
    padding: 2px 4px;
    border-radius: 2px;
    cursor: pointer;

    &:hover {
        background-color: #ea8416;
        transition: background-color 0.3s;
    }
}
</style>
