<template>
    <div>
        <!-- begin login-cover -->
        <div class="login-cover">
            <div class="login-cover-image"></div>
            <div class="login-cover-bg"></div>
        </div>
        <!-- end login-cover -->

        <div class="login login-v2" data-pageload-addclass="animated fadeIn">
            <el-steps
                style="margin-bottom: 6px"
                :space="200"
                :active="step"
                finish-status="success"
                align-center
            >
                <el-step title="登录"> </el-step>
                <el-step title="选择仓库"></el-step>
                <el-step title="完成"></el-step>
            </el-steps>

            <!-- begin brand -->
            <div v-if="step == 1" class="step-1">
                <el-select
                    v-model="value"
                    placeholder="请选择仓库"
                    style="width: 50%"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.stock_id"
                        :label="item.stock_name"
                        :value="item.stock_id"
                    >
                    </el-option>
                </el-select>
                <button
                    :disabled="value == ''"
                    style="width: 60%"
                    class="btn btn-success btn-block btn-lg"
                    @click="selectStock"
                >
                    进入
                </button>
            </div>
            <div v-if="step == 0">
                <div class="login-header">
                    <div class="brand">
                        <span class="logo"></span> <b>萌牙WMS</b>
                        <small>请输入您的账号和密码，我们将会妥善保管。</small>
                    </div>
                    <div class="icon">
                        <i class="fa fa-lock"></i>
                    </div>
                </div>

                <!-- end brand -->
                <!-- begin login-content -->
                <div class="login-content">
                    <form
                        v-on:submit="checkForm"
                        method="POST"
                        class="margin-bottom-0"
                    >
                        <div class="form-group m-b-20">
                            <input
                                type="text"
                                class="form-control form-control-lg"
                                placeholder="请输入用户名"
                                v-model="username"
                                required
                            />
                        </div>
                        <div class="form-group m-b-20">
                            <input
                                type="password"
                                class="form-control form-control-lg"
                                placeholder="请输入密码"
                                v-model="password"
                                required
                            />
                        </div>
                        <!-- <div class="form-group m-b-20 group-position">
                        <input
                            type="text"
                            class="form-control form-control-lg"
                            placeholder="请输入验证码"
                            v-model="code"
                            required
                        />
                        <img
                            @click="randomString(6)"
                            :src="'data:image/png;base64,' + vercode"
                            alt=""
                        />
                    </div> -->
                        <!-- <div class="checkbox checkbox-css m-b-20">
                        <input type="checkbox" id="remember_checkbox" />
                        <label for="remember_checkbox">
                            Remember Me
                        </label>
                    </div> -->

                        <div class="login-buttons">
                            <button
                                type="submit"
                                class="btn btn-success btn-block btn-lg"
                            >
                                登录
                            </button>
                        </div>
                        <!-- <div class="m-t-20">
                        Not a member yet? Click
                        <a href="javascript:;">here</a> to register.
                    </div> -->
                    </form>
                </div>
            </div>
            <!-- end login-content -->
        </div>
        <!-- begin login -->

        <!-- end login -->
    </div>
</template>

<script>
import PageOptions from "@/config/PageOptions.vue";
import { mapMutations } from "vuex";

export default {
    created() {
        PageOptions.pageEmpty = true;
    },
    beforeRouteLeave(to, from, next) {
        PageOptions.pageEmpty = false;
        next();
    },
    data() {
        return {
            value: "",
            options: [],
            step: 0,
            username: "",
            password: "",
            code: "", // 用户输入对验证码
            random: "", //随机字符串
            vercode: "", //图片base
        };
    },
    mounted() {
        // this.randomString(6);
        const urlParams = new URLSearchParams(window.location.search);
        const autoLogin = urlParams.get("auto_login");
        const token = urlParams.get("token");
        const stock_id = urlParams.get("stock_id");
        const loginCode = Number(autoLogin);
        console.log("auto_login:", loginCode); // 将输出 "0"
        if (loginCode === 1) {
            this.cookies.set("token", token);
            this.value = stock_id;
            this.selectStock();
        } else if (loginCode === 2) {
            this.cookies.remove("token");
            this.cookies.remove("stock_id");
        } else {
            if (window.location.host === "wms.vinehoo.com") {
                window.location.href =
                    "https://vos.vinehoo.com/login/systems?platform=wms";
            } else {
                window.location.href =
                    "https://test-vos.wineyun.com/login/systems?platform=wms";
            }
        }
    },
    methods: {
        ...mapMutations(["setUserInfo", "setPermissions", "setStockList"]),

        async randomString(len) {
            // 生成随机字符串
            this.random = "";
            len = len || 6;
            const $chars =
                "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
            let maxPos = $chars.length;
            for (let i = 0; i < len; i++) {
                this.random += $chars.charAt(
                    Math.floor(Math.random() * maxPos)
                );
            }
            // 生成base图片

            try {
                let res = await this.$request.user.getCaptcha(this.random);
                console.log(res);
                this.vercode = res.data.data.vercode;
            } catch {
                console.log("获取验证码失败");
            }
        },
        selectStock() {
            this.cookies.set("stock_id", this.value);
            let permissionData = {
                token: this.cookies.get("token"),
                type: 0,
                // eslint-disable-next-line camelcase
                stock_id: this.value,
            };
            this.$request.user.getPermission(permissionData).then((per) => {
                console.error(per);
                if (per.data.errorCode == 0) {
                    this.cookies.set("stock_id", this.value);
                    this.setUserInfo(per.data.data);
                    this.setPermissions(per.data.data.permission_maps);
                    this.setStockList(this.options);
                    setTimeout(() => {
                        // 清空 URL 参数
                        window.history.replaceState(
                            {},
                            "",
                            window.location.pathname
                        );
                        // 跳转到首页
                        this.$router.push({ path: "/home" });
                    }, 200);
                }
            });
        },
        checkForm: function (e) {
            e.preventDefault();
            let data = {
                username: this.username,
                password: this.password,
                // randstr: "dsad",
                // code: "1321"
            };
            this.$request.user.login(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    // , { expires: 7 }
                    this.step = 1;
                    this.cookies.set("token", res.data.data.token);
                    this.options = res.data.data.warehouse;
                    if (res.data.data.warehouse.length) {
                        this.value = res.data.data.warehouse[0].stock_id;
                    } else {
                        this.$message.error("暂无可选仓库");
                    }
                } else {
                    // this.randomString(6);
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.step-1 {
    min-height: 290px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
}
.group-position {
    position: relative;
    & > img {
        position: absolute;
        top: 10px;
        right: 3px;
    }
}
// #
// 7c7d7f
/deep/ .el-step__head.is-wait {
    border-color: #c0c4cc !important;
    color: #c0c4cc !important;
}
/deep/ .el-step__head.is-process {
    border-color: #7c7d7f !important;
    color: #7c7d7f !important;
}
/deep/ .el-step__title.is-process {
    color: #c0c4cc !important;
}
/deep/ .el-step__title.is-wait {
    color: #7c7d7f !important;
}
/deep/ .el-step__head.is-success {
    border-color: #00acac !important;
    color: #00acac !important;
}
/deep/ .el-step__title.is-success {
    font-weight: bold !important;
    color: #00acac !important;
}
</style>
