<template>
    <div
        @dblclick="fullScreen"
        class="board-layout"
        :class="pageEmpty ? 'p-20-auto' : ''"
        v-if="dashboard"
    >
        <div class="operation">
            <div class="flex-bt">
                <div>
                    <div class="widget widget-stats bg-blue">
                        <div class="stats-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stats-info">
                            <h4>本月已累计出库</h4>
                            <p>{{ dashboard.out_orders_total }}</p>
                        </div>
                        <div class="stats-link">
                            <a
                                >查看
                                <i class="fa fa-arrow-alt-circle-right"></i
                            ></a>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="widget widget-stats bg-green">
                        <div class="stats-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="stats-info">
                            <h4>未生成波次订单</h4>
                            <p>{{ dashboard.not_picked_orders }}</p>
                        </div>
                        <div class="stats-link">
                            <a
                                >查看
                                <i class="fa fa-arrow-alt-circle-right"></i
                            ></a>
                        </div>
                    </div>
                </div>
                <!-- begin col-3 -->
                <!-- <div>
                    <div class="widget widget-stats bg-red">
                        <div class="stats-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stats-info">
                            <h4>本月累计错发</h4>
                            <p>{{ dashboard.mispost.length }}</p>
                        </div>
                        <div class="stats-link">
                            <a href="javascript:;"
                                >明细
                                <i class="fa fa-arrow-alt-circle-right"></i
                            ></a>
                        </div>
                    </div>
                </div> -->
                <div>
                    <div class="widget widget-stats bg-red">
                        <div class="stats-icon">
                            <!-- <i class="fas fa-times-circle"></i> -->
                        </div>
                        <div class="stats-info">
                            <h4>
                                库位使用率
                                {{
                                    (
                                        (dashboard.location.use_nums /
                                            dashboard.location.total_nums) *
                                        100
                                    ).toFixed(2)
                                }}%
                            </h4>
                            <p>
                                {{ dashboard.location.use_nums }}(Used) /
                                {{ dashboard.location.total_nums }}(Total)
                            </p>
                        </div>
                        <div class="stats-link">
                            <a href="javascript:;"
                                >明细
                                <i class="fa fa-arrow-alt-circle-right"></i
                            ></a>
                        </div>
                    </div>
                </div>
                <!-- end col-3 -->
                <!-- begin col-3 -->
                <!-- <div>
                    <div class="widget widget-stats bg-orange">
                        <div class="stats-icon">
                            <i class="fa fa-link"></i>
                        </div>

                        <div class="stats-info">
                            <h4>本月累计破损</h4>
                            <p>{{ dashboard.damaged.length }}</p>
                        </div>
                        <div class="stats-link">
                            <a href="javascript:;"
                                >明细
                                <i class="fa fa-arrow-alt-circle-right"></i
                            ></a>
                        </div>
                    </div>
                </div> -->
                <!-- end col-3 -->
                <!-- begin col-3 -->
                <div>
                    <div class="widget widget-stats bg-grey-darker">
                        <div class="stats-icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="stats-info">
                            <h4>当前总在岗人数</h4>
                            <p>
                                {{ dashboard.work.total_person }}
                            </p>
                        </div>

                        <div class="stats-link">
                            <a href="javascript:;">
                                <div class="f-bt">
                                    <div>打包台开放5个</div>
                                    <div
                                        v-if="
                                            dashboard.work &&
                                            dashboard.work.bale_person
                                        "
                                    >
                                        打包员{{ dashboard.work.bale_person }}人
                                    </div>
                                    <div>
                                        拣货员{{
                                            dashboard.work.picking_person
                                        }}人
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main">
                <div class="left">
                    <panel title="今日任务看板" bodyClass="p-t-0">
                        <div class="table-responsive">
                            <table class="table table-valign-middle">
                                <thead>
                                    <tr>
                                        <th>类型</th>
                                        <th>当日执行</th>
                                        <th>18:00前待完成</th>
                                        <th>今日总任务</th>
                                        <th>相较昨日</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item, index) in dashboard
                                            .operate.survey"
                                        :key="index"
                                    >
                                        <td>
                                            <label
                                                class="label label-default"
                                                >{{ item.name }}</label
                                            >
                                        </td>
                                        <td>
                                            <label
                                                class="label label-success"
                                                >{{ item.complete }}</label
                                            >
                                        </td>
                                        <td>
                                            <label
                                                class="label label-warning"
                                                >{{ item.not }}</label
                                            >
                                        </td>
                                        <td>
                                            <label
                                                class="label label-primary"
                                                >{{ item.total }}</label
                                            >
                                        </td>
                                        <td>
                                            <label class="label label-danger">{{
                                                item.yesterday
                                            }}</label>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </panel>
                    <panel title="本月快递公司出库概况" bodyClass="p-t-0">
                        <div class="table-responsive">
                            <table class="table table-valign-middle">
                                <thead>
                                    <tr>
                                        <th>快递公司</th>
                                        <th>出库订单数量</th>
                                        <th>原箱订单数量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(
                                            item, index
                                        ) in dashboard.out_orders"
                                        :key="index"
                                    >
                                        <td>
                                            <label
                                                class="label label-default"
                                                >{{
                                                    item.logistics_companies
                                                }}</label
                                            >
                                        </td>
                                        <td>
                                            <label
                                                class="label label-success"
                                                >{{ item.orders }}</label
                                            >
                                        </td>
                                        <td>
                                            <label
                                                class="label label-warning"
                                                >{{ item.original_box }}</label
                                            >
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </panel>

                    <div style="display: flex">
                        <panel
                            class="pack-person"
                            title="打包员工作概况"
                            style="margin-right: 14px; width: 49%"
                        >
                            <div class="btn">
                                <el-button
                                    size="mini"
                                    @click="exportWork(1)"
                                    type="success"
                                    >导出</el-button
                                >
                            </div>
                            <el-table
                                :data="dashboard.work.bale"
                                v-if="dashboard.work && dashboard.work.bale"
                            >
                                <el-table-column
                                    min-width="80"
                                    align="center"
                                    prop="name"
                                    label="打包员"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="complete"
                                    align="center"
                                    label="订单数"
                                    min-width="80"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="goods_nums"
                                    align="center"
                                    label="瓶数"
                                    min-width="100"
                                >
                                </el-table-column>

                                <el-table-column
                                    prop="working_hours"
                                    align="center"
                                    label="工时"
                                    min-width="80"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="performance"
                                    align="center"
                                    label="绩效"
                                    min-width="80"
                                >
                                </el-table-column>
                            </el-table>
                        </panel>
                        <panel
                            title="拣货员工作概况"
                            class="picking-person"
                            style="width: 49%"
                        >
                            <div class="btn">
                                <el-button
                                    size="mini"
                                    @click="exportWork(2)"
                                    type="success"
                                    >导出</el-button
                                >
                            </div>
                            <el-table
                                :data="dashboard.work.picking"
                                v-if="dashboard.work && dashboard.work.picking"
                            >
                                <el-table-column
                                    min-width="80"
                                    align="center"
                                    prop="name"
                                    label="拣货员"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="complete"
                                    align="center"
                                    label="订单数"
                                    min-width="80"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="goods_nums"
                                    align="center"
                                    label="瓶数"
                                    min-width="100"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="working_hours"
                                    align="center"
                                    label="工时"
                                    min-width="80"
                                >
                                </el-table-column>
                                <el-table-column
                                    prop="performance"
                                    align="center"
                                    label="绩效"
                                    min-width="80"
                                >
                                </el-table-column>
                            </el-table>
                        </panel>
                    </div>
                </div>
                <div class="right">
                    <div
                        class="
                            card
                            border-0
                            bg-dark
                            text-white text-truncate
                            mb-3
                        "
                    >
                        <!-- begin card-body -->
                        <div class="card-body">
                            <!-- begin title -->
                            <div class="mb-3 text-grey">
                                <b class="mb-3">当前波次情况</b>
                                <span class="ml-2"
                                    ><i
                                        class="fa fa-info-circle"
                                        title=""
                                        v-b-popover.hover="'当前波次情况'"
                                    ></i
                                ></span>
                            </div>
                            <!-- end title -->
                            <!-- begin conversion-rate -->
                            <div class="d-flex align-items-center mb-2">
                                <h2 class="text-white mb-0 mr-2">
                                    {{ dateTime }}
                                </h2>
                                <br />
                                <h2
                                    class="text-white mb-0"
                                    style="letter-spacing: 1px"
                                >
                                    {{ time }}
                                </h2>
                                <div class="ml-auto"></div>
                            </div>
                            <!-- end conversion-rate -->
                            <!-- begin percentage -->
                            <div class="mb-2 text-grey">
                                <i class="fa fa-caret-down"></i>
                                {{
                                    dashboard.operate.out_situation.length
                                }}个波次
                            </div>
                            <!-- end percentage -->
                            <!-- begin info-row -->
                            <div
                                class="d-flex mb-2"
                                v-for="(item, index) in dashboard.operate
                                    .out_situation"
                                :key="index"
                            >
                                <div
                                    class="d-flex align-items-center task-table"
                                >
                                    <div class="d-flex align-items-center">
                                        <i
                                            :class="taskClass(item.carrier)"
                                            class="fa fa-circle f-s-8 mr-2"
                                        ></i>
                                        {{ item.carrier }}
                                    </div>
                                    <div class="text-grey">
                                        <i class="fas fa-clock"></i>
                                        {{ item.time }}
                                    </div>

                                    <div class="text-grey">
                                        <i class="fas fa-user"></i>
                                        {{ item.principal }}
                                    </div>

                                    <div
                                        class="width-30 text-right pl-2 f-w-600"
                                    >
                                        {{ item.number }}
                                    </div>
                                </div>
                            </div>
                            <!-- end info-row -->
                        </div>
                        <!-- end card-body -->
                    </div>
                    <div
                        class="
                            card
                            border-0
                            bg-dark
                            text-white text-truncate
                            mb-3
                        "
                    >
                        <!-- begin card-body -->
                        <div class="card-body">
                            <!-- begin title -->
                            <div class="mb-3 text-grey">
                                <b class="mb-3">订单积压情况</b>
                                <span class="ml-2"
                                    ><i
                                        class="fa fa-info-circle"
                                        title=""
                                        v-b-popover.hover="'目前订单积压情况'"
                                    ></i
                                ></span>
                            </div>
                            <div
                                class="d-flex mb-2"
                                v-for="(item, index) in dashboard.operate
                                    .overstock"
                                :key="index"
                            >
                                <div class="d-flex align-items-center">
                                    <i
                                        :class="labelClass(index)"
                                        class="fa fa-circle f-s-8 mr-2"
                                    ></i>
                                    {{ item.name }}
                                </div>
                                <div class="d-flex align-items-center ml-auto">
                                    <!-- <div class="text-grey f-s-11">
                                        <i class="fa fa-caret-up"></i>
                                        {{ item.value }}
                                    </div> -->
                                    <div
                                        class="width-50 text-right pl-2 f-w-600"
                                    >
                                        {{ item.value }}
                                    </div>
                                </div>
                            </div>
                            <!-- end info-row -->
                        </div>
                        <!-- end card-body -->
                    </div>

                    <!-- begin card -->
                    <div
                        class="
                            card
                            border-0
                            bg-dark
                            text-white text-truncate
                            mb-3
                        "
                    >
                        <!-- begin card-body -->
                        <div class="card-body">
                            <!-- begin title -->
                            <div class="mb-3 text-grey">
                                <b class="mb-3">线下/三方订单情况</b>
                                <span class="ml-2"
                                    ><i
                                        class="fa fa-info-circle"
                                        title=""
                                        v-b-popover.hover="
                                            '目前线下/三方订单情况概况'
                                        "
                                    ></i
                                ></span>
                            </div>
                            <div
                                class="d-flex mb-2"
                                v-for="(item, index) in dashboard.operate
                                    .platform"
                                :key="index"
                            >
                                <div class="d-flex align-items-center">
                                    <i
                                        :class="operateClass(item.name)"
                                        class="fa fa-circle f-s-8 mr-2"
                                    ></i>
                                    {{ item.name }}
                                </div>
                                <div class="d-flex align-items-center ml-auto">
                                    <!-- <div class="text-grey f-s-11">
                                        <i class="fa fa-caret-up"></i>
                                        {{ item.value }}
                                    </div> -->
                                    <div
                                        class="width-50 text-right pl-2 f-w-600"
                                    >
                                        {{ item.value }}
                                    </div>
                                </div>
                            </div>
                            <!-- end info-row -->
                        </div>
                        <!-- end card-body -->
                    </div>
                    <!-- begin card -->

                    <!-- end card -->
                </div>
            </div>
        </div>
        <audio
            ref="audioChanged"
            src="https://images.vinehoo.com/wms/videos/dashboard.mp3"
            type="audio/ogg"
        />
        <el-dialog
            center
            title="导出工作概况"
            :visible.sync="exportDialogStatus"
            width="270px"
        >
            <el-date-picker
                v-model="exportData.date"
                align="right"
                type="month"
                :picker-options="pickerOptions"
                placeholder="选择月份"
                value-format="yyyy-MM"
            >
            </el-date-picker>
            <span slot="footer" class="dialog-footer">
                <el-button @click="exportDialogStatus = false">取 消</el-button>
                <el-button
                    type="primary"
                    :disabled="!(exportData.date && exportData.type)"
                    @click="exportWorkExcel"
                    >导出</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import fileDownload from "js-file-download";
import PageOptions from "@/config/PageOptions.vue";
export default {
    beforeRouteLeave(to, from, next) {
        PageOptions.pageEmpty = false;
        next();
    },
    data() {
        return {
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },

            exportData: {
                type: 0,
                date: "",
            },
            path: "",
            exportDialogStatus: false,
            websock: undefined,
            dashboard: {
                not_picked_orders: 0,
                out_orders_total: 0,
                mispost: [],
                damaged: [],
                work: {
                    bale_person: 0,
                    picking_person: 0,
                    total_person: 0,
                },
                out_orders: [],
                operate: {
                    survey: [],
                    bale: [],
                    picking: [],
                    out_situation: [],
                    overstock: [],
                    platform: [],
                },
            },
            pageEmpty: PageOptions.pageEmpty,
            heartBeatTimer: null,
            dateTime: "",
            time: "",
            tableData: [],
        };
    },
    mounted() {
        if (window.location.host == "wms.vinehoo.com") {
            this.path = "wss://wms.vinehoo.com/dashboard";
        } else {
            this.path = "wss://wms-test.wineyun.com/dashboard";
        }
        document.onkeyup = (e) => {
            var event = e || window.event;
            var key = event.which || event.keyCode || event.charCode;
            if (key == 27 || key == 13) {
                PageOptions.pageEmpty = false;
                if (document.fullscreenElement && document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        };
        this.getDateTime();
        this.getDashboradData();
    },
    methods: {
        async exportWorkExcel() {
            this.exportDialogStatus = false;
            const res = await this.$request.stock.exportWorkOverview(
                this.exportData
            );
            console.log(res.data);
            if (res.data.size < 1024) {
                this.$message.error("没有权限");
            } else {
                this.$message.success("导出成功");
                if (this.exportData.type == 1) {
                    fileDownload(
                        res.data,
                        this.exportData.date + "打包员工作概况.xlsx"
                    );
                } else if (this.exportData.type == 2) {
                    fileDownload(
                        res.data,
                        this.exportData.date + "拣货员工作概况.xlsx"
                    );
                }
            }
        },
        exportWork(type) {
            this.exportData.type = type;
            this.exportDialogStatus = true;
        },
        playSound() {
            this.$refs.audioChanged.volume = 0.6;
            this.$refs.audioChanged.play();
        },

        getDateTime() {
            setInterval(() => {
                let time = new Date();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                let year = time.getFullYear();
                let hour = this.checkTime(time.getHours()); //获取时

                let minite = this.checkTime(time.getMinutes()); //获取分

                let second = this.checkTime(time.getSeconds()); //获取秒
                this.dateTime = year + "年" + month + "月" + day + "日";

                this.time = hour + ":" + minite + ":" + second;
            }, 1000);
        },
        checkTime(number) {
            if (number < 10) {
                return "0" + number;
            } else {
                return String(number);
            }
        },
        getDashboradData() {
            this.$request.user.getBoardData().then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res.data.data);
                    this.dashboard = res.data.data;
                    this.initWebSocket();
                }
            });
        },
        initWebSocket() {
            //初始化weosocket
            this.isSocketDeath();
            if (this.websock == undefined) {
                this.websock = new WebSocket(this.path);
                this.websock.onmessage = this.websocketonmessage;
                this.websock.onopen = this.websocketonopen;
                this.websock.onerror = this.websocketonerror;
                this.websock.onclose = this.websocketclose;
            }
        },
        isSocketDeath() {
            if (this.heartBeatTimer) {
                clearInterval(this.heartBeatTimer);
            }
            this.heartBeatTimer = setInterval(() => {
                this.websock.close();
            }, 41000);
        },
        operateClass(name) {
            switch (name) {
                case "酒云":
                    return "text-red";
                case "SO手工单":
                    return "text-warning";
                case "线下业务":
                    return "text-warning";
                default:
                    return "text-lime";
            }
        },
        taskClass(name) {
            switch (name) {
                case "SF":
                    return "text-blue";
                case "JD":
                    return "text-red";
            }
        },
        labelClass(index) {
            switch (index) {
                case 0:
                    return "";
                case 1:
                    return "text-teal";
                case 2:
                    return "text-blue";
                default:
                    return "";
            }
        },
        websocketonopen() {
            console.log("链接成功");
        },
        websocketonerror() {
            //连接建立失败重连
            this.initWebSocket();
        },
        websocketonmessage(e) {
            if (e.data === "heartBeat") {
                this.isSocketDeath();
                return;
            } else {
                try {
                    this.playSound();
                    this.dashboard = JSON.parse(e.data);
                } catch {
                    console.log("解析错误");
                    this.websock.close();
                }
            }
        },
        websocketsend(Data) {
            this.websock.send(Data);
        },
        websocketclose(e) {
            console.log("断开连接", e);
            this.websock = undefined;

            if (this.$route.path == "/dashboard") {
                this.initWebSocket();
            }
        },
        fullScreen() {
            console.log(PageOptions.pageEmpty);
            PageOptions.pageEmpty = true;
            var docElm = document.documentElement;
            //W3C
            if (docElm.requestFullscreen) {
                docElm.requestFullscreen();
            } else if (docElm.mozRequestFullScreen) {
                docElm.mozRequestFullScreen();
            } else if (docElm.webkitRequestFullScreen) {
                docElm.webkitRequestFullScreen();
            }
            //IE11
        },
    },
    destroyed() {
        // 销毁监听
        this.websock.close();
    },
};
</script>
<style lang="scss" scoped>
.p-20-auto {
    padding: 20px 18px;
}
.board-layout {
    min-height: 86vh;
    border-radius: 8px;

    .f-bt {
        display: flex;
        justify-content: space-between;
    }
    .task-table {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
    .table-td-valign-middle td,
    .table-th-valign-middle th,
    .table-valign-middle td,
    .table-valign-middle th {
        text-align: center;
        padding: 14px 0;
    }
    .label {
        padding: 4px 14px;
    }
    .staff {
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .main {
            display: flex;
            justify-content: space-between;
            & > div {
                width: 40%;
            }
        }
    }
    .pack-person,
    .picking-person {
        position: relative;
        .btn {
            position: absolute;
            z-index: 10000;
            top: -2px;
            right: 0;
        }
    }
    .operation {
        .empty {
            float: right;
            color: #409eff;
            font-weight: bold;
            font-size: 30px;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .online {
            // margin-top: 12px;
            text-align: right;
            font-size: 18px;
            font-weight: 400;
        }
        .flex-bt {
            display: flex;
            justify-content: space-between;
            align-items: center;
            & > div {
                width: 100%;
                margin-right: 10px;
            }
            & > :last-child {
                margin-right: 0px;
            }
            p {
                padding: 0;
                font-size: 20px;
                font-weight: 500;
                margin: 0;
            }
        }
        .main {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            .left {
                width: 75%;
            }
            .right {
                width: 23%;
            }
        }
        .panel {
            border: 1px solid #dae0e6;
        }
        .table thead tr th {
            border-bottom: 0;
        }
        .table thead th {
            border-bottom: 0;
        }
        /deep/ .panel .panel-body {
            padding: 0 !important;
        }
    }
}
</style>
