/**
 *
 * @param type
 *
 */
function playSound(type) {
    //  创建DOM节点
    let audio = document.createElement("audio");
    let source = document.createElement("source");
    source.type = "audio/mpeg";
    switch (type) {
        case "changed":
            // 看板数据变化后
            source.src = "https://images.vinehoo.com/wms/videos/dashboard.mp3";
            break;
    }
    audio.appendChild(source);
    audio.play(); // 播放
    audio.addEventListener(
        "ended",
        function () {
            // 播放完后，删除DOM
            audio.remove();
            source.remove();
        },
        false
    );
}

export default { playSound };
