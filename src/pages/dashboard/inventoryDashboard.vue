<template>
    <div class="inventory-dashboard">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>库存数据看板</h1>
            <p>实时监控库存周转率和进出库数据</p>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-cards">
            <div class="stats-card">
                <div class="stats-header">
                    <span class="stats-title">当日入库数量</span>
                    <i class="fas fa-info-circle stats-info-icon"></i>
                </div>
                <div class="stats-number">{{ formatNumber(dashboardData.today_in || 0) }}</div>
                <div class="stats-change" :class="dashboardData.today_in_percent >= 0 ? 'positive' : 'negative'">
                    <i :class="dashboardData.today_in_percent >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                    {{ Math.abs(dashboardData.today_in_percent || 0).toFixed(1) }}% 同比去年
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <span class="stats-title">当日出库数量</span>
                    <i class="fas fa-info-circle stats-info-icon"></i>
                </div>
                <div class="stats-number">{{ formatNumber(dashboardData.today_out || 0) }}</div>
                <div class="stats-change" :class="dashboardData.today_out_percent >= 0 ? 'positive' : 'negative'">
                    <i :class="dashboardData.today_out_percent >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                    {{ Math.abs(dashboardData.today_out_percent || 0).toFixed(1) }}% 同比去年
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <span class="stats-title">本月入库数量</span>
                    <i class="fas fa-info-circle stats-info-icon"></i>
                </div>
                <div class="stats-number">{{ formatNumber(dashboardData.month_in || 0) }}</div>
                <div class="stats-subtitle">本月累计</div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <span class="stats-title">本月出库数量</span>
                    <i class="fas fa-info-circle stats-info-icon"></i>
                </div>
                <div class="stats-number">{{ formatNumber(dashboardData.month_out || 0) }}</div>
                <div class="stats-subtitle">本月累计</div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <span class="stats-title">年度入库数量</span>
                    <i class="fas fa-info-circle stats-info-icon"></i>
                </div>
                <div class="stats-number">{{ formatNumber(dashboardData.year_in || 0) }}</div>
                <div class="stats-subtitle">本年累计</div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <span class="stats-title">年度出库数量</span>
                    <i class="fas fa-info-circle stats-info-icon"></i>
                </div>
                <div class="stats-number">{{ formatNumber(dashboardData.year_out || 0) }}</div>
                <div class="stats-subtitle">本年累计</div>
            </div>
        </div>

        <!-- 库存周转率筛选区域 -->
        <div class="filter-section">
            <div class="filter-header">
                <h2>库存周转率</h2>
                <p class="filter-description">请选择姓名或输入SKU进行查询</p>
            </div>

            <div class="filter-form">
                <div class="filter-row">
                    <!-- 姓名选择 -->
                    <div class="filter-item">
                        <label>姓名</label>
                        <el-select
                            v-model="filters.selectedName"
                            placeholder="张三"
                            @change="onNameChange"
                            @focus="showAdvancedFilters = true"
                            clearable
                            class="filter-select"
                        >
                            <el-option
                                v-for="(contact, index) in contactList"
                                :key="index"
                                :label="contact.contacts_name"
                                :value="contact.contacts_name"
                            />
                        </el-select>
                    </div>

                    <!-- SKU输入 -->
                    <div class="filter-item">
                        <label>SKU简码</label>
                        <el-input
                            v-model="filters.skuSearch"
                            placeholder="请输入SKU"
                            @focus="showAdvancedFilters = true"
                            @input="onSkuInput"
                            clearable
                            class="filter-input"
                        />
                    </div>

                    <!-- 开始时间 -->
                    <div class="filter-item" v-if="showAdvancedFilters && (filters.selectedName || filters.skuSearch)">
                        <label>开始时间</label>
                        <el-date-picker
                            v-model="filters.startDate"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            class="filter-input"
                        />
                    </div>

                    <!-- 结束时间 -->
                    <div class="filter-item" v-if="showAdvancedFilters && (filters.selectedName || filters.skuSearch)">
                        <label>结束时间</label>
                        <el-date-picker
                            v-model="filters.endDate"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            class="filter-input"
                            
                        />
                    </div>
                </div>

                <!-- 虚拟仓和查询按钮行 -->
                <div class="filter-row" v-if="showAdvancedFilters && (filters.selectedName || filters.skuSearch)">
                    <div class="filter-item">
                        <label>虚拟仓</label>
                        <el-select
                            v-model="filters.selectedWarehouse"
                            placeholder="全部虚拟仓"
                            clearable
                            class="filter-select"
                        >
                            <el-option
                                v-for="warehouse in warehouseList"
                                :key="warehouse.fictitious_id"
                                :label="warehouse.fictitious_name"
                                :value="warehouse.fictitious_id"
                            />
                        </el-select>
                    </div>

                    <div class="filter-item">
                        <label>&nbsp;</label>
                        <el-button
                            type="primary"
                            @click="searchTurnoverData"
                            :loading="loading"
                            class="search-button"
                        >
                            查询
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div v-if="showTable && turnoverData.length > 0" class="table-section">
            <div class="table-header">
                <div class="table-title">
                    <h3>商品数据详情</h3>
                    <p class="table-subtitle">{{ getTableSubtitle() }}</p>
                </div>
                <div class="table-actions">
                    <el-button
                        type="primary"
                        @click="exportData"
                        :loading="exportLoading"
                        class="export-button"
                    >
                        导出
                    </el-button>
                </div>
            </div>

            <div class="table-wrapper">
                <el-table
                    :data="turnoverData"
                    v-loading="loading"
                    class="data-table"
                     @sort-change="sortChange"
                    :header-cell-style="{ background: '#fafafa', color: '#333' }"
                >
                    <el-table-column  align="center" prop="short_code" label="SKU" width="150" />
                    <el-table-column  align="center" prop="goods_name" label="商品名称" min-width="200" />
                    <el-table-column
                        prop="turnover_rate"
                        label="周转率"
                        width="120"
                        align="center"
                        :sortable="'turnover_rate'"
                       
                    >
                        <template slot-scope="scope">
                            <span :class="getTurnoverRateClass(scope.row.turnover_rate)">
                                {{ scope.row.turnover_rate }}%
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fictitious_name"  align="center" label="虚拟仓" width="180">
                        <template slot-scope="scope">
                            <el-tag size="small" type="info">{{ scope.row.fictitious_name }}</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <div class="pagination-info">
                    显示 {{ (pagination.page - 1) * pagination.limit + 1 }}-{{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，共 {{ pagination.total }} 条
                </div>
                <div class="pagination-controls">
                    <el-select v-model="pagination.limit" @change="handleSizeChange" class="page-size-select">
                        <el-option label="10" :value="10" />
                        <el-option label="20" :value="20" />
                        <el-option label="50" :value="50" />
                        <el-option label="100" :value="100" />
                    </el-select>
                    <span class="pagination-text">条/页</span>
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="pagination.page"
                        :page-size="pagination.limit"
                        layout="prev, pager, next"
                        :total="pagination.total"
                        class="custom-pagination"
                    />
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div v-if="showTable && turnoverData.length === 0 && !loading" class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>暂无数据</p>
        </div>
    </div>
</template>

<script>
import inventoryDashboardService from "@/services/inventory-dashboard";
import fileDownload from "js-file-download";

export default {
    name: "InventoryDashboard",
    data() {
        return {
            // 统计数据
            dashboardData: {
                today_in: 0,
                today_in_percent: 0,
                today_out: 0,
                today_out_percent: 0,
                month_in: 0,
                month_out: 0,
                year_in: 0,
                year_out: 0,
            },
            
            // 联系人和虚拟仓数据
            contactList: [],
            warehouseList: [],
            
            // 筛选器
            filters: {
                selectedName: "",
                skuSearch: "",
                startDate: this.getDefaultStartDate(),
                endDate: this.getDefaultEndDate(),
                selectedWarehouse: null,
                sort:''
            },
            
            // 显示控制
            showAdvancedFilters: false,
            showTable: false,
            
            // 周转率数据
            turnoverData: [],
            
            // 分页
            pagination: {
                page: 1,
                limit: 20,
                total: 0,
            },
            
            // 加载状态
            loading: false,
            exportLoading: false,
        };
    },
    
    mounted() {
        this.initData();
    },
    
    methods: {
        // 获取默认开始日期（一个月前）
        getDefaultStartDate() {
            const now = new Date();
            now.setMonth(now.getMonth() - 1);
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        },

        // 获取默认结束日期（当天）
        getDefaultEndDate() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        },

        // 格式化数字（添加千分位分隔符）
        formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },

        // 获取表格副标题
        getTableSubtitle() {
            let subtitle = '';
            if (this.filters.startDate && this.filters.endDate) {
                const [startYear, startMonth, startDay] = this.filters.startDate.split('-');
                const [endYear, endMonth, endDay] = this.filters.endDate.split('-');
                subtitle = `${startYear}年${parseInt(startMonth, 10)}月${parseInt(startDay, 10)}日 至 ${endYear}年${parseInt(endMonth, 10)}月${parseInt(endDay, 10)}日`;
            }

            if (this.filters.selectedName) {
                return `${this.filters.selectedName} - ${subtitle}`;
            } else if (this.filters.skuSearch) {
                return `SKU: ${this.filters.skuSearch} - ${subtitle}`;
            }
            return '';
        },

        // 初始化数据
        async initData() {
            await this.loadDashboardData();
            await this.loadContactWarehouse();
        },
        
        // 加载统计数据
        async loadDashboardData() {
            try {
                const response = await inventoryDashboardService.getInventoryBoard();
                if (response.data && response.data.data) {
                    this.dashboardData = response.data.data;
                }
            } catch (error) {
                console.error("加载统计数据失败:", error);
                this.$message.error("加载统计数据失败");
            }
        },
        
        // 加载联系人和虚拟仓数据
        async loadContactWarehouse() {
            try {
                const response = await inventoryDashboardService.getContactWarehouse();
                if (response.data && response.data.data && response.data.data.list) {
                    this.contactList = response.data.data.list;
                    // 提取所有虚拟仓数据
                    this.warehouseList = [];
                    this.contactList.forEach(contact => {
                        if (contact.fictitious_list) {
                            this.warehouseList.push(...contact.fictitious_list);
                        }
                    });
                }
            } catch (error) {
                console.error("加载联系人数据失败:", error);
                this.$message.error("加载联系人数据失败");
            }
        },
        
        // 姓名选择变化
        onNameChange(value) {
            if (value) {
                this.filters.skuSearch = "";
                this.updateWarehouseList();
            }
            this.showTable = false;
        },
        
        // SKU输入变化
        onSkuInput(value) {
            if (value) {
                this.filters.selectedName = "";
            }
            this.showTable = false;
        },
        
        // 更新虚拟仓列表（根据选择的姓名）
        updateWarehouseList() {
            if (this.filters.selectedName) {
                const selectedContact = this.contactList.find(
                    contact => contact.contacts_name === this.filters.selectedName
                );
                if (selectedContact && selectedContact.fictitious_list) {
                    this.warehouseList = selectedContact.fictitious_list;
                } else {
                    this.warehouseList = [];
                }
            } else {
                // 显示所有虚拟仓
                this.warehouseList = [];
                this.contactList.forEach(contact => {
                    if (contact.fictitious_list) {
                        this.warehouseList.push(...contact.fictitious_list);
                    }
                });
            }
            this.filters.selectedWarehouse = null;
        },
        
        // 搜索周转率数据
        async searchTurnoverData() {
            if (!this.filters.selectedName && !this.filters.skuSearch) {
                this.$message.warning("请选择姓名或输入SKU简码");
                return;
            }

            if (!this.filters.startDate || !this.filters.endDate) {
                this.$message.warning("请选择开始时间和结束时间");
                return;
            }

            // 新增：结束时间不能小于开始时间
            if (new Date(this.filters.endDate) < new Date(this.filters.startDate)) {
                this.$message.warning("结束时间不能小于开始时间");
                return;
            }

            this.loading = true;
            try {
                const params = {
                    page: this.pagination.page,
                    limit: this.pagination.limit,
                    contacts_name: this.filters.selectedName,
                    start_date: this.filters.startDate,
                    end_date: this.filters.endDate,
                    sort: this.filters.sort,
                };

                if (this.filters.skuSearch) {
                    params.short_code = this.filters.skuSearch;
                }

                if (this.filters.selectedWarehouse) {
                    params.fictitious_id = this.filters.selectedWarehouse;
                }

                const response = await inventoryDashboardService.getInventoryTurnover(params);
                if (response.data && response.data.data) {
                    this.turnoverData = response.data.data.list || [];
                    this.pagination.total = response.data.data.total || 0;
                    this.showTable = true;
                }
            } catch (error) {
                console.error("查询周转率数据失败:", error);
               
            } finally {
                this.loading = false;
            }
        },
        
        // 重置筛选器
        resetFilters() {
            this.filters = {
                selectedName: "",
                skuSearch: "",
                startDate: this.getDefaultStartDate(),
                endDate: this.getDefaultEndDate(),
                selectedWarehouse: null,
                sort:''
            };
            this.showAdvancedFilters = false;
            this.showTable = false;
            this.turnoverData = [];
            this.pagination.page = 1;
            this.updateWarehouseList();
        },
        
        // 导出数据
        async exportData() {
            if (!this.filters.selectedName && !this.filters.skuSearch) {
                this.$message.warning("请先查询数据");
                return;
            }

            this.exportLoading = true;
            try {
                const params = {
                    contacts_name: this.filters.selectedName,
                    start_date: this.filters.startDate,
                    end_date: this.filters.endDate,
                    sort: this.filters.sort,
                };

                if (this.filters.skuSearch) {
                    params.short_code = this.filters.skuSearch;
                }

                if (this.filters.selectedWarehouse) {
                    params.fictitious_id = this.filters.selectedWarehouse;
                }

                const response = await inventoryDashboardService.exportInventoryTurnover(params);
                const fileName = `库存周转率数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
                fileDownload(response.data, fileName);
                this.$message.success("导出成功");
            } catch (error) {
                console.error("导出失败:", error);
                this.$message.error("导出失败");
            } finally {
                this.exportLoading = false;
            }
        },
        
        // 分页大小变化
        handleSizeChange(val) {
            this.pagination.limit = val;
            this.pagination.page = 1;
            this.searchTurnoverData();
        },
        
        // 当前页变化
        handleCurrentChange(val) {
            this.pagination.page = val;
            this.searchTurnoverData();
        },
     
        sortChange(val){
             const order = val.order;
            // const name = val.prop;

            if (order) {
                if(order == "ascending"){
                    this.filters.sort = "asc";
                }else{
                    this.filters.sort = "desc";
                }
                this.pagination.page = 1;
                this.searchTurnoverData();
            }
        },
        
        // 获取周转率样式类
        getTurnoverRateClass(rate) {
            if (rate >= 90) return "high-rate";
            if (rate >= 70) return "medium-rate";
            return "low-rate";
        },

    },
};
</script>

<style scoped>
.inventory-dashboard {
    padding: 24px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.page-header {
    margin-bottom: 24px;
}

.page-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.page-header p {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
}

/* 统计卡片样式 */
.stats-cards {
    display: grid;
      grid-template-columns: repeat(auto-fit, minmax(max(260px, 33.33% - 20px), 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    position: relative;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.stats-title {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.stats-info-icon {
    color: #d1d5db;
    font-size: 14px;
    cursor: pointer;
}

.stats-number {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
    line-height: 1;
}

.stats-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
}

.stats-change.positive {
    color: #10b981;
}

.stats-change.negative {
    color: #ef4444;
}

.stats-subtitle {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 400;
}

/* 筛选区域样式 */
.filter-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.filter-header {
    margin-bottom: 24px;
}

.filter-section h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.filter-description {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
}

.filter-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 0.2fr));
    gap: 16px;
    align-items: end;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-item label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.filter-select,
.filter-input {
    height: 40px;
}

.date-picker-wrapper {
    display: flex;
    gap: 8px;
}

.year-select,
.month-select {
    flex: 1;
    height: 40px;
}

.search-button {
    height: 40px;
    background: #1f2937;
    border-color: #1f2937;
    font-weight: 500;
}

.search-button:hover {
    background: #111827;
    border-color: #111827;
}

/* 表格区域样式 */
.table-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.table-title h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.table-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 12px;
}

.export-button {
    background: #1f2937;
    border-color: #1f2937;
    font-weight: 500;
    padding: 8px 16px;
}

.export-button:hover {
    background: #111827;
    border-color: #111827;
}

.table-wrapper {
    margin-bottom: 20px;
}

.data-table {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    width: 100%;
}

/* 周转率颜色样式 */
.high-rate {
    color: #10b981;
    font-weight: 600;
}

.medium-rate {
    color: #f59e0b;
    font-weight: 600;
}

.low-rate {
    color: #ef4444;
    font-weight: 600;
}

/* 分页样式 */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.pagination-info {
    font-size: 14px;
    color: #6b7280;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-size-select {
    width: 80px;
}

.pagination-text {
    font-size: 14px;
    color: #6b7280;
}

.custom-pagination {
    margin-left: 16px;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.empty-state i {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
}

.empty-state p {
    font-size: 16px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .inventory-dashboard {
        padding: 16px;
    }

    .stats-cards {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .date-picker-wrapper {
        flex-direction: column;
    }

    .table-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    .pagination-controls {
        justify-content: center;
    }
}

/* Element UI 样式覆盖 */
.el-table th {
    background-color: #fafafa !important;
    color: #374151 !important;
    font-weight: 600 !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.el-table td {
    border-bottom: 1px solid #f3f4f6 !important;
}

.el-table--border {
    border: 1px solid #e5e7eb !important;
}

.el-table--border th,
.el-table--border td {
    border-right: 1px solid #f3f4f6 !important;
}
</style>
