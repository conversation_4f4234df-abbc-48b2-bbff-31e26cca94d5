<template>
    <div>
        <el-date-picker
            style="margin-bottom: 10px"
            value-format="yyyy-MM"
            v-model="date"
            type="month"
            @change="ExpressShipBoard"
            placeholder="选择月"
        >
        </el-date-picker>
        <div class="table-dashbord">
            <el-table :data="processedData" border style="margin-right: 20px">
                <el-table-column
                    prop="group"
                    label=""
                    width="100"
                    :row-span-method="rowSpanMethod"
                ></el-table-column>
                <el-table-column
                    prop="name"
                    label="物流名称"
                    width="340"
                ></el-table-column>
                <el-table-column
                    prop="value"
                    label="物流订单数"
                    width="100"
                ></el-table-column>
            </el-table>
            <el-table :data="allData" border>
                <el-table-column
                    prop="name"
                    label="物流名称"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="total"
                    label="物流总订单数"
                    width="120"
                ></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            rawData: [],
            date: "",
            allData: [],
            processedData: [],
        };
    },
    mounted() {
        const now = new Date();
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, "0"); // getMonth() returns a zero-based value
        this.date = `${year}-${month}`;
        this.ExpressShipBoard();
    },
    methods: {
        async ExpressShipBoard() {
            const res = await this.$request.stock.ExpressShipBoard({
                date: this.date,
            });
            this.rawData = res.data.data;
            this.processData();
        },
        processData() {
            const data = [];
            const allData = [];
            this.rawData.forEach((item) => {
                allData.push({
                    name: item.name,
                    total: item.total,
                });
                item.logistics.forEach((logisticItem) => {
                    data.push({
                        group: item.name,
                        name: logisticItem.name,
                        value: logisticItem.nums,
                    });
                });
                // Push an empty row for the total value
                data.push({
                    group: "",
                    name: "",
                    value: "",
                });
            });
            this.allData = allData;
            this.processedData = data;
        },
        rowSpanMethod({ rowIndex, columnIndex }) {
            if (columnIndex === 0) {
                if (this.processedData[rowIndex].group) {
                    const groupName = this.processedData[rowIndex].group;
                    let rowspan = 0;
                    for (let i = rowIndex; i < this.processedData.length; i++) {
                        if (this.processedData[i].group === groupName) {
                            rowspan++;
                        } else {
                            break;
                        }
                    }
                    return { rowspan, colspan: 1 };
                } else {
                    return { rowspan: 0, colspan: 0 };
                }
            }
        },
    },
};
</script>

<style>
.table-dashbord {
    display: flex;
    justify-content: space-between;
}
/* This ensures each group ends with a total row with an empty group cell */
.el-table th,
.el-table td {
    text-align: center;
}
</style>
