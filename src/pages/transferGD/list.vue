<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.task_no"
                    placeholder="请输入任务编号"
                    clearable
                    @keyup.enter.native="reload"
                />
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.bar_code"
                    placeholder="请输入商品条码"
                    clearable
                    @keyup.enter.native="reload"
                />
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="请输入商品简码"
                    clearable
                    @keyup.enter.native="reload"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
                <el-button type="success" @click="add">创建</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="list" border>
            <el-table-column
                prop="id"
                label="任务ID"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="task_no"
                label="任务编号"
                align="center"
            ></el-table-column>
            <el-table-column label="任务类型" align="center">
                <template slot-scope="scope"
                    >{{ scope.row.task_type | toText("MTransferGDTypeText") }}
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope"
                    >{{ scope.row.status | toText("MTransferGDStatusText") }}
                </template>
            </el-table-column>
            <el-table-column
                prop="fictitious_name"
                label="虚拟仓名称"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="nums"
                label="商品数量"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="goods_name"
                label="中文品名"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="short_code"
                label="商品简称"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="out_location_code"
                label="转出库位编码"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="in_location_code"
                label="转入库位编码"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="create_time"
                label="创建时间"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="complete_time"
                label="完成时间"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="receives"
                label="领取人"
                align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        v-if="
                            [
                                MTransferGDStatus.PendCheck,
                                MTransferGDStatus.PendReceive,
                                MTransferGDStatus.Received,
                            ].includes(scope.row.status)
                        "
                        size="mini"
                        type="primary"
                        @click="cancel(scope.row)"
                        >取消</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row type="flex" justify="center" style="margin-top: 10px">
            <el-pagination
                background
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            />
        </el-row>
        <el-dialog
            class="addDialog"
            width="400px"
            title="创建任务"
            :visible.sync="addDialogStatus"
        >
            <div class="addDialog-box">
                虚拟仓：<el-select
                    v-model="addData.fictitious_id"
                    filterable
                    placeholder="请选择虚拟仓"
                >
                    <el-option
                        v-for="item in virtualList"
                        :key="item.fictitious_id"
                        :label="item.fictitious_name"
                        :value="item.fictitious_id"
                    >
                    </el-option>
                </el-select>
            </div>
            <div class="addDialog-box">
                简码：<el-input
                    style="width: 200px"
                    placeholder="请输入简码"
                    v-model="addData.bar_code"
                >
                </el-input>
            </div>
            <div class="addDialog-box">
                移出库位：<el-select
                    v-model="addData.out_location_id"
                    filterable
                    placeholder="请选择要移出的库位"
                >
                    <el-option
                        v-for="item in stockStorageList"
                        :key="item.location_id"
                        :label="item.location_code"
                        :value="item.location_id"
                    >
                    </el-option>
                </el-select>
            </div>
            <div class="addDialog-box">
                移入库位：<el-select
                    v-model="addData.in_location_id"
                    filterable
                    placeholder="请选择要移入的库位"
                >
                    <el-option
                        v-for="item in stockStorageList"
                        :key="item.location_id"
                        :label="item.location_code"
                        :value="item.location_id"
                    >
                    </el-option>
                </el-select>
            </div>
            <div class="addDialog-box">
                数量：<el-input-number :min="1" v-model="addData.nums">
                </el-input-number>
            </div>

            <div
                style="margin-top: 30px; display: flex; justify-content: center"
            >
                <el-button type="success" @click="addTask">确认</el-button>
                <el-button @click="cancelAdd">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { MTransferGDStatus } from "@/tools/mapperModel";
export default {
    data: () => ({
        MTransferGDStatus,
        addDialogStatus: false,
        stockStorageList: [],
        virtualList: [],
        query: {
            page: 1,
            limit: 10,
            task_no: "",
            bar_code: "",
            short_code: "",
        },
        list: [],
        addData: {
            task_type: 0,
            fictitious_id: "",
            bar_code: "",
            nums: 1,
            out_location_id: "",
            in_location_id: "",
        },
        total: 0,
    }),
    methods: {
        cancelAdd() {
            this.addDialogStatus = false;
            this.load();
            this.addData = {
                task_type: 0,
                fictitious_id: "",
                bar_code: "",
                nums: 1,
                out_location_id: "",
                in_location_id: "",
            };
        },
        async addTask() {
            const res = await this.$request.stock.createdTransfer(this.addData);
            if (res.data.errorCode == 0) {
                this.$message.success("操作成功");
                this.cancelAdd();
            }
        },
        add() {
            this.getStockStorageList();
            this.getVirtualList();
            this.addDialogStatus = true;
        },
        async getVirtualList() {
            let data = { page: 1, limit: 999 };
            const res = await this.$request.stock.getVirtualList(data);
            if (res.data.errorCode == 0) {
                this.virtualList = res.data.data.list;
            }
        },
        async getStockStorageList() {
            let data = { page: 1, limit: 999 };
            const res = await this.$request.stock.getStockStorageList(data);
            if (res.data.errorCode == 0) {
                this.stockStorageList = res.data.data.list;
            }
        },
        load() {
            this.$request.goods.searchTransferGDList(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list = [], total = 0 } = res.data.data;
                    this.list = list;
                    this.total = total;
                }
            });
        },
        cancel(row) {
            this.$confirm("您确认要取消该账号吗？", "提示", {
                type: "warning",
            }).then(() => {
                const { id } = row;
                this.$request.goods.cancelTransferGD({ id }).then(() => {
                    this.$message.success("操作成功");
                    this.load();
                });
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
    created() {
        this.load();
    },
};
</script>
<style scoped lang="scss">
.addDialog-box {
    margin-bottom: 10px;
}
</style>
