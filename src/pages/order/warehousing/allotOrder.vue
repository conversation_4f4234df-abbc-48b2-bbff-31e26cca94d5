<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
                <div>
                    <el-input
                        v-model="form.rd_code"
                        clearable
                        placeholder="单号"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        v-model="form.warehousing_code"
                        clearable
                        placeholder="入库单编号"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        v-model="form.bar_code"
                        clearable
                        placeholder="商品条码"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        v-model="form.short_code"
                        clearable
                        placeholder="商品简码"
                    ></el-input>
                </div>
                <div>
                    <el-select
                        v-model="status"
                        clearable
                        placeholder="调拨单状态"
                    >
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-input
                        v-model="form.supplier"
                        clearable
                        placeholder="供应商"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        v-model="form.fictitious_name"
                        clearable
                        placeholder="虚拟仓名称"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        v-model="form.applicant"
                        clearable
                        placeholder="申请人"
                    ></el-input>
                </div>
                <div>
                    <el-select
                        v-model="is_error"
                        clearable
                        placeholder="是否异常"
                    >
                        <el-option :key="1" label="正常" :value="1">
                        </el-option>
                        <el-option :key="2" label="异常" :value="2">
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-date-picker
                        v-model="finishTime"
                        type="datetimerange"
                        @change="finishTimeChange"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator=""
                        start-placeholder="完成-开始日期"
                        end-placeholder="完成-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </div>
                <!-- <div>
                    <el-select
                        v-model="waybill_no_status"
                        clearable
                        placeholder="物流状态"
                    >
                        <el-option
                            v-for="item in waybill_no_statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div> -->
            </div>
            <div style="margin-top: 10px"></div>
            <div class="actions-from">
                <el-button type="warning" @click="search">查询</el-button>
                <el-button style="margin-left: 10px" @click="add" type="primary"
                    >创建调拨入库单</el-button
                >
                <el-button type="success" @click="exportTable">导出</el-button>
                <el-button
                    style="
                        background-color: #00acac;
                        color: #fff;
                        border-color: #00acac;
                    "
                    @click="openImportStatus = true"
                    >导入调拨单</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table
                :data="tableData"
                @sort-change="sortChange"
                stripe
                border
                style="width: 100%"
            >
                <!-- @selection-change="handleSelectionChange" -->
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-form-item label="虚拟仓名称">
                                <span>{{ props.row.fictitious_name }}</span>
                            </el-form-item>
                            <el-form-item label="供应商">
                                <span>{{ props.row.supplier }}</span>
                            </el-form-item>
                            <el-form-item label="预计到达">
                                <span>{{
                                    props.row.expected_arrival_time
                                }}</span>
                            </el-form-item>
                            <!-- <el-form-item label="运单号">
                                <span>{{ props.row.waybill_no }}</span>
                            </el-form-item> -->
                            <el-form-item label="备注">
                                <span>{{ props.row.remake }}</span>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="rd_code"
                    label="单号"
                    width="200"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="warehousing_code"
                    label="调拨单编号"
                    min-width="300"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="order_type"
                    label="订单类型"
                    width="80"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="applicant"
                    label="申请人"
                    width="100"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="apply_time"
                    sortable="custom"
                    align="center"
                    label="申请时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    prop="finish_time"
                    label="完成时间"
                    align="center"
                    width="180"
                >
                </el-table-column>

                <el-table-column
                    prop="status"
                    label="入库单状态"
                    width="100"
                    align="center"
                >
                </el-table-column
                ><el-table-column
                    prop="is_error"
                    label="是否异常"
                    width="80"
                    align="center"
                >
                </el-table-column>

                <el-table-column label="操作" width="400" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="viewGoods(scope.row)"
                            >查看商品</el-button
                        >
                        <el-button
                            type="success"
                            size="mini"
                            @click="viewClearTask(scope.row)"
                            >查看清点任务</el-button
                        >
                        <!-- <el-button type="text" @click="viewUpTask(scope.row)"
                            >查看上架任务</el-button
                        > -->
                        <el-button
                            type="warning"
                            size="mini"
                            @click="viewUpTask(scope.row)"
                            >查看上架任务</el-button
                        >

                        <el-button
                            type="danger"
                            size="mini"
                            v-if="
                                !(
                                    scope.row.status === '已终止' ||
                                    scope.row.status === '已上架'
                                )
                            "
                            @click="stopWarehousing(scope.row)"
                            >终止</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100, 1000]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog
            title="创建调拨入库单"
            :visible.sync="addTaskDialogStatus"
            width="1000px"
            center
            :close-on-click-modal="false"
        >
            <add
                @close="close"
                :order_type="2"
                v-if="addTaskDialogStatus"
            ></add>
        </el-dialog>
        <div>
            <el-dialog
                title="查看商品"
                :visible.sync="viewGoodsDialogStatus"
                width="70%"
                center
            >
                <warehousingGoods
                    @closeGoodsView="closeGoodsView"
                    @getWarehousingList="getWarehousingList"
                    v-if="viewGoodsDialogStatus"
                    :storage_id="storage_id"
                ></warehousingGoods>
            </el-dialog>
        </div>
        <el-dialog
            center
            title="请上传需要导入的调拨单"
            :visible.sync="openImportStatus"
            width="40%"
        >
            <el-link
                href="https://images.vinehoo.com/wms/order/调拨单模版.xlsx"
                target="_blank"
                type="warning"
                >下载模版<i class="el-icon-download el-icon--right"></i>
            </el-link>
            <div class="el-upload__tip">只能上传xlsx文件，且不超过10MB</div>

            <span slot="footer" class="dialog-footer">
                <div>
                    <el-button @click="openImportStatus = false"
                        >取 消</el-button
                    >
                </div>
                <el-upload
                    class="upload-demo"
                    action="/admin/stock/storage/import"
                    :multiple="false"
                    name="exclFile"
                    :show-file-list="false"
                    :headers="headers"
                    :limit="999"
                    :on-success="onSuccessAnnex"
                    :before-upload="beforeAvatarUploadAnnex"
                >
                    <!-- :on-success="onSuccess" -->

                    <el-button type="primary">上 传</el-button>
                </el-upload>
            </span>
        </el-dialog>
        <el-dialog
            title="查看清点任务"
            :visible.sync="clearTaskOrderDialogStatus"
            width="60%"
            center
        >
            <clearTaskOrder
                v-if="clearTaskOrderDialogStatus"
                :storage_id="storage_id"
            ></clearTaskOrder>
        </el-dialog>
        <el-dialog
            title="查看上架任务"
            :visible.sync="upTaskOrderDialogStatus"
            width="60%"
            center
        >
            <upTaskOrder
                v-if="upTaskOrderDialogStatus"
                :storage_id="storage_id"
            ></upTaskOrder>
        </el-dialog>
    </div>
</template>
<script>
import { Loading } from "element-ui";
import upTaskOrder from "./upTaskOrder.vue";
import clearTaskOrder from "./clearTaskOrder.vue";
import add from "./addTaskOrder.vue";
import Cookies from "js-cookie";
import warehousingGoods from "./goodsListOrder";
import fileDownload from "js-file-download";
export default {
    components: {
        warehousingGoods,
        clearTaskOrder,
        upTaskOrder,
        add,
    },
    data() {
        return {
            headers: {
                warehousecheckval: Cookies.get("stock_id"),
                securitycheckval: Cookies.get("token"),
            },
            openImportStatus: false,
            upTaskOrderDialogStatus: false,
            addTaskDialogStatus: false,
            clearTaskOrderDialogStatus: false,
            direction: "desc",
            storage_id: "",
            viewGoodsDialogStatus: false,
            is_error: "",
            waybill_no_status: "",
            waybill_no_statusOptions: [
                {
                    value: 1,
                    label: "未发货",
                },
                {
                    value: 2,
                    label: "在途中",
                },
                {
                    value: 3,
                    label: "已到达",
                },
            ],
            form: {
                rd_code: "",
                warehousing_code: "",
                bar_code: "",
                short_code: "",
                supplier: "",
                fictitious_name: "",
                applicant: "",
            },
            status: "",
            statusOptions: [
                {
                    value: 1,
                    label: "未开始",
                },
                {
                    value: 2,
                    label: "清点上架中",
                },
                {
                    value: 3,
                    label: "已完成",
                },
                {
                    value: 4,
                    label: "已终止",
                },
            ],
            keyword: "",
            page: 1,
            finishTime: [],
            limit: 10,
            start_time: "",
            end_time: "",
            searchOptions: [
                {
                    value: "rd_code",
                    label: "单号",
                },
                {
                    value: "warehousing_code",
                    label: "调拨单编号",
                },
                {
                    value: "bar_code",
                    label: "商品条码",
                },

                {
                    value: "supplier",
                    label: "供应商",
                },
                {
                    value: "fictitious_name",
                    label: "虚拟仓名称",
                },
                {
                    value: "applicant",
                    label: "申请人",
                },
                // {
                //     value: "waybill_no",
                //     label: "运单号"
                // }
            ],
            column: "rd_code",
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px",
            multipleSelection: [],
        };
    },
    mounted() {
        // this.getStockLinkage();
        this.getWarehousingList();
    },
    methods: {
        closeGoodsView() {
            this.viewGoodsDialogStatus = false;
        },
        beforeAvatarUploadAnnex(file) {
            const type = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log(type);
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M || type !== "xlsx") {
                if (!isLt10M) {
                    this.$message.error("上传文件大小不能超过 10MB!");
                } else {
                    this.$message.error("上传文件类型错误 请重新上传!");
                }
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading",
                });
            }
            return isLt10M && type === "xlsx";
        },
        onSuccessAnnex(res, file) {
            if (res.errorCode == 0) {
                console.log(file.response.data);
                this.getWarehousingList();
                this.openImportStatus = false;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        add() {
            this.addTaskDialogStatus = true;
            // this.$router.push({
            //     path: "/addWareTask",
            //     query: {
            //         order_type: 2
            //     }
            // });
        },
        sortChange(val) {
            console.log(val);
            if (val.order == "ascending") {
                this.direction = "asc";
            } else {
                this.direction = "desc";
            }
            this.search();
        },
        viewClearTask(row) {
            this.clearTaskOrderDialogStatus = true;
            this.storage_id = row.storage_id;
        },
        close() {
            this.getWarehousingList();
            this.addTaskDialogStatus = false;
        },
        viewUpTask(row) {
            this.upTaskOrderDialogStatus = true;
            this.storage_id = row.storage_id;
        },
        stopWarehousing(row) {
            this.$confirm("此操作将永久终止该订单, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    console.log(row);
                    let data = {
                        // eslint-disable-next-line camelcase
                        storage_id: row.storage_id,
                    };
                    this.$request.order
                        .stopWarehousingOrder(data)
                        .then((res) => {
                            if (res.data.errorCode == 0) {
                                this.$message({
                                    message: "操作成功",
                                    type: "success",
                                });
                                this.getWarehousingList();
                            }
                        });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消操作",
                    });
                });
        },
        viewGoods(row) {
            this.viewGoodsDialogStatus = true;
            this.storage_id = row.storage_id;
        },
        shelves(row) {
            // 上架
            console.log(row);
            let data = {
                // eslint-disable-next-line camelcase
                storage_id: row.storage_id,
            };
            this.$request.order.upTask(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.$message({
                        message: "操作成功",
                        type: "success",
                    });
                }
                this.getWarehousingList();
            });
            console.log(row);
        },
        clear(row) {
            // 清点
            // /admin/stock/warehousing/addInventoryTask
            let data = {
                // eslint-disable-next-line camelcase
                storage_id: row.storage_id,
            };
            this.$request.order.addInventoryTask(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.$message({
                        message: "操作成功",
                        type: "success",
                    });
                }
                this.getWarehousingList();
            });
            console.log(row);
        },
        exportTable() {
            let data = {
                is_error: this.is_error,

                status: this.status,
                start_time: this.start_time,
                end_time: this.end_time,
                order_type: 2,
                direction: this.direction,
                ...this.form,
            };
            this.$request.order.exportTableOrder(data).then((res) => {
                if (res.data.size < 1024) {
                    this.$message.error("没有权限");
                    console.log("false");
                } else {
                    this.$message.success("导出成功");
                    fileDownload(res.data, "入库.xlsx");
                }
            });
            // this.$request.order.exportTable(data).then(res => {
            //     if (res.data.errorCode == 0) {
            //         console.log(res.data);
            //     }
            // });
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection);
        },
        getStockLinkage() {
            this.$request.stock.getStockLinkage(1).then((res) => {
                this.options = res.data.data;
            });
        },
        search() {
            this.page = 1;
            this.getWarehousingList();
        },

        getWarehousingList() {
            let data = {
                page: this.page,
                limit: this.limit,
                is_error: this.is_error,

                status: this.status,
                start_time: this.start_time,
                end_time: this.end_time,
                order_type: 2,
                direction: this.direction,
                ...this.form,
            };
            this.$request.order.wareHousingOrderList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        getCheckedNodes(val) {
            if (val[0]) {
                // eslint-disable-next-line camelcase
                this.area_id = val[0];
            } else {
                // eslint-disable-next-line camelcase
                this.area_id = "";
            }
            if (val[1]) {
                // eslint-disable-next-line camelcase
                this.location_id = val[1];
            } else {
                // eslint-disable-next-line camelcase
                this.location_id = "";
            }
        },
        handleSizeChange(val) {
            this.page = 1;

            this.limit = val;
            this.getWarehousingList();
            console.log(`每页 ${val} 条`);
        },
        finishTimeChange(val) {
            if (val) {
                this.start_time = val[0];
                this.end_time = val[1];
            } else {
                this.start_time = "";
                this.end_time = "";
            }
            console.log(val);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getWarehousingList();
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.area-layout {
    .dialog-footer {
        display: flex;
        justify-content: center;
        .el-button {
            margin: 0 10px;
        }
    }
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            & > div {
                display: inline-block;
                margin-bottom: 10px;
                margin-right: 10px;
            }
            /deep/ .el-input {
                width: 230px !important;
            }
        }
    }
}
</style>
