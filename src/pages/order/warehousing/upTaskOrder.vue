<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
                <!-- <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                > -->
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <el-table-column
                    prop="applicant"
                    label="任务发起人"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="recipient"
                    label="领取人"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="任务状态"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    label="任务创建时间"
                    width="180"
                    align="center"
                >
                </el-table-column
                ><el-table-column
                    prop="task_create_time"
                    label="任务领取时间"
                    width="180"
                    align="center"
                >
                </el-table-column
                ><el-table-column
                    prop="task_finish_time"
                    label="任务完成时间"
                    width="180"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="操作" min-width="170" align="center">
                    <template slot-scope="row">
                        <el-button type="primary" size="mini">查看</el-button>
                        <el-button
                            v-if="row.row.status === '待领取'"
                            @click="cancel(row.row)"
                            type="danger"
                            size="mini"
                            >取消任务</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
export default {
    props: ["storage_id"],
    data() {
        return {
            tableData: [],
            formLabelWidth: "120px",
        };
    },
    mounted() {
        this.getUpTaskList();
    },
    methods: {
        cancel(row) {
            console.log(row);
            let data = {
                // eslint-disable-next-line camelcase
                task_id: row.task_id,
            };
            this.$request.order.cancelUpTaskOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.getUpTaskList();
                    this.$message({
                        message: "操作成功",
                        type: "success",
                    });
                }
            });
        },
        getUpTaskList() {
            let data = {
                storage_id: this.storage_id,
            };
            this.$request.order.getUpTaskListOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);

                    this.tableData = res.data.data.list;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 230px !important;
            }
        }
    }
}
</style>
