<template>
    <div class="area-layout">
        <!-- <div class="form"> -->
        <!-- <div class="search"> -->
        <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
        <!-- <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                >
            </div>
        </div> -->
        <div class="area-main">
            <el-table
                :data="tableData"
                stripe
                ref="multipleTable"
                border
                style="width: 100%"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <!-- @selection-change="handleSelectionChange" -->

                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-form-item label="商品中文名">
                                <template>
                                    {{ props.row.goods_name }}
                                </template>
                            </el-form-item>
                            <el-form-item label="商品年份">
                                <template>
                                    {{ props.row.goods_years }}
                                </template>
                            </el-form-item>

                            <el-form-item label="所在库位">
                                <div
                                    v-for="(item, index) in props.row
                                        .location_arr"
                                    :key="index"
                                >
                                    <div>
                                        <span style="font-weight: 600"
                                            >库位编码：</span
                                        >{{ item.location_code }}
                                        <span
                                            style="
                                                margin-left: 20px;
                                                font-weight: 600;
                                            "
                                            >数量：</span
                                        >{{ item.number }}
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="次品信息">
                                <el-table
                                    border
                                    size="mini"
                                    max-height="250"
                                    :data="props.row.err_arr"
                                >
                                    <el-table-column
                                        width="80"
                                        label="类型"
                                        align="center"
                                    >
                                        <template slot-scope="row">
                                            {{ row.row.type }}
                                        </template></el-table-column
                                    >
                                    <el-table-column
                                        width="80"
                                        label="次品类型"
                                        align="center"
                                    >
                                        <template slot-scope="row">
                                            {{ row.row.err_type }}
                                        </template></el-table-column
                                    >
                                    <el-table-column
                                        width="80"
                                        label="次品数量"
                                        align="center"
                                    >
                                        <template slot-scope="row">
                                            {{ row.row.number | nullFormat }}
                                        </template></el-table-column
                                    >
                                    <el-table-column
                                        width="100"
                                        label="图片"
                                        align="center"
                                    >
                                        <template slot-scope="row">
                                            <div class="demo-image__preview">
                                                <el-image
                                                    style="
                                                        width: 60px;
                                                        height: 60px;
                                                    "
                                                    :src="
                                                        setUrlImage(
                                                            'str',
                                                            row.row.img
                                                        )
                                                    "
                                                    :preview-src-list="
                                                        setUrlImage(
                                                            'arr',
                                                            row.row.img
                                                        )
                                                    "
                                                >
                                                    <div
                                                        slot="error"
                                                        class="image-slot"
                                                    ></div>
                                                </el-image>
                                            </div> </template
                                    ></el-table-column>
                                    <!--  -->
                                </el-table>
                                <!-- <el-popover
                                    placement="bottom"
                                    trigger="click"
                                    v-model="popoverVisible"
                                >
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        slot="reference"
                                        @click="commodityError(props.row)"
                                        >查看次品详情</el-button
                                    >
                                </el-popover> -->
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
                <el-table-column
                    prop="bar_code"
                    min-width="130"
                    label="条码"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="150"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="number"
                    label="订单商品数量"
                    width="110"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="good_number"
                    align="center"
                    label="上架良品数量"
                    width="110"
                >
                </el-table-column>
                <el-table-column
                    prop="bad_number"
                    label="上架次品数量"
                    align="center"
                    width="110"
                >
                </el-table-column>
                <el-table-column
                    prop="back_number"
                    label="退回数量"
                    width="90"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="lack_number"
                    label="缺货数量"
                    width="90"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="abnormal_number"
                    label="异常数量"
                    width="90"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="reissue_number"
                    label="待处理数量"
                    width="100"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="inventory_status"
                    width="110"
                    label="清点状态"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    label="上架状态"
                    prop="up_status"
                    width="110"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <div class="clear-button">
                <el-button
                    type="success"
                    @click="clear"
                    :disabled="multipleSelection.length == 0"
                >
                    生成清点任务
                </el-button>
            </div>
            <!-- <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div> -->
        </div>
        <!-- <el-dialog
            title="次品详情"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose"
        >
            <span>这是一段信息</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false"
                    >确 定</el-button
                >
            </span>
        </el-dialog> -->
    </div>
</template>
<script>
export default {
    props: ["storage_id"],
    data() {
        return {
            keyword: "",
            page: 1,
            limit: 100,
            errorData: [],
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            location_id: "",

            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px",
            multipleSelection: [],
        };
    },
    mounted() {
        this.getGoodsList();
    },
    filters: {
        nullFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无";
            }
        },
    },
    methods: {
        setUrlImage(type, str) {
            if (str) {
                let arr = [];
                str.split(",").map((i) => {
                    arr.push("https://images.vinehoo.com" + i);
                });
                if (type == "arr") {
                    return arr;
                }
                if (type == "str") {
                    return arr[0];
                }
            }
        },
        clear() {
            // 清点
            // /admin/stock/warehousing/addInventoryTask

            let arr = [];
            this.multipleSelection.forEach((i) => {
                arr.push(i.storage_goods_id);
            });

            let data = {
                // eslint-disable-next-line camelcase
                storage_goods_arr: arr,
                storage_id: this.storage_id,
            };
            this.$request.order.createClearTaskOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.$emit("getWarehousingList");
                    this.$emit("closeGoodsView");

                    this.$message({
                        message: "操作成功",
                        type: "success",
                    });
                }
            });
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        commodityError(row) {
            this.errorData = row.err_arr;
        },
        getGoodsList() {
            let data = {
                page: this.page,
                limit: this.limit,
                // eslint-disable-next-line camelcase
                storage_id: this.storage_id,
            };
            this.$request.order.getGoodsListOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.totalnum;
                }
            });
        },

        search() {
            this.page = 1;
            this.getGoodsList();
        },

        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getGoodsList();
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 100% !important;
}
.clear-button {
    text-align: center;
    margin-top: 20px;
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 230px !important;
            }
        }
    }
}
</style>
