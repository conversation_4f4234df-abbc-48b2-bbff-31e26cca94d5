<template>
    <div class="add-task-layout">
        <el-form
            :model="ruleForm"
            :rules="rules"
            :inline="true"
            ref="ruleForm"
            label-width="120px"
            class="demo-ruleForm"
        >
            <el-form-item label="入库类型" prop="order_type">
                <el-select
                    style="width: 220px"
                    v-model="ruleForm.order_type"
                    placeholder="请选择入库类型"
                    disabled
                >
                    <el-option
                        v-for="(item, index) in order_typeList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="单号" prop="rd_code">
                <el-input
                    style="width: 300px"
                    v-model="ruleForm.rd_code"
                ></el-input>
            </el-form-item>
            <el-form-item label="申请人" prop="applicant">
                <el-input
                    v-model="ruleForm.applicant"
                    style="width: 220px"
                ></el-input>
            </el-form-item>

            <el-form-item label="虚拟仓名称" prop="fictitious_name">
                <el-select
                    filterable
                    style="width: 300px"
                    v-model="ruleForm.fictitious_name"
                    placeholder="请选择虚拟仓名称"
                >
                    <el-option
                        v-for="(item, index) in virtualList"
                        :key="index"
                        :disabled="item.fictitious_pid === 0"
                        :label="item.fictitious_name"
                        :value="item.fictitious_name"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="预计到达时间" prop="expected_arrival_time">
                <el-date-picker
                    v-model="ruleForm.expected_arrival_time"
                    type="date"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd"
                >
                </el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="运单号" prop="waybill_no">
                <el-input
                    v-model="ruleForm.waybill_no"
                    style="width:300px"
                ></el-input>
            </el-form-item> -->

            <el-form-item label="供应商" prop="supplier">
                <el-input
                    style="width: 300px"
                    placeholder="请输入供应商"
                    v-model="ruleForm.supplier"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remake">
                <el-input
                    type="textarea"
                    autosize
                    style="width: 500px"
                    placeholder="请输入备注"
                    v-model="ruleForm.remake"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="商品名称" prop="goods_arr">
                <el-select
                    filterable
                    v-model="goods_arr"
                    remote
                    style="width: 600px"
                    @change="goodsChange"
                    clearable
                    multiple
                    :loading="loadings"
                    reserve-keyword
                    placeholder="商品条码（至少3位）"
                    :remote-method="remoteMethod"
                >
                    <el-option
                        v-for="item in goodsOptions"
                        :key="item.bar_code"
                        :label="item.goods_name + '/' + item.bar_code"
                        :value="item.bar_code"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-alert
                style="margin: 20px auto; width: 80%"
                title="提示：请先选择完商品后，再输入商品信息"
                type="warning"
                center
                :show-icon="true"
                :closable="false"
            >
            </el-alert>
            <el-form-item label="">
                <div
                    v-for="(item, index) in ruleForm.goods_arr"
                    :key="index"
                    style="display: flex; align-items: center"
                >
                    <div style="display: flex" class="flex-d-c">
                        条码：
                        <el-tag style="margin-right: 40px">{{
                            item.bar_code
                        }}</el-tag>
                    </div>
                    <div style="displya: flex" class="flex-d-c">
                        含税单价：
                        <el-input-number
                            v-model="item.price"
                            :min="1"
                            :precision="2"
                            :max="1000000"
                        ></el-input-number>
                    </div>
                    <div style="displya: flex" class="flex-d-c">
                        生产日期：
                        <!-- item.production_date -->
                        <el-date-picker
                            v-model="item.production_date"
                            type="date"
                            placeholder="选择生产日期"
                            format="yyyy 年 MM 月 dd 日"
                            value-format="yyyy-MM-dd hh:mm:ss"
                        >
                        </el-date-picker>
                    </div>
                    <div style="displya: flex" class="flex-d-c">
                        含税金额：
                        <el-input-number
                            v-model="item.tax_price"
                            :min="1"
                            :precision="2"
                            :max="1000000"
                        ></el-input-number>
                    </div>
                    <div class="flex-d-c">
                        商品数量：
                        <el-input-number
                            v-model="item.number"
                            :min="1"
                            :max="10000"
                        ></el-input-number>
                    </div>
                </div>
            </el-form-item>

            <!-- expected_arrival_time -->
            <div class="center">
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >立即创建</el-button
                >
                <el-button @click="close">取消</el-button>
            </div>
        </el-form>
    </div>
</template>
<script>
export default {
    props: ["order_type"],
    watch: {
        // // eslint-disable-next-line camelcase
        // goods_arr(val) {
        //     console.log(val);
        // }
    },
    data() {
        return {
            // eslint-disable-next-line camelcase
            order_typeList: [
                {
                    label: "新品入库单",
                    value: 1,
                },
                {
                    label: "调拨入库单",
                    value: 2,
                },
            ],
            virtualList: [],
            loadings: false,
            // eslint-disable-next-line camelcase
            goods_arr: [],
            goodsOptions: [],
            ruleForm: {
                // eslint-disable-next-line camelcase
                rd_code: "",
                // eslint-disable-next-line camelcase
                apply_time:
                    new Date().getFullYear() +
                    "-" +
                    Number(new Date().getMonth() + 1) +
                    "-" +
                    new Date().getDate(),
                // eslint-disable-next-line camelcase
                expected_arrival_time: "",
                applicant: "",
                // eslint-disable-next-line camelcase
                goods_arr: [],
                supplier: "",
                remake: "",
                // eslint-disable-next-line camelcase
                // waybill_no: "",
                // eslint-disable-next-line camelcase
                fictitious_name: "",
                // eslint-disable-next-line camelcase
                order_type: 1,
            },
            rules: {
                // eslint-disable-next-line camelcase
                goods_arr: [
                    {
                        required: true,
                        message: "请选择商品",
                        trigger: "blur",
                    },
                ],
                supplier: [
                    {
                        required: true,
                        message: "请输入供应商",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                order_type: [
                    {
                        required: true,
                        message: "请选择入库类型",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                applicant: [
                    {
                        required: true,
                        message: "请输入申请人",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                rd_code: [
                    {
                        required: true,
                        message: "请输入单号",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                fictitious_name: [
                    {
                        required: true,
                        message: "请选择虚拟仓库名称",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    mounted() {
        this.ruleForm.order_type = this.order_type;
        this.getVirtualListOption();
    },
    methods: {
        goodsChange(val) {
            console.log(val);
            // eslint-disable-next-line camelcase
            let arr = [];
            val.map((i) => {
                let obj = {
                    // eslint-disable-next-line camelcase
                    bar_code: i,
                };

                if (!i.price) {
                    obj.price = 0;
                }
                if (!i.tax_price) {
                    // eslint-disable-next-line camelcase
                    obj.tax_price = 0;
                }
                // if (!i.tax_price) {
                //     // eslint-disable-next-line camelcase
                //     obj.production_date = "2000-1-1 00:00:00";
                // }

                if (!i.number) {
                    obj.number = 1;
                }

                arr.push(obj);
            });
            // eslint-disable-next-line camelcase
            this.ruleForm.goods_arr = arr;
        },
        close() {
            this.$emit("close");
        },
        remoteMethod(query) {
            console.log(query);
            if (query.length >= 3) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        page: 1,
                        // eslint-disable-next-line camelcase
                        bar_code: query,
                        limit: 999,
                    };
                    this.$request.goods.getGoodsList(data).then((res) => {
                        console.log(res.data);
                        this.loadings = false;
                        if (res.data.errorCode == 0) {
                            this.goodsOptions = res.data.data.list;
                            console.log(this.goodsOptions);
                        }
                    });
                }, 300);
            } else {
                this.goodsOptions = [];
            }

            console.log(this.goodsOptions);
        },
        getVirtualListOption() {
            console.log(11);
            let data = {
                page: 1,
                limit: 50,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                this.virtualList = res.data.data.list;
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                    };
                    this.$request.order.addWarehousing(data).then((res) => {
                        if (res.data.errorCode == 0) {
                            console.log(res);
                            this.$message.success("创建入库单成功");
                            this.close();
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.flex-d-c {
    display: flex;
    flex-direction: column;
    margin-right: 3px;
    justify-content: center;
    align-items: center;
}
.center {
    text-align: center;
}
</style>
