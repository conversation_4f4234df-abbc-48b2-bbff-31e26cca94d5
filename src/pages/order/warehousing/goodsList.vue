<template>
    <div class="area-layout">
        <!-- <div class="form"> -->
        <!-- <div class="search"> -->
        <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
        <!-- <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                >
            </div>
        </div> -->
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <!-- @selection-change="handleSelectionChange" -->

                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-form-item label="所在库位">
                                <div
                                    v-for="(item, index) in props.row
                                        .location_arr"
                                    :key="index"
                                >
                                    <div>
                                        <span style="font-weight:600"
                                            >库位编码：</span
                                        >{{ item.location_code }}
                                        <span
                                            style="margin-left:20px;font-weight:600"
                                            >数量：</span
                                        >{{ item.num }}
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item
                                label="异常信息"
                                v-if="
                                    props.row.error_number &&
                                        props.row.error_number.length > 0
                                "
                            >
                                <div
                                    v-for="(item, index) in props.row
                                        .error_number"
                                    :key="index"
                                >
                                    <b
                                        style="color: #409eff"
                                        v-if="item.list.length != 0"
                                        >#{{ item.attr }}：</b
                                    >
                                    <span
                                        v-for="(items, index) in item.list"
                                        :key="index"
                                    >
                                        <span style="color: #f56c6c">{{
                                            items.type
                                        }}</span>
                                        ({{ items.num }})
                                    </span>
                                </div>
                                <el-popover
                                    placement="right"
                                    width="620"
                                    trigger="click"
                                >
                                    <el-table border :data="errorData">
                                        <el-table-column
                                            width="110"
                                            label="领取人"
                                        >
                                            <template slot-scope="row">
                                                {{
                                                    row.row.recipient
                                                        | nullFormat
                                                }}
                                            </template></el-table-column
                                        >
                                        <el-table-column
                                            width="100"
                                            label="类型"
                                        >
                                            <template slot-scope="row">
                                                {{ row.row.type | nullFormat }}
                                            </template></el-table-column
                                        >
                                        <el-table-column
                                            width="100"
                                            label="异常数量"
                                        >
                                            <template slot-scope="row">
                                                {{
                                                    row.row.number | nullFormat
                                                }}
                                            </template></el-table-column
                                        >

                                        <el-table-column
                                            width="90"
                                            label="状态"
                                        >
                                            <template slot-scope="row">
                                                {{
                                                    row.row.status | nullFormat
                                                }}
                                            </template></el-table-column
                                        >

                                        <el-table-column label="领取时间">
                                            <template slot-scope="row">
                                                {{
                                                    row.row.task_create_time
                                                        | nullFormat
                                                }}
                                            </template></el-table-column
                                        >

                                        <el-table-column
                                            property="task_finish_time"
                                            label="完成时间"
                                        >
                                            <template slot-scope="row">
                                                {{
                                                    row.row.task_finish_time
                                                        | nullFormat
                                                }}
                                            </template></el-table-column
                                        >
                                    </el-table>
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        slot="reference"
                                        @click="commodityError(props.row)"
                                        >查看异常详情</el-button
                                    >
                                </el-popover>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
                <el-table-column
                    prop="bar_code"
                    width="120"
                    label="条码"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="goods_name"
                    align="center"
                    label="商品中文名"
                    min-width="350"
                >
                </el-table-column>

                <el-table-column prop="status" label="清点状态" align="center">
                </el-table-column>
                <el-table-column
                    prop="putaway_status"
                    label="上架状态"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="number"
                    label="订单商品数量"
                    width="120"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="putaway_num"
                    align="center"
                    label="上架良品数量"
                    width="120"
                >
                    <template>
                        <div slot-scope="props">
                            {{ props.row.putaway_num - props.row.defectueux }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="defectueux"
                    label="上架次品数量"
                    align="center"
                    width="120"
                >
                </el-table-column>
                <el-table-column
                    prop="back_num"
                    label="退回数量"
                    width="100"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <!-- <el-dialog
            title="异常详情"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose"
        >
            <span>这是一段信息</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false"
                    >确 定</el-button
                >
            </span>
        </el-dialog> -->
    </div>
</template>
<script>
export default {
    props: ["warehousing_id"],
    data() {
        return {
            keyword: "",
            page: 1,
            limit: 10,
            errorData: [],
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px"
            // multipleSelection: []
        };
    },
    mounted() {
        this.getGoodsList();
    },
    filters: {
        nullFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无";
            }
        }
    },
    methods: {
        commodityError(row) {
            this.errorData = [];
            console.log(row.bar_code, row.warehousing_id);
            let data = {
                page: 1,
                limit: 999,
                // eslint-disable-next-line camelcase
                bar_code: row.bar_code,
                // eslint-disable-next-line camelcase
                warehousing_id: row.warehousing_id
            };
            this.$request.order.commodityError(data).then(res => {
                if (res.data.errorCode == 0) {
                    console.info(res);
                    this.errorData = res.data.data.list;
                }
            });
        },
        getGoodsList() {
            let data = {
                page: this.page,
                limit: this.limit,
                // eslint-disable-next-line camelcase
                warehousing_id: this.warehousing_id
            };
            this.$request.order.getGoodsList(data).then(res => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.totalnum;
                }
            });
        },

        search() {
            this.page = 1;
            this.getGoodsList();
        },

        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getGoodsList();
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 100% !important;
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
