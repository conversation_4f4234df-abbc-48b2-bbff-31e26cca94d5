<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
                <!-- <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                > -->
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <el-table-column
                    prop="applicant"
                    label="任务发起人"
                    width="120"
                >
                </el-table-column>
                <el-table-column prop="recipient" label="领取人" width="120">
                </el-table-column>
                <el-table-column prop="status" label="任务状态" width="160">
                </el-table-column
                ><el-table-column
                    prop="task_create_time"
                    label="任务领取时间"
                    width="180"
                >
                </el-table-column
                ><el-table-column
                    prop="task_finish_time"
                    label="任务完成时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column label="操作" width="400">
                    <template slot-scope="row">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="viewUpTask(row.row)"
                            >查看上架任务</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            v-if="row.row.status === '已完成'"
                            @click="shelves(row.row)"
                            >生成上架任务</el-button
                        >
                        <el-button
                            type="warning"
                            size="mini"
                            v-if="row.row.status === '已完成'"
                            @click="openDialogPrint(row)"
                            >打印清点任务清单</el-button
                        >
                        <el-button
                            v-if="row.row.status === '待领取'"
                            @click="cancel(row.row)"
                            type="danger"
                            size="mini"
                            >取消任务</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog
            title="清点任务清单"
            center
            :fullscreen="true"
            :visible.sync="dialogVisible"
        >
            <vue-easy-print tableShow ref="easyPrint">
                <div class="tips">
                    <p>入库单单号：{{ warehousing_code }}</p>
                    <p>打印时间：{{ nowDate }}</p>
                </div>

                <div class="cards">
                    <el-card
                        v-for="(item, index) in container"
                        :key="index"
                        class="card"
                    >
                        <div
                            slot="header"
                            class="clearfix"
                            style="text-align:center"
                        >
                            <span class="title"
                                >容器编号：{{ item.code }}
                            </span>
                        </div>
                        <el-card
                            shadow="never"
                            v-for="(goods, index) in item.goods"
                            :key="index"
                        >
                            <p>商品名称：{{ goods.goods_name }}</p>
                            <p>商品条码：{{ goods.bar_code }}</p>
                            <p>商品简码：{{ goods.short_code }}</p>
                            <p>规格：{{ goods.capacity }}</p>
                            <p>数量：{{ goods.num }}</p>
                        </el-card>
                    </el-card>
                </div>
            </vue-easy-print>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="print">再次打印</el-button>
                <el-button @click="dialogVisible = false" type="danger"
                    >关闭窗口</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import vueEasyPrint from "vue-easy-print";

export default {
    components: {
        vueEasyPrint
    },
    data() {
        return {
            dialogVisible: false,
            keyword: "",
            page: 1,
            activeNames: ["allOpen"],
            container: [],
            nowDate: "",
            limit: 10,
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            warehousing_code: "",
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px"
            // multipleSelection: []
        };
    },
    mounted() {
        this.getClearList();
    },
    methods: {
        openDialogPrint(row) {
            this.dialogVisible = true;
            this.container = row.row.container_arr;
            // eslint-disable-next-line camelcase
            this.warehousing_code = row.row.warehousing_code;

            this.print();
        },
        cancel(row) {
            this.$confirm("此操作将永久取消该任务, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                console.log(row);
                let data = {
                    // eslint-disable-next-line camelcase
                    task_id: row.task_id
                };
                this.$request.order.cancelInventoryTask(data).then(res => {
                    if (res.data.errorCode == 0) {
                        this.getClearList();
                        this.$message({
                            message: "操作成功",
                            type: "success"
                        });
                    }
                });
            });
        },
        viewUpTask(row) {
            console.log(row);
            this.$router.push({
                path: "/upTask",
                query: {
                    // eslint-disable-next-line camelcase
                    inventory_task_id: row.task_id,
                    // eslint-disable-next-line camelcase
                    warehousing_id: row.warehousing_id
                }
            });
        },

        shelves(row) {
            // 上架
            console.log(row);
            let data = {
                // eslint-disable-next-line camelcase
                inventory_task_id: row.task_id,
                // eslint-disable-next-line camelcase
                warehousing_id: row.warehousing_id
            };
            this.$request.order.upTask(data).then(res => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.$message({
                        message: "操作成功",
                        type: "success"
                    });
                }
                this.getClearList();
            });
            console.log(row);
        },

        getClearList() {
            let data = {
                page: this.page,
                limit: this.limit,
                keyword: this.keyword,
                // eslint-disable-next-line camelcase
                warehousing_id: this.$route.query.warehousing_id
            };
            this.$request.order.getClearList(data).then(res => {
                if (res.data.errorCode == 0) {
                    console.log(res);

                    this.tableData = res.data.data.list;
                    this.total = res.data.data.totalnum;
                }
            });
        },
        print() {
            let date = new Date();
            this.nowDate =
                date.getFullYear() +
                "-" +
                Number(date.getMonth() + 1) +
                "-" +
                date.getDate() +
                " " +
                date.getHours() +
                ":" +
                date.getMinutes();
            setTimeout(() => {
                this.$refs.easyPrint.print();
            }, 300);
        },
        search() {
            this.page = 1;
            this.getClearList();
        },

        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getClearList();
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.tips {
    text-align: center;
}
.cards {
    display: flex;
    .card {
        margin-right: 10px;
        width: 500px;
        .title {
            font-size: 16px;
            font-weight: 600;
        }
    }
}

.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
