<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
                <!-- <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                > -->
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <el-table-column
                    prop="applicant"
                    label="任务发起人"
                    width="350"
                >
                </el-table-column>
                <el-table-column prop="recipient" label="领取人">
                </el-table-column>
                <el-table-column prop="status" label="任务状态">
                </el-table-column
                ><el-table-column prop="task_create_time" label="任务领取时间">
                </el-table-column
                ><el-table-column prop="task_finish_time" label="任务完成时间">
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template slot-scope="row">
                        <el-button
                            v-if="row.row.status === '待领取'"
                            @click="cancel(row.row)"
                            type="danger"
                            size="mini"
                            >取消任务</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                    background
                >
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            keyword: "",
            page: 1,
            limit: 10,
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px"
            // multipleSelection: []
        };
    },
    mounted() {
        this.getUpTaskList();
    },
    methods: {
        cancel(row) {
            console.log(row);
            let data = {
                // eslint-disable-next-line camelcase
                task_id: row.task_id
            };
            this.$request.order.cancelUpTask(data).then(res => {
                if (res.data.errorCode == 0) {
                    this.getUpTaskList();
                    this.$message({
                        message: "操作成功",
                        type: "success"
                    });
                }
            });
        },
        getUpTaskList() {
            let data = {
                page: this.page,
                limit: this.limit,
                keyword: this.keyword,
                // eslint-disable-next-line camelcase
                inventory_task_id: this.$route.query.inventory_task_id,
                // eslint-disable-next-line camelcase
                warehousing_id: this.$route.query.warehousing_id
            };
            this.$request.order.getUpTaskList(data).then(res => {
                if (res.data.errorCode == 0) {
                    console.log(res);

                    this.tableData = res.data.data.list;
                    this.total = res.data.data.totalnum;
                }
            });
        },

        search() {
            this.page = 1;
            this.getUpTaskList();
        },

        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getUpTaskList();
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
