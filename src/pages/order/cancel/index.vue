<template>
    <div id="cancellations-order">
        <div id="search">
            <el-input
                v-model="form.ordersn"
                placeholder="系统订单号"
            ></el-input>
            <el-input
                v-model="form.orderno"
                placeholder="商家订单号"
            ></el-input>
            <el-input v-model="form.bar_code" placeholder="条码"></el-input>
            <el-input v-model="form.short_code" placeholder="简码"></el-input>
            <el-select v-model="form.type" clearable placeholder="请选择类型">
                <el-option
                    v-for="(item, index) in typeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select v-model="form.status" clearable placeholder="请选择状态">
                <el-option
                    v-for="(item, index) in statusOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-button type="warning" @click="search">查询</el-button>
        </div>
        <div class="main">
            <el-table
                :data="cancellationsOrders"
                border
                stripe
                style="width: 100%"
            >
                <el-table-column
                    prop="task_no"
                    label="任务编号"
                    min-width="200"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="ordersn"
                    label="系统订单号"
                    min-width="220"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="orderno"
                    label="商家订单号"
                    min-width="220"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="receives"
                    label="领取人"
                    min-width="50"
                    align="center"
                >
                    <template slot-scope="row">
                        {{ row.row.receives !== "" ? row.row.receives : "-" }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="type"
                    label="创建类型"
                    min-width="50"
                    align="center"
                >
                    <template slot-scope="row">
                        {{ row.row.type | typeFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="状态"
                    min-width="50"
                    align="center"
                >
                    <template slot-scope="row">
                        {{ row.row.status | statusFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    label="创建时间"
                    min-width="150"
                    align="center"
                ></el-table-column>
                <el-table-column label="操作" width="200" align="center">
                    <template slot-scope="row">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="viewGoods(row.row)"
                            >查看商品</el-button
                        >
                        <el-button
                            type="danger"
                            size="mini"
                            @click="cancelOrder(row.row)"
                            >取消</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div>
                <el-dialog
                    center
                    title="商品信息"
                    :visible.sync="viewGoodsTableStatus"
                    width="80%"
                >
                    <el-table
                        border
                        :data="viewGoodsTableData"
                        style="width: 100%"
                    >
                        <el-table-column type="expand">
                            <template slot-scope="row">
                                <el-form
                                    v-for="(item, index) in row.row.on_shelf"
                                    :key="index"
                                    label-position="left"
                                    inline
                                    class="demo-table-expand"
                                >
                                    <el-form-item label="上架库位">
                                        <span>{{ item.location_code }}</span>
                                    </el-form-item>
                                    <el-form-item label="上架数量">
                                        <span>{{ item.on_nums }}</span>
                                    </el-form-item>
                                </el-form>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="bar_code"
                            label="商品条码"
                            min-width="80"
                            align="center"
                        ></el-table-column>
                        <el-table-column
                            prop="short_code"
                            label="商品简码"
                            min-width="80"
                            align="center"
                        ></el-table-column>
                        <el-table-column
                            prop="nums"
                            label="商品数量"
                            min-width="50"
                            align="center"
                        ></el-table-column>
                        <el-table-column
                            prop="on_nums"
                            label="商品上架数量"
                            min-width="50"
                            align="center"
                        ></el-table-column>
                        <el-table-column
                            prop="location_code"
                            label="推荐上架库位"
                            min-width="50"
                            align="center"
                        ></el-table-column>
                        <el-table-column
                            prop="goods_name"
                            label="商品中文品名"
                            min-width="200"
                            align="center"
                        ></el-table-column>
                        <el-table-column
                            prop="en_goods_name"
                            label="商品英文品名"
                            min-width="200"
                            align="center"
                        ></el-table-column>
                    </el-table>
                </el-dialog>
            </div>
            <div id="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "index",
    filters: {
        typeFormat(val) {
            switch (val) {
                case 0:
                    return "撤单";
                case 1:
                    return "终止出库";
                default:
                    return "未知";
            }
        },
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未领取";
                case 1:
                    return "已领取";
                case 2:
                    return "上架中";
                case 3:
                    return "已完成";
                case 4:
                    return "已取消";
                default:
                    return "未知";
            }
        },
    },
    data() {
        return {
            typeOptions: [
                {
                    value: "0",
                    label: "撤单",
                },
                {
                    value: "1",
                    label: "终止出库",
                },
            ],
            statusOptions: [
                {
                    value: "0",
                    label: "未领取",
                },
                {
                    value: "1",
                    label: "已领取",
                },
                {
                    value: "2",
                    label: "上架中",
                },
                {
                    value: "3",
                    label: "已完成",
                },
                {
                    value: "4",
                    label: "已取消",
                },
            ],
            // 撤单上架任务
            cancellationsOrders: [],
            viewGoodsTableStatus: false,
            // 撤单上架任务的商品详情
            viewGoodsTableData: [],
            page: 1,
            limit: 10,
            total: 0,
            form: {
                // 系统订单号
                ordersn: "",
                // 商家订单号
                orderno: "",
                // 商品条码
                bar_code: "",
                short_code: "",
                // 创建类型
                type: "",
                // 状态
                status: "",
            },
        };
    },
    methods: {
        async cancelOrder(row) {
            console.log(row);
            const data = {
                id: row.id,
            };
            const res = await this.$request.order.CancelOrderTask(data);
            if (res.data.errorCode == 0) {
                console.log(res.data.data);
                this.$message.success("取消成功");
                this.getCancellationsOrders();
            }
        },
        getCancellationsOrders() {
            let data = {
                page: this.page,
                limit: this.limit,
                ...this.form,
            };
            this.$request.order.getCancelOrderList(data).then((response) => {
                if (response.data.errorCode === "0") {
                    this.total = response.data.data.total;
                    this.cancellationsOrders = response.data.data.list;
                }
            });
        },
        search() {
            this.page = 1;
            this.getCancellationsOrders();
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
            this.page = 1;
            this.limit = val;
            this.getCancellationsOrders();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getCancellationsOrders();
        },
        viewGoods(row) {
            console.log(row);
            this.viewGoodsTableStatus = true;
            this.viewGoodsTableData = row.goods;
        },
    },
    mounted() {
        this.getCancellationsOrders();
    },
};
</script>

<style scoped lang="scss">
#cancellations-order {
    height: 100%;
}
/deep/ #search .el-input {
    margin-bottom: 6px;
    margin-right: 12px;
    width: 230px !important;
}
.main {
    margin-top: 10px;
}
#block {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}
</style>
