<template>
    <div>
        <div>
            <div class="title">
                <el-alert
                    :title="'滞留订单数 ' + result.stay_orders"
                    type="warning"
                    center
                    effect="dark"
                    :closable="false"
                >
                </el-alert>
            </div>

            <el-tooltip
                class="item"
                effect="dark"
                content="地区设置"
                placement="left"
            >
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <el-input
                            class="input-new-tag"
                            v-if="inputVisible"
                            v-model="inputValue"
                            ref="saveTagInput"
                            size="small"
                            @keyup.enter.native="handleInputConfirm"
                            @blur="handleInputConfirm"
                        >
                        </el-input>
                        <div v-else>
                            <el-button
                                class="button-new-tag"
                                size="small"
                                type="success"
                                @click="showInput"
                                >新增区域</el-button
                            >
                        </div>
                    </div>
                    <span
                        class="text item"
                        v-for="(item, index) in result.area"
                        :key="index"
                    >
                        <el-tag
                            type="danger"
                            effect="dark"
                            closable
                            :disable-transitions="false"
                            @close="handleClose(item)"
                            style="margin: 0 4px 4px 0"
                            >{{ item }}</el-tag
                        >
                    </span>
                </el-card>
            </el-tooltip>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            inputVisible: false,
            inputValue: "",
            result: {
                stay_orders: 0,
                area: [],
            },
        };
    },
    mounted() {
        this.getResult();
    },
    methods: {
        handleClose(tag) {
            this.result.area.splice(this.result.area.indexOf(tag), 1);
            this.ControlAreaUpdate();
        },

        showInput() {
            this.inputVisible = true;
            this.$nextTick(() => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },
        async ControlAreaUpdate() {
            console.log(this.result.area);
            let data = {
                area: this.result.area,
            };
            let res = await this.$request.order.UpdateControlArea(data);
            if (res.data.errorCode == 0) {
                this.$message.success("更新管控区域信息成功");
            }
            this.getResult();
        },
        handleInputConfirm() {
            let inputValue = this.inputValue;
            if (inputValue) {
                let find = this.result.area.find((item) => item == inputValue);
                if (!find) {
                    this.result.area.push(inputValue);
                    this.ControlAreaUpdate();
                } else {
                    this.$message.warning("添加的管控区域已存在");
                }
            }

            this.inputVisible = false;
            this.inputValue = "";
        },
        async getResult() {
            let res = await this.$request.order.ControlAreaInfo();
            if (res.data.errorCode == 0) {
                this.result = res.data.data;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.title {
    text-align: center;
    margin-bottom: 2px;
}
.text {
    font-size: 14px;
}

.item {
    margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}
.clearfix:after {
    clear: both;
}

.box-card {
    width: 100%;
}
</style>
