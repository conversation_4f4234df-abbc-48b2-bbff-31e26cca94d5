<template>
    <div>
        <div v-if="status">
            <div class="title">
                <el-alert
                    :title="'未拣货订单总数量 ' + result.order_total"
                    type="error"
                    center
                    effect="dark"
                    :closable="false"
                >
                </el-alert>
            </div>

            <el-tooltip
                class="item"
                effect="dark"
                content="爆款"
                placement="left"
            >
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span> {{ result.sell_well.goods_name }} </span>
                    </div>
                    <div class="text item">
                        <div style="margin-bottom:10px">
                            <el-tag type="info"
                                >订单商品数量
                                {{ result.sell_well.goods_nums }}</el-tag
                            >
                            <el-tag type="danger" style="margin-left:4px"
                                >订单数量
                                {{ result.sell_well.order_nums }}</el-tag
                            >
                        </div>
                        <div>
                            <el-tag>条码 {{ result.sell_well.bar_code }}</el-tag
                            ><el-tag type="success" style="margin-left:4px"
                                >简码 {{ result.sell_well.short_code }}</el-tag
                            >
                        </div>
                    </div>
                </el-card>
            </el-tooltip>
            <el-card shadow="hover" class="box-card">
                <el-table :data="result.area" stripe>
                    <el-table-column
                        property="area_name"
                        align="center"
                        label="分区"
                    ></el-table-column>
                    <el-table-column
                        property="orders"
                        label="订单数量"
                        align="center"
                    ></el-table-column>
                </el-table>
            </el-card>
        </div>
        <div v-else>
            <el-alert
                title="暂无订单"
                :closable="false"
                type="error"
                effect="dark"
            >
            </el-alert>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            status: false,
            result: {
                area: []
            }
        };
    },
    mounted() {
        this.getResult();
    },
    methods: {
        async getResult() {
            let res = await this.$request.order.intelligentAnalysis();
            if (res.data.errorCode == 0) {
                console.log(res.data);
                this.result = res.data.data;
                if (this.result.order_total == 0) {
                    this.status = false;
                } else {
                    this.status = true;
                }
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.title {
    text-align: center;
    margin-bottom: 2px;
}
.text {
    font-size: 14px;
}

.item {
    margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}
.clearfix:after {
    clear: both;
}

.box-card {
    width: 100%;
}
</style>
