<template>
    <div>
        <el-table :data="viewOrderData" border style="width: 100%">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <el-form
                        label-position="top"
                        inline
                        class="demo-table-expand"
                    >
                        <el-form-item
                            :label="'商品' + Number(index + 1) + '：'"
                            v-for="(item, index) in props.row.goods"
                            :key="index"
                        >
                            <p>
                                中文名称：<b>{{ item.goods_name }}</b>
                            </p>
                            <p>
                                英文名称：<b>{{ item.en_goods_name }}</b>
                            </p>
                            <p>
                                条码：<b>{{ item.bar_code }}</b>
                            </p>
                            <p>
                                简码：<b>{{ item.short_code }}</b>
                            </p>
                            <p>
                                年份：<b>{{ item.goods_years }}</b>
                            </p>
                            <p>
                                数量：<b>{{ item.nums }}</b>
                            </p>
                        </el-form-item>
                    </el-form></template
                ></el-table-column
            >
            <el-table-column
                prop="ordersn"
                label="订单号"
                width="240"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="orderno"
                label="商家订单号"
                width="240"
                align="center"
            >
            </el-table-column>
            <el-table-column label="运单号" width="220" align="center">
                <template slot-scope="row" v-if="row.row.logistics_no">
                    <div
                        v-for="(item, index) in JSON.parse(
                            row.row.logistics_no
                        )"
                        :key="index"
                    >
                        {{ item }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="receiver_name"
                align="center"
                label="收件人"
                width="100"
            >
            </el-table-column
            ><el-table-column
                align="center"
                prop="receiver_phone"
                label="收件人电话"
                width="120"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="province"
                label="省"
                min-width="120"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="city"
                label="市"
                min-width="120"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="town"
                label="区"
                min-width="120"
            >
            </el-table-column
            ><el-table-column
                prop="address"
                align="center"
                label="收件人地址"
                min-width="300"
            >
            </el-table-column
            ><el-table-column
                prop="is_expedited"
                label="是否加急"
                width="80"
                align="center"
            >
                <template slot-scope="row">
                    {{ row.row.is_expedited | isFormat }}
                </template> </el-table-column
            ><el-table-column
                prop="is_topay"
                label="是否到付"
                width="80"
                align="center"
            >
                <template slot-scope="row">
                    {{ row.row.is_topay | isFormat }}
                </template> </el-table-column
            ><el-table-column
                align="center"
                prop="is_cancel_order"
                label="是否撤单"
                width="80"
            >
                <template slot-scope="row">
                    {{ row.row.is_cancel_order | isFormat }}
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="logistics_company"
                label="快递公司"
                width="130"
            >
            </el-table-column>
            <el-table-column label="附加信息" width="280" align="center">
                <template slot-scope="row">
                    <div v-html="row.row.attach_info"></div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    filters: {
        isFormat(val) {
            if (val) {
                return "是";
            } else {
                return "否";
            }
        },
    },
    data() {
        return {
            orderList: [],
        };
    },
    props: ["viewOrderData"],
    mounted() {},
    methods: {},
};
</script>
<style lang="scss" scoped>
p {
    margin: 0;
}
/deep/ .el-drawer__body {
    padding: 20px;
}
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    // width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
</style>
