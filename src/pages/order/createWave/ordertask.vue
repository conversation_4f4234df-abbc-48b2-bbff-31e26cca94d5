<template>
    <div>
        <div class="task-layout">
            <div class="task-layout-top">
                <div class="task-layout-form">
                    <div>
                        <el-input
                            class="w-220"
                            clearable
                            v-model="forms.task_no"
                            placeholder="波次编号"
                        ></el-input>
                    </div>
                    <div>
                        <el-input
                            class="w-220"
                            clearable
                            v-model="forms.ordersn"
                            placeholder="订单号"
                        ></el-input>
                    </div>
                    <div>
                        <el-input
                            class="w-220"
                            clearable
                            v-model="forms.orderno"
                            placeholder="商家订单号"
                        ></el-input>
                    </div>
                    <div>
                        <el-input
                            class="w-220"
                            clearable
                            v-model="forms.reviews"
                            placeholder="复核人"
                        ></el-input>
                    </div>

                    <div>
                        <el-select
                            v-model="forms.logistics_id"
                            clearable
                            placeholder="所属快递公司"
                        >
                            <el-option
                                v-for="item in expreesList"
                                :key="item.id"
                                :label="item.logistics_company"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-input
                            placeholder="商品条码"
                            class="w-220"
                            clearable
                            v-model="forms.bar_code"
                        ></el-input>
                    </div>
                    <div>
                        <el-input
                            placeholder="商品简码"
                            class="w-220"
                            clearable
                            v-model="forms.short_code"
                        ></el-input>
                    </div>
                    <div>
                        <el-select
                            v-model="forms.status"
                            clearable
                            placeholder="波次状态"
                        >
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.id"
                                :label="item.label"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-date-picker
                            v-model="finishTime"
                            type="datetimerange"
                            @change="finishTimeChange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator=""
                            start-placeholder="复核完成-开始日期"
                            end-placeholder="复核完成-结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                    </div>
                    <div>
                        <el-date-picker
                            v-model="startTime"
                            type="datetimerange"
                            @change="startTimeChange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator=""
                            start-placeholder="创建时间-开始日期"
                            end-placeholder="创建时间-结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <div>
                    <el-button type="primary" @click="drawer = true"
                        >生成波次</el-button
                    >
                    <el-button type="success" @click="assistDialogStatus = true"
                        >辅助分析</el-button
                    >
                    <el-button type="info" @click="regulate = true"
                        >管控区域配置</el-button
                    >
                </div>
                <div>
                    <el-button @click="search" type="warning">查询</el-button>
                </div>
            </div>
            <div class="dialog">
                <el-dialog title="规则设置" width="70%" :visible.sync="drawer">
                    <el-form
                        :inline="true"
                        label-position="left"
                        :rules="rules"
                        ref="ruleForm"
                        label-width="140px"
                        :model="form"
                    >
                        <el-form-item label="快递公司" prop="logistics_id">
                            <el-select
                                style="margin-right: 10px"
                                v-model="form.logistics_id"
                                clearable
                                @change="logistics_idChange"
                                placeholder="请选择所属快递公司"
                            >
                                <el-option
                                    v-for="item in expreesList"
                                    :key="item.id"
                                    :label="item.logistics_company"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="波次订单数" prop="limit">
                            <el-input-number
                                v-model="form.limit"
                                :min="1"
                                :disabled="limitDisable"
                                :step="1"
                                step-strictly
                                :max="1000"
                                label="订单数"
                            ></el-input-number>
                        </el-form-item>

                        <el-form-item label="品质类型" prop="quality_type">
                            <el-select
                                style="margin-right: 10px"
                                v-model="form.quality_type"
                                filterable
                                placeholder="请选择品质类型"
                            >
                                <el-option
                                    v-for="item in quality_typeList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="箱子类型">
                            <el-select
                                style="margin-right: 10px"
                                v-model="form.platform_type"
                                clearable
                                filterable
                                @change="platformTypeListChange"
                                placeholder="请选择箱子类型"
                            >
                                <el-option
                                    v-for="item in platformTypeList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="平台">
                            <el-select
                                style="margin-right: 10px"
                                v-model="form.platform"
                                clearable
                                filterable
                                multiple
                                placeholder="请选择所属平台"
                            >
                                <el-option
                                    v-for="item in platformList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="订单号" prop="ordersn">
                            <el-input
                                style="width: 300px"
                                v-model="form.ordersn"
                                clearable
                                autosize
                                type="textarea"
                                placeholder="请输入订单号以英文逗号隔开"
                            >
                            </el-input>
                            <!-- <el-select
                                v-model="form.ordersn"
                                multiple
                                filterable
                                allow-create
                                clearable
                                default-first-option
                                placeholder="请输入订单号"
                            >
                            </el-select> -->
                        </el-form-item>
                        <el-form-item label="是否打印面单" prop="is_logistics">
                            <el-tooltip
                                :content="
                                    form.is_logistics == 1
                                        ? '打印面单'
                                        : '不打印面单'
                                "
                                placement="top"
                            >
                                <el-switch
                                    v-model="form.is_logistics"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949"
                                    :active-value="1"
                                    :inactive-value="0"
                                >
                                </el-switch>
                            </el-tooltip>
                            <!-- <el-select
                                style="margin-right: 10px"
                                :disabled="companyLogistics"
                                v-model="form.is_logistics"
                                placeholder="请选择"
                            >
                                <el-option :key="1" label="是" :value="1">
                                </el-option>
                                <el-option :key="0" label="否" :value="0">
                                </el-option>
                            </el-select> -->
                        </el-form-item>
                        <el-form-item label="分区" prop="area_id">
                            <el-select
                                style="margin-right: 10px"
                                v-model="form.area_id"
                                clearable
                                placeholder="请选择所属分区"
                            >
                                <el-option
                                    v-for="item in areaList"
                                    :key="item.area_id"
                                    :label="item.area_name"
                                    :value="item.area_id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="商品筛选类型">
                            <el-select
                                style="margin-right: 10px"
                                v-model="goodsType"
                                placeholder="商品筛选类型"
                                @change="goodsTypeChange"
                            >
                                <el-option
                                    v-for="item in goodsTypeList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="SKU数"
                            prop="goods_nums"
                            v-if="goodsType === 4"
                        >
                            <el-input-number
                                v-model="numbers"
                                :min="1"
                                :max="999999"
                                label="品数"
                            ></el-input-number>
                            <el-button
                                style="margin-left: 10px"
                                type="primary"
                                @click="addGoodsNumber"
                                size="mini"
                                >设置</el-button
                            >
                            <div>
                                <el-tag
                                    @close="closeTag"
                                    style="margin-right: 4px"
                                    v-for="tag in form.goods_nums"
                                    :key="tag"
                                    effect="dark"
                                    closable
                                >
                                    SKU数:{{ tag }}
                                </el-tag>
                            </div>
                        </el-form-item>
                        <el-form-item
                            label="商品条码"
                            prop="goods_name"
                            v-if="goodsType === 3"
                        >
                            <el-select
                                v-model="form.bar_code"
                                filterable
                                remote
                                clearable
                                :loading="loadings"
                                reserve-keyword
                                placeholder="商品条码（至少3位）"
                                :remote-method="remoteMethod"
                            >
                                <el-option
                                    v-for="item in goodsOptions"
                                    :key="item.bar_code"
                                    :label="
                                        item.goods_name + '/' + item.bar_code
                                    "
                                    :value="item.bar_code"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item
                            label="是否选择相同期数"
                            prop="same_goods"
                            v-if="goodsType === 2"
                        >
                            <el-checkbox
                                v-model="form.same_goods"
                            ></el-checkbox>
                        </el-form-item>
                        <el-form-item
                            label="商品期数"
                            prop="goods_id"
                            v-if="goodsType === 1"
                        >
                            <!-- <el-input-number
                            v-model="form.goods_id"
                            :min="1"
                            :step="1"
                            step-strictly
                            :max="100000000"
                            label="商品期数"
                        ></el-input-number> -->
                            <el-input
                                style="width: 195px"
                                v-model="form.goods_id"
                                placeholder="请填写期数"
                                type="number"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="是否加急" prop="is_expedited">
                            <el-select
                                v-model="form.is_expedited"
                                clearable
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in expedited"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="逾期状态">
                            <el-select
                                v-model="form.delivery_status"
                                clearable
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in delivery_statusOptions"
                                    :key="item.id"
                                    :label="item.label"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <div style="text-align: center; margin: 30px 0 10px 0">
                            <el-button
                                type="primary"
                                @click="submitForm('ruleForm')"
                                >立即生成</el-button
                            >
                            <el-button @click="reset">重置</el-button>
                        </div>
                    </el-form>
                </el-dialog>
            </div>
            <div class="table">
                <el-table border :data="tableData" style="width: 100%">
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <el-form
                                label-position="left"
                                inline
                                class="demo-table-expand"
                            >
                                <el-form-item
                                    v-for="(item, index) in props.row.task"
                                    :key="index"
                                    :label="
                                        item.bar_code + '(商品条码) 捡货进度:'
                                    "
                                >
                                    <div style="margin-bottom: 30px">
                                        <span style="margin-right: 40px"
                                            ><b>总货数: </b
                                            >{{ item.total_nums }}
                                        </span>
                                        <span
                                            ><b>已捡货数: </b
                                            >{{ item.take_nums }}</span
                                        >
                                        <el-progress
                                            :text-inside="true"
                                            :stroke-width="20"
                                            :percentage="
                                                (item.take_nums /
                                                    item.total_nums) *
                                                100
                                            "
                                        ></el-progress>
                                    </div>
                                </el-form-item> </el-form></template
                    ></el-table-column>
                    <el-table-column
                        prop="task_no"
                        label="波次编号"
                        min-width="210"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="recipients"
                        label="领取人"
                        width="110"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="reviews"
                        label="复核人"
                        width="110"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="container_code"
                        label="容器编码"
                        min-width="180"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        label="状态"
                        min-width="100"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ row.row.status | statusFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="create_time"
                        label="创建时间"
                        width="190"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="review_etime"
                        label="复核完成时间"
                        width="190"
                    >
                    </el-table-column>

                    <el-table-column label="操作" width="300" align="left">
                        <template slot-scope="row">
                            <el-button
                                v-if="
                                    row.row.tracing_source_code &&
                                    row.row.status !== 3
                                "
                                @click="downloadZip(row.row)"
                                type="success"
                                size="mini"
                                >下载溯源码</el-button
                            >
                            <el-button
                                @click="viewOrder(row.row)"
                                type="primary"
                                size="mini"
                                >查看订单</el-button
                            >
                            <el-button
                                v-if="
                                    row.row.pickup_status == 0 &&
                                    (row.row.status == 0 || row.row.status == 2)
                                "
                                @click="cancelWave(row.row)"
                                type="danger"
                                size="mini"
                                >取消波次</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <div class="block">
                    <el-pagination
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="page"
                        :page-size="limit"
                        :page-sizes="[10, 30, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                    >
                    </el-pagination>
                </div>
            </div>
        </div>
        <div class="view-goods">
            <el-dialog
                title="查看订单"
                :visible.sync="viewOrderDataDialogStatus"
                width="80%"
            >
                <waveOrderList :viewOrderData="viewOrderData"></waveOrderList>
            </el-dialog>
        </div>
        <div>
            <el-drawer
                :wrapperClosable="false"
                :visible.sync="assistDialogStatus"
                direction="rtl"
            >
                <template slot="title">
                    <div class="dialog-title-solt">
                        <span style="font-size: 16px">
                            <i class="el-icon-set-up"></i>
                            智能辅助分析
                        </span>
                    </div>
                </template>
                <div style="padding: 0 14px">
                    <Assist v-if="assistDialogStatus"></Assist>
                </div>
            </el-drawer>
        </div>
        <div>
            <el-drawer
                :wrapperClosable="false"
                :visible.sync="regulate"
                direction="ltr"
            >
                <template slot="title">
                    <div class="dialog-title-solt">
                        <span style="font-size: 16px">
                            <i class="el-icon-truck"></i>
                            管控区域配置
                        </span>
                    </div>
                </template>
                <div style="padding: 0 14px">
                    <Regulate v-if="regulate"></Regulate>
                </div>
            </el-drawer>
        </div>
    </div>
</template>
<script>
import Assist from "./assist.vue";
import Regulate from "./Regulate.vue";
import fileDownload from "js-file-download";
import waveOrderList from "./waveOrderList.vue";
import { Loading } from "element-ui";
export default {
    components: {
        waveOrderList,
        Assist,
        Regulate,
    },
    filters: {
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未完成";
                case 1:
                    return "已完成";
                case 2:
                    return "捡货位库存不足";
                case 3:
                    return "已取消";
            }
        },
    },
    data() {
        return {
            finishTime: [],
            quality_typeList: [],
            platformTypeList: [],
            regulate: false,
            startTime: [],
            viewOrderData: "",
            assistDialogStatus: false,
            viewOrderDataDialogStatus: false,
            delivery_statusOptions: [
                {
                    label: "即将逾期",
                    id: 0,
                },
                {
                    label: "已逾期",
                    id: 1,
                },
            ],
            statusOptions: [
                {
                    label: "未完成",
                    id: 0,
                },
                {
                    label: "已完成",
                    id: 1,
                },
                {
                    label: "捡货位库存不足",
                    id: 2,
                },
                {
                    label: "已取消",
                    id: 3,
                },
            ],
            platformList: [],
            limit: 10,
            forms: {
                task_no: "",
                bar_code: "",
                short_code: "",
                orderno: "",
                review_etime: "",
                review_stime: "",

                reviews: "",
                ordersn: "",
                stime: "",
                etime: "",
                logistics_id: "",
            },
            options: [],
            goodsOptions: [],
            tableData: [],
            goodsType: 1,
            goodsTypeList: [
                {
                    id: 1,
                    name: "商品期数",
                },
                {
                    id: 2,
                    name: "是否相同期数",
                },
                {
                    id: 3,
                    name: "商品条码",
                },
                {
                    id: 4,
                    name: "SKU数",
                },
            ],
            loadings: false,
            page: 1,
            numbers: 1,
            areaList: [],
            total: 0,

            expedited: [
                {
                    label: "加急",
                    value: 1,
                },
                {
                    label: "不加急",
                    value: 0,
                },
            ],
            rules: {
                // eslint-disable-next-line camelcase
                quality_type: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                // goods_id: [
                //     {
                //         required: true,
                //         message: "请选择期数",
                //         trigger: "blur"
                //     }
                // ],
                // // eslint-disable-next-line camelcase
                // goods_name: [
                //     {
                //         required: true,
                //         message: "请选择商品名称",
                //         trigger: "blur"
                //     }
                // ],
                platform: [
                    {
                        required: true,
                        message: "请输入平台名称",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                logistics_id: [
                    {
                        required: true,
                        message: "请选择快递公司",
                        trigger: "blur",
                    },
                ],
                limit: [
                    {
                        required: true,
                        message: "波次订单数",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                // goods_nums: [
                //     {
                //         type: "array",
                //         required: true,
                //         message: "请添加一个品数",
                //         trigger: "change"
                //     }
                // ]
            },
            form: {
                platform_type: "",
                is_logistics: 1,
                // eslint-disable-next-line camelcase
                same_goods: false,
                // eslint-disable-next-line camelcase
                logistics_id: "",
                // eslint-disable-next-line camelcase
                goods_id: "",
                platform: [],
                ordersn: "",
                delivery_status: "",
                quality_type: "",
                status: "",
                // eslint-disable-next-line camelcase
                is_expedited: "",
                // eslint-disable-next-line camelcase
                limit: 10,
                // eslint-disable-next-line camelcase
                bar_code: "",
                // eslint-disable-next-line camelcase
                goods_nums: [],
                // eslint-disable-next-line camelcase
                area_id: "",
            },
            drawer: false,
            expreesList: [],
            limitDisable: false,
            companyLogistics: false,
        };
    },
    mounted() {
        this.getQuality_typeList();
        this.getExpressList();
        this.getStockAreaList();
        this.getOrderTask();
        this.getPlatformList();
        this.getPlatformType();
    },
    methods: {
        async downloadZip(row) {
            let data = {
                id: row.id,
            };
            const res = await this.$request.order.DownloadTracingSourceCode(
                data
            );
            if (res.data.size == 0) {
                this.$message.error("没有权限");
                console.log("false");
            } else {
                this.$message.success("导出成功");
                fileDownload(res.data, row.task_no + ".zip");
            }
        },
        getQuality_typeList() {
            this.$request.order.getQuality_typeList().then((res) => {
                if (res.data.errorCode == 0) {
                    this.quality_typeList = res.data.data.list;
                    if (res.data.data.list.length) {
                        this.form.quality_type = res.data.data.list[0].id;
                    }
                }
            });
        },
        platformTypeListChange(val) {
            console.log(val);

            if (val) {
                this.form.platform = [];
                this.getPlatformList(val);
            } else {
                this.getPlatformList();
            }
        },
        search() {
            this.getOrderTask();
            this.page = 1;
        },
        getPlatformType() {
            this.$request.order.getPlatformType().then((res) => {
                this.platformTypeList = res.data.data.list;
            });
        },
        getPlatformList(type) {
            let data = {
                type: type ? type : "",
            };
            this.$request.order.getPlatformList(data).then((res) => {
                this.platformList = res.data.data.list;
            });
        },
        goodsIdChange(val) {
            console.log(val);
        },
        finishTimeChange(val) {
            if (val) {
                this.forms.review_stime = val[0];
                this.forms.review_etime = val[1];
            } else {
                this.forms.review_stime = "";
                this.forms.review_etime = "";
            }
        },
        startTimeChange(val) {
            if (val) {
                this.forms.stime = val[0];
                this.forms.etime = val[1];
            } else {
                this.forms.etime = "";
                this.forms.stime = "";
            }
        },
        viewOrder(row) {
            console.log(row.order);
            this.viewOrderDataDialogStatus = true;
            this.viewOrderData = row.order;
            // this.$router.push({
            //     path: "/waveOrderList",
            //     query: {
            //         order: JSON.stringify(row.order)
            //     }
            // });
        },
        getOrderTask() {
            let data = {
                page: this.page,
                limit: this.limit,
                ...this.forms,
            };
            this.$request.order.getOrderTaskList(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res.data);
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.totalnum;
                }
            });
        },
        reset() {
            this.form = {
                is_logistics: 1,
                // eslint-disable-next-line camelcase
                same_goods: false,
                // eslint-disable-next-line camelcase
                logistics_id: "",
                delivery_status: "",
                // eslint-disable-next-line camelcase
                goods_id: "",
                platform: [],
                platform_type: "",
                ordersn: "",
                status: "",
                // eslint-disable-next-line camelcase
                is_expedited: "",
                // eslint-disable-next-line camelcase
                limit: 10,
                // eslint-disable-next-line camelcase
                bar_code: "",
                // eslint-disable-next-line camelcase
                goods_nums: [],
                // eslint-disable-next-line camelcase
                area_id: "",
            };
            this.platformTypeListChange();
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getOrderTask();
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
            this.page = 1;
            this.limit = val;
            this.getOrderTask();
            console.log(`每页 ${val} 条`);
        },
        submitForm(formName) {
            console.log(this.form);

            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (
                        !this.form.platform_type &&
                        !this.form.platform.length
                    ) {
                        this.$message.warning("平台或箱型,必选择一项后再操作");
                        return;
                    }
                    let data = {
                        ...this.form,
                    };
                    // Loading.service();
                    if (this.form.delivery_status === "") {
                        delete data["delivery_status"];
                    }
                    this.$request.order.createGenerate(data).then((res) => {
                        if (res.data.errorCode == 0) {
                            console.log(res.data);
                            this.getOrderTask();
                            Loading.service().close();
                            this.$message.success("操作成功");
                            this.drawer = false;
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        logistics_idChange(val) {
            console.log(val);
            if (val == 21 || val == 22) {
                this.form.limit = 1;
                this.companyLogistics = true;
                this.limitDisable = true;
                // this.form.is_logistics = 0;
            } else {
                // this.form.is_logistics = 1;
                this.companyLogistics = false;
                this.limitDisable = false;

                this.form.limit = 10;
            }
        },
        cancelWave(row) {
            this.$confirm("此操作将取消波次任务, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    console.log(row);
                    let data = {
                        id: row.id,
                    };
                    this.$request.order.cancelWave(data).then((res) => {
                        console.log(res);
                        if (res.data.errorCode) {
                            this.getOrderTask();
                        }
                    });
                })
                .catch(() => {});
        },
        closeTag() {
            // eslint-disable-next-line camelcase
            this.form.goods_nums = [];
        },
        getStockAreaList() {
            let data = {
                page: 1,
                limit: 9999,
            };
            this.$request.stock.getStockAreaList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.areaList = res.data.data.list;
                }
            });
        },
        goodsTypeChange(val) {
            console.log(val);
            // eslint-disable-next-line camelcase
            this.form.same_goods = false;
            // eslint-disable-next-line camelcase
            this.form.goods_id = "";
            // eslint-disable-next-line camelcase
            this.form.bar_code = "";
            this.goodsOptions = [];
            // eslint-disable-next-line camelcase
            this.form.goods_nums = [];
            // this.form;
        },
        remoteMethod(query) {
            console.log(query);
            if (query.length >= 3) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        page: 1,
                        // eslint-disable-next-line camelcase
                        bar_code: query,
                        limit: 999,
                    };
                    this.$request.goods.getGoodsList(data).then((res) => {
                        console.log(res.data);
                        this.loadings = false;
                        if (res.data.errorCode == 0) {
                            this.goodsOptions = res.data.data.list;
                            console.log(this.goodsOptions);
                        }
                    });
                }, 300);
            } else {
                this.goodsOptions = [];
            }
            console.log(this.goodsOptions);
        },
        addGoodsNumber() {
            // 误删 （多选）
            // this.form.goods_nums.push(this.numbers);
            // this.form.goods_nums.map(i => {
            //     if (Number(i) % 1 !== 0 || Number(i) === 0) {
            //         this.$message.error("格式错误");
            //         this.form.goods_nums.pop();
            //     }
            // });
            // // eslint-disable-next-line camelcase
            // this.form.goods_nums = [...new Set(this.form.goods_nums)];

            //单选

            let value = Number(this.numbers);
            if (value % 1 !== 0 || value === 0) {
                this.$message.error("格式错误");
            } else {
                this.form.goods_nums[0] = this.numbers;
            }

            // eslint-disable-next-line camelcase
            this.form.goods_nums = [...new Set(this.form.goods_nums)];
        },
        filterMethod(val) {
            console.log(val);
        },

        getExpressList() {
            this.$request.order.getExpressList().then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.expreesList = res.data.data.list;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.task-layout {
    .task-layout-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .task-layout-form {
            // display: flex;

            & > div {
                margin-bottom: 10px;
                display: inline-block;
                margin-right: 10px;
                span {
                    font-size: 14px;
                }
            }
        }
    }
    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .table {
        margin-top: 20px;
    }
    /deep/ .el-drawer__body {
        padding: 20px;
    }
    /deep/ .demo-table-expand {
        font-size: 0 !important;
    }
    /deep/ .demo-table-expand label {
        // width: 90px !important;

        color: #99a9bf !important;
    }
    /deep/ .demo-table-expand .el-form-item {
        margin-right: 0 !important;
        text-align: center !important;
        margin-bottom: 0 !important;
        width: 33% !important;
    }
    .dialog .el-form-item {
        width: 47% !important;
    }
    .w-220 {
        width: 220px;
    }
    .block {
        margin-top: 10px;
        display: flex;
        justify-content: center;
    }
}
</style>
