<template>
    <div class="area-layout">
        <!-- <div class="form"> -->
        <!-- <div class="search"> -->
        <!-- <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader> -->
        <!-- <el-input
                    v-model="keyword"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-button style="margin-left:10px" @click="search"
                    >查询</el-button
                >
            </div>
        </div> -->
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <!-- @selection-change="handleSelectionChange" -->

                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-form-item label="所在库位">
                                <div
                                    v-for="(item, index) in props.row
                                        .location_arr"
                                    :key="index"
                                >
                                    <div>
                                        <span style="font-weight: 600"
                                            >库位编码：</span
                                        >{{ item.location_code }}
                                        <span
                                            style="
                                                margin-left: 20px;
                                                font-weight: 600;
                                            "
                                            >数量：</span
                                        >{{ item.num }}
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item label="次品信息">
                                <el-table
                                    border
                                    size="mini"
                                    max-height="250"
                                    :data="props.row.defectueux_info"
                                >
                                    <el-table-column
                                        width="100"
                                        align="center"
                                        label="类型"
                                    >
                                        <template slot-scope="row">
                                            {{ row.row.type | nullFormat }}
                                        </template></el-table-column
                                    >
                                    <el-table-column
                                        width="100"
                                        align="center"
                                        label="次品数量"
                                    >
                                        <template slot-scope="row">
                                            {{ row.row.num }}
                                        </template></el-table-column
                                    >
                                    <el-table-column
                                        width="150"
                                        label="图片"
                                        align="center"
                                    >
                                        <template slot-scope="row">
                                            <div class="demo-image__preview">
                                                <el-image
                                                    style="
                                                        width: 80px;
                                                        height: 80px;
                                                    "
                                                    :src="
                                                        setUrlImage(
                                                            'str',
                                                            row.row.img
                                                        )
                                                    "
                                                    :preview-src-list="
                                                        setUrlImage(
                                                            'arr',
                                                            row.row.img
                                                        )
                                                    "
                                                >
                                                    <div
                                                        slot="error"
                                                        class="image-slot"
                                                    ></div>
                                                </el-image>
                                                <div>点击图片查看 {{ setUrlImage(
                                                            'arr',
                                                            row.row.img
                                                        ).length}}张</div>
                                            </div> </template
                                    ></el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
                <el-table-column
                    prop="bar_code"
                    width="125"
                    label="条码"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                    width="180"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="goods_name"
                    align="center"
                    label="商品中文名"
                    width="350"
                >
                </el-table-column>

                <el-table-column align="center" prop="status" label="状态">
                </el-table-column>
                <el-table-column
                    prop="number"
                    align="center"
                    label="订单商品数量"
                    width="110"
                >
                </el-table-column>

                <el-table-column
                    align="center"
                    prop="normal_num"
                    label="上架良品数量"
                    width="110"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="defectueux"
                    label="上架次品数量"
                    width="110"
                >
                </el-table-column>
            </el-table>
            <!-- <div class="block">
                <el-pagination
                background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    layout="total, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div> -->
        </div>
        <!-- <el-dialog
            title="次品详情"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose"
        >
            <span>这是一段信息</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false"
                    >确 定</el-button
                >
            </span>
        </el-dialog> -->
    </div>
</template>
<script>
export default {
    props: ["return_id"],
    data() {
        return {
            keyword: "",
            page: 1,
            limit: 10,
            errorData: [],
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px",
            // multipleSelection: []
        };
    },
    mounted() {
        this.refundGoodsList();
    },
    filters: {
        nullFormat(val) {
            if (val) {
                return val;
            } else {
                return "暂无";
            }
        },
    },
    methods: {
        setUrlImage(type, str) {
            if (str) {
                let arr = [];
                str.split(",").map((i) => {
                    arr.push("https://images.vinehoo.com" + i);
                });
                if (type == "arr") {
                    return arr;
                }
                if (type == "str") {
                    return arr[0];
                }
            }
        },
        commodityError(row) {
            this.errorData = row;
        },
        refundGoodsList() {
            let data = {
                // eslint-disable-next-line camelcase
                return_id: this.return_id,
            };
            this.$request.order.refundGoodsList(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.tableData = res.data.data;
                }
            });
        },

        search() {
            this.page = 1;
            this.refundGoodsList();
        },

        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.refundGoodsList();
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 100% !important;
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
