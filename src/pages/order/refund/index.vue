<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <CompanySelectGroup
                    v-model="corp"
                    style="margin-right: 10px"
                ></CompanySelectGroup>
                <el-select v-model="column" placeholder="搜索条件">
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="keyword"
                    @keyup.enter.native="search"
                    clearable
                    placeholder="请输入关键字"
                ></el-input>
                <el-select
                        v-model="fictitious_id"
                        clearable
                        filterable
                        multiple
                        style="margin-left: 10px"
                        placeholder="请选择虚拟仓"
                    >
                        <el-option
                            :disabled="item.fictitious_pid === 0"
                            :label="item.fictitious_name"
                            :value="item.fictitious_id"
                            v-for="(item, index) in fictitiousList"
                            :key="index"
                        ></el-option>
                    </el-select>
                <el-input
                    v-model="bar_code"
                    @keyup.enter.native="search"
                    clearable
                    style="margin-left: 10px"
                    placeholder="请输入条码"
                ></el-input>
                <el-input
                    v-model="short_code"
                    @keyup.enter.native="search"
                    clearable
                    style="margin-left: 10px"
                    placeholder="请输入商品简码"
                ></el-input>
                <el-select
                    v-model="status"
                    placeholder="退货单状态"
                    clearable
                    style="margin-left: 10px"
                >
                    <el-option
                        v-for="item in orderStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-date-picker
                    v-model="creatTime"
                    type="daterange"
                    start-placeholder="创建开始时间"
                    end-placeholder="创建结束时间"
                    value-format="yyyy-MM-dd"
                    style="margin-left: 10px; margin-top: 10px;"
                ></el-date-picker>
                <el-date-picker
                    v-model="completeTime"
                    type="daterange"
                    start-placeholder="完成开始时间"
                    end-placeholder="完成结束时间"
                    value-format="yyyy-MM-dd"
                    style="margin-left: 10px; margin-top: 10px;"
                ></el-date-picker>
                
                <el-button
                    style="margin-left: 10px; margin-top: 10px;"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
                <el-button
                    style="margin-left: 10px; margin-top: 10px;"
                    type="primary"
                    @click="addRefundDialogStatus = true"
                    >创建退货单</el-button
                >
                <el-button
                    style="
                        background-color: #00acac;
                        color: #fff;
                        border-color: #00acac;
                        margin-left: 10px; margin-top: 10px;
                    "
                    @click="openImportStatus = true"
                    >导入退货单</el-button
                >
                <el-button type="danger" @click="batchCancel"  style="margin-left: 10px; margin-top: 10px;"
                    >批量撤销</el-button
                >
                <el-button
                    style="margin-left: 10px; margin-top: 10px;"
                    type="info"
                    @click="exportOrder"
                    >导出</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table
                :data="tableData"
                stripe
                @selection-change="handleSelectionChange"
                @sort-change="sortChange"
                border
                style="width: 100%"
            >
                <el-table-column type="selection" width="55" align="center">
                    <!-- <template v-if="row.row.is_cancel_order == 0" slot-scope="row">
                    
                </template> -->
                </el-table-column>
                <!-- @selection-change="handleSelectionChange" -->
                <CompanyTableColumn></CompanyTableColumn>
                <el-table-column
                    prop="return_code"
                    label="退货单号"
                    align="center"
                    width="220"
                >
                </el-table-column>
                <el-table-column
                    prop="order_no"
                    label="商户订单号"
                    width="220"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="wy_no"
                    label="运单号"
                    width="180"
                    align="center"
                >
                    <template slot-scope="row">
                        <div
                            v-for="(item, index) in row.row.wy_no"
                            :key="index"
                        >
                            {{ item }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="creator"
                    label="制单人"
                    width="110"
                >
                </el-table-column>
                
                <el-table-column
                    align="center"
                    prop="fictitious_name"
                    label="仓库"
                    width="200"
                >
                </el-table-column>

                <el-table-column
                    prop="status"
                    label="退货单状态"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="user_remark"
                    label="备注"
                    width="200"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="remake"
                    align="center"
                    label="操作员备注"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="create_time"
                    sortable="custom"
                    label="创建时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="finish_time"
                    label="完成时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column label="操作" width="220" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="viewGoods(scope.row)"
                            >查看商品</el-button
                        >
                        <el-button
                            type="warning"
                            size="mini"
                            @click="changeStatus(scope.row)"
                            :disabled="
                                !['部分确认', '待确认'].includes(
                                    scope.row.status
                                )
                            "
                            >修改状态</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100, 1000]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog
            center
            title="请上传需要导入的退货单"
            :visible.sync="openImportStatus"
            width="40%"
        >
            <el-link
                href="https://images.vinehoo.com/wms/order/退货单模版.xlsx"
                target="_blank"
                type="warning"
                >下载模版<i class="el-icon-download el-icon--right"></i>
            </el-link>
            <div class="el-upload__tip">只能上传xlsx文件，且不超过10MB</div>

            <span slot="footer" class="dialog-footer">
                <div>
                    <el-button @click="openImportStatus = false"
                        >取 消</el-button
                    >
                </div>
                <el-upload
                    class="upload-demo"
                    action="/admin/stock/returns/import"
                    :multiple="false"
                    name="exclFile"
                    :show-file-list="false"
                    :headers="headers"
                    :limit="999"
                    :on-success="onSuccessAnnex"
                    :before-upload="beforeAvatarUploadAnnex"
                >
                    <!-- :on-success="onSuccess" -->

                    <el-button type="primary">上 传</el-button>
                </el-upload>
            </span>
        </el-dialog>
        <div>
            <el-dialog
                title="创建退货单"
                :visible.sync="addRefundDialogStatus"
                width="900px"
                :close-on-click-modal="false"
                center
            >
                <add @close="close" v-if="addRefundDialogStatus"></add>
            </el-dialog>
        </div>
        <div>
            <el-dialog
                title="查看商品"
                :visible.sync="viewGoodsDialogStatus"
                width="70%"
                center
            >
                <refundGoodsList
                    v-if="viewGoodsDialogStatus"
                    :return_id="return_id"
                ></refundGoodsList>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import fileDownload from "js-file-download";
import { Loading } from "element-ui";
import Cookies from "js-cookie";
import refundGoodsList from "./goodsList.vue";
import add from "./add.vue";
import CompanySelectGroup from "@/components/CompanySelectGroup";
import CompanyTableColumn from "@/components/CompanyTableColumn";
export default {
    components: {
        refundGoodsList,
        add,
        CompanySelectGroup,
        CompanyTableColumn,
    },
    data() {
        return {
            openImportStatus: false,
            headers: {
                warehousecheckval: Cookies.get("stock_id"),
                securitycheckval: Cookies.get("token"),
            },
            orderStatusOptions: [
                {
                    label: "待上架",
                    value: 1,
                },
                {
                    label: "上架中",
                    value: 2,
                },
                {
                    label: "已完成",
                    value: 3,
                },
                {
                    label: "已撤销",
                    value: 4,
                },
                {
                    label: "待确认",
                    value: 5,
                },
                {
                    label: "部分确认",
                    value: 6,
                },
                // {
                //     label: "待认领",
                //     value: 0,
                // },
                // {
                //     label: "待签收",
                //     value: 1,
                // },
                // {
                //     label: "清点中",
                //     value: 2,
                // },
                // {
                //     label: "待审核",
                //     value: 3,
                // },
                // {
                //     label: "待上架",
                //     value: 4,
                // },
                // {
                //     label: "已完成",
                //     value: 5,
                // },
                // {
                //     label: "已撤销",
                //     value: 6,
                // },
                // {
                //     label: "已驳回",
                //     value: 7,
                // },
            ],
            status: "",
            viewGoodsDialogStatus: false,
            addRefundDialogStatus: false,
            return_id: "",
            field: "",
            bar_code: "",
            short_code: "",
            keyword: "",
            orderBy: "",
            fictitious_id: [],
            fictitiousList: [],
            page: 1,
            limit: 10,
            searchOptions: [
                {
                    value: "order_no",
                    label: "商户订单号",
                },
                {
                    value: "return_code",
                    label: "退货单号",
                },

                {
                    value: "wy_no",
                    label: "运单号",
                },
            ],
            column: "order_no",
            options: [],
            cascaderValue: [],
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            // eslint-disable-next-line camelcase
            area_id: "",
            tableData: [],
            formLabelWidth: "120px",
            multipleSelection: [],
            corp: "",
            creatTime:null,
            completeTime:null,
        };
    },
    mounted() {
        // this.getStockLinkage();
        this.getFictitiousList();
        this.refundOrderList();
    },
    methods: {
        exportOrder() {
            let data = {
                orderBy: this.orderBy,
                bar_code: this.bar_code,
                short_code: this.short_code,
                field: this.field,
                status: this.status,
                column: this.column,
                // eslint-disable-next-line camelcase
                order_type: 1,
                // eslint-disable-next-line camelcase
                // location_id: this.location_id,
                // // eslint-disable-next-line camelcase
                // area_id: this.area_id,
                fictitious_id: this.fictitious_id.join(','),
                keyword: this.keyword,
                corp: this.corp,
                create_stime:this.creatTime ? this.creatTime[0] : "",
                create_etime:this.creatTime ? this.creatTime[1] : "",
                finish_stime:this.completeTime ? this.completeTime[0] : "",
                finish_etime:this.completeTime ? this.completeTime[1] : "",
            };
            this.$request.order.returnExport(data).then((res) => {
                if (res.data.size < 1024) {
                    this.$message.error("没有权限");
                } else {
                    this.$message.success("导出成功");
                    fileDownload(res.data, "退货单.xlsx");
                }
            });
        },
        sortChange(val) {
            console.log(val);
            if (val.order) {
                this.field = "create_time";
                if (val.order == "ascending") {
                    this.orderBy = "asc";
                } else {
                    this.orderBy = "desc";
                }
            } else {
                this.field = "";
                this.orderBy = "";
            }
            this.refundOrderList();
        },
        viewGoods(row) {
            this.viewGoodsDialogStatus = true;
            this.return_id = row.return_id;
            // this.$router.push({
            //     path: "/refundGoods",
            //     query: {
            //         // eslint-disable-next-line camelcase
            //         return_id: row.return_id
            //     }
            // });
        },

        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection);
        },
        getStockLinkage() {
            this.$request.stock.getStockLinkage(1).then((res) => {
                this.options = res.data.data;
            });
        },
        search() {
            this.page = 1;
            this.refundOrderList();
        },
        beforeAvatarUploadAnnex(file) {
            const type = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log(type);
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M || type !== "xlsx") {
                if (!isLt10M) {
                    this.$message.error("上传文件大小不能超过 10MB!");
                } else {
                    this.$message.error("上传文件类型错误 请重新上传!");
                }
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading",
                });
            }
            return isLt10M && type === "xlsx";
        },
        getFictitiousList() {
            let data = {
                page: 1,
                limit: 5000,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.fictitiousList = res.data.data.list;
                }
            });
        },
        onSuccessAnnex(res, file) {
            if (res.errorCode == 0) {
                console.log(file.response.data);
                this.refundOrderList();
                this.openImportStatus = false;
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        async batchCancel() {
            let return_ids = [];
            this.multipleSelection.map((item) => {
                return_ids.push(item.return_id);
            });
            const data = {
                return_ids,
            };

            console.log(data);
            const res = await this.$request.order.CancelReturnReceiptOrder(
                data
            );
            if (res.data.errorCode == 0) {
                this.$message.success("操作成功");
                this.multipleSelection = [];
                this.search();
            }
        },
        refundOrderList() {
            let data = {
                page: this.page,
                orderBy: this.orderBy,
                bar_code: this.bar_code,
                short_code: this.short_code,
                field: this.field,
                limit: this.limit,
                status: this.status,
                column: this.column,
                // eslint-disable-next-line camelcase
                order_type: 1,
                // eslint-disable-next-line camelcase
                // location_id: this.location_id,
                // // eslint-disable-next-line camelcase
                // area_id: this.area_id,
                fictitious_id: this.fictitious_id.join(','),
                keyword: this.keyword,
                corp: this.corp,
                create_stime:this.creatTime ? this.creatTime[0] : "",
                create_etime:this.creatTime ? this.creatTime[1] : "",
                finish_stime:this.completeTime ? this.completeTime[0] : "",
                finish_etime:this.completeTime ? this.completeTime[1] : "",
            };
            this.$request.order.refundOrderList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },

        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
            this.page = 1;

            this.limit = val;
            this.refundOrderList();
        },
        close() {
            this.addRefundDialogStatus = false;
            this.refundOrderList();
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.refundOrderList();
        },
        async changeStatus(row) {
            try {
                await this.$confirm("确定要修改此退货单的状态吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                });

                const res = await this.$request.order.changeStatus({
                    return_id: row.return_id,
                });
                if (res.data.errorCode === 0) {
                    this.$message.success("状态修改成功");
                    this.refundOrderList(); // 刷新列表
                } else {
                    this.$message.error(res.data.msg || "状态修改失败");
                }
            } catch (error) {
                if (error !== "cancel") {
                    console.error("修改状态时出错:", error);
                    this.$message.error("修改状态时出错");
                }
            }
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.area-layout {
    .dialog-footer {
        display: flex;
        justify-content: center;
        .el-button {
            margin: 0 10px;
        }
    }
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
