<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            :inline="true"
            class="demo-ruleForm"
        >
            <el-form-item label="商户订单号" prop="order_no">
                <el-input class="w-300" v-model="ruleForm.order_no"></el-input>
            </el-form-item>
            <!-- <el-form-item label="实体仓编号" prop="store_code">
                <el-input
                    class="w-300"
                    v-model="ruleForm.store_code"
                ></el-input>
            </el-form-item> -->
            <el-form-item label="退货运单号" prop="wy_no">
                <el-select
                    style="width: 400px"
                    v-model="ruleForm.wy_no"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    placeholder="请输入退货运单号"
                >
                    <el-option
                        v-for="item in wy_noOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="联系人" prop="contacts_name">
                <el-input
                    class="w-300"
                    v-model="ruleForm.contacts_name"
                ></el-input>
            </el-form-item>
            <el-form-item label="联系人电话" prop="contacts_phone">
                <el-input
                    class="w-300"
                    v-model="ruleForm.contacts_phone"
                ></el-input>
            </el-form-item>
            <el-form-item label="联系人地址" prop="contacts_address">
                <el-input
                    style="width: 350px"
                    v-model="ruleForm.contacts_address"
                ></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="user_remark">
                <el-input
                    style="width: 500px"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    placeholder="请输入备注"
                    maxlength="80"
                    show-word-limit
                    v-model="ruleForm.user_remark"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="商品条码" prop="goods" style="display: block">
                <el-select
                    filterable
                    v-model="goods_arr"
                    remote
                    style="width: 650px"
                    clearable
                    multiple
                    @change="goodsChange"
                    :loading="loadings"
                    reserve-keyword
                    placeholder="商品条码（至少3位）"
                    :remote-method="remoteMethod"
                >
                    <el-option
                        v-for="item in goodsOptions"
                        :key="item.bar_code"
                        :label="item.goods_name + '/' + item.bar_code"
                        :value="item.bar_code"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="公司" prop="corp">
                <CompanySelectGroup
                    v-model="ruleForm.corp"
                ></CompanySelectGroup>
            </el-form-item>
            <div style="margin-bottom: 20px">
                <el-alert
                    title="提示：请先选择商品后，再选择虚拟仓库"
                    type="warning"
                    center
                    :show-icon="true"
                    :closable="false"
                >
                </el-alert>
                <div
                    v-for="(item, index) in ruleForm.goods"
                    :key="index"
                    style="margin-top: 10px"
                >
                    <el-tag>{{ item.goods_name }}/{{ item.bar_code }}</el-tag>
                    <el-input-number
                        style="margin-left: 10px"
                        v-model="item.number"
                        size="mini"
                        :min="1"
                        label="请输入数量"
                        :precision="0"
                    ></el-input-number>
                    <el-select
                        style="margin-left: 10px"
                        filterable
                        v-model="item.fictitious"
                        placeholder="请选择虚拟仓"
                    >
                        <el-option
                            v-for="item in fictitiousOptions"
                            :key="item.fictitious_id"
                            :disabled="item.fictitious_pid === 0"
                            :label="item.fictitious_name"
                            :value="item.fictitious_id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </div>

            <div class="center">
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >立即创建</el-button
                >
                <el-button @click="$emit('close')">取消</el-button>
            </div>
        </el-form>
    </div>
</template>
<script>
import CompanySelectGroup from "@/components/CompanySelectGroup";

export default {
    components: {
        CompanySelectGroup,
    },
    data() {
        return {
            fictitiousOptions: [],
            goods_arr: [],
            loadings: false,
            goodsOptions: [],
            wy_noOptions: [],
            ruleForm: {
                wy_no: "",
                contacts_name: "",
                contacts_phone: "",
                contacts_address: "",
                // store_code: "",
                order_no: "",
                goods: [],
                user_remark: "",
                corp: "",
            },
            rules: {
                goods: [
                    {
                        required: true,
                        message: "请选择商品",
                        trigger: "blur",
                    },
                ],
                order_no: [
                    {
                        required: true,
                        message: "请输入商户订单号",
                        trigger: "blur",
                    },
                ],
                // store_code: [
                //     {
                //         required: true,
                //         message: "请输入实体仓编号",
                //         trigger: "change"
                //     }
                // ],
                wy_no: [
                    {
                        required: true,
                        message: "退货运单号必须填写",
                        trigger: "blur",
                    },
                ],
                corp: [
                    {
                        required: true,
                        message: "请选择公司",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    mounted() {
        this.getVirtualOptions();
    },
    methods: {
        getVirtualOptions() {
            let data = {
                page: 1,
                limit: 999,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    // this.tableData = res.data.data.list;
                    // let obj = {
                    //     // eslint-disable-next-line camelcase
                    //     fictitious_name: "顶级",
                    //     // eslint-disable-next-line camelcase
                    //     fictitious_id: 0,
                    //     // eslint-disable-next-line camelcase
                    //     fictitious_pid: 0
                    //     // eslint-disable-next-line camelcase
                    // };
                    this.fictitiousOptions = res.data.data.list;
                    // this.fictitiousOptions.unshift(obj);
                }
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                    };
                    this.$request.order.addRefundOrder(data).then((res) => {
                        if (res.data.errorCode == 0) {
                            this.$emit("close");

                            this.$message.success("创建成功");
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        goodsChange(val) {
            console.log(val);
            this.ruleForm.goods = [];
            val.map((i) => {
                let data = {
                    page: 1,
                    // eslint-disable-next-line camelcase
                    bar_code: i,
                    limit: 100,
                };
                this.$request.goods.getGoodsList(data).then((res) => {
                    if (res.data.errorCode == 0) {
                        let resData = res.data.data.list;
                        resData.map((r, index) => {
                            if (r.bar_code == i) {
                                let data = {
                                    en_goods_name: resData[index].en_goods_name,
                                    goods_name: resData[index].goods_name,
                                    bar_code: resData[index].bar_code,
                                    short_code: resData[index].short_code,
                                    goods_years: resData[index].goods_years,
                                    capacity: resData[index].capacity,
                                    number: 1,
                                    fictitious: "",
                                };
                                this.ruleForm.goods.push(data);
                            }
                        });
                    }
                });
            });
        },
        remoteMethod(query) {
            console.log(query, 11);
            if (query.length >= 3) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        page: 1,
                        // eslint-disable-next-line camelcase
                        bar_code: query,
                        limit: 999,
                    };
                    this.$request.goods.getGoodsList(data).then((res) => {
                        console.log(res.data);
                        this.loadings = false;
                        if (res.data.errorCode == 0) {
                            this.goodsOptions = res.data.data.list;
                            console.log(this.goodsOptions);
                        }
                    });
                }, 300);
            } else {
                this.goodsOptions = [];
            }

            console.log(this.goodsOptions);
        },
    },
};
</script>

<style lang="scss" scoped>
.w-300 {
    width: 200px;
}

.center {
    text-align: center;
}
</style>
