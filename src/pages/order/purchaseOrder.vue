<template>
    <div>
        <el-row type="flex" style="margin-bottom: 20px">
            <div
                v-for="item in statsList"
                :key="item.type"
                :style="{
                    marginRight: '30px',
                    cursor: 'pointer',
                }"
                @click="reloadByQueryStatus(item.type)"
            >
                <div
                    :style="{
                        position: 'relative',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '240px',
                        height: '110px',
                        background: item.background,
                        borderRadius: '8px',
                    }"
                >
                    <div
                        :style="{
                            position: 'absolute',
                            top: '5px',
                            left: '10px',
                            fontWeight: 700,
                            fontSize: '14px',
                            color: 'rgba(255, 255, 255, 1)',
                        }"
                    >
                        {{ item.title }}
                    </div>
                    <div
                        :style="{
                            fontWeight: 700,
                            fontSize: '20px',
                            color: 'rgba(255, 255, 255, 1)',
                        }"
                    >
                        {{ item.text }}
                    </div>
                </div>
                <div style="text-align: center">
                    <el-button type="text" size="small">查看详情</el-button>
                </div>
            </div>
        </el-row>

        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.order_no"
                    placeholder="采购订单号"
                    clearable
                    @keyup.enter.native="reload"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.supplier"
                    placeholder="供应商"
                    clearable
                    @keyup.enter.native="reload"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.bar_code"
                    placeholder="条码"
                    clearable
                    @keyup.enter.native="reload"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                    clearable
                    @keyup.enter.native="reload"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.goods_name"
                    placeholder="商品中文名"
                    clearable
                    @keyup.enter.native="reload"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="purchaseTime"
                    type="daterange"
                    start-placeholder="采购开始时间"
                    end-placeholder="采购结束时间"
                    value-format="yyyy-MM-dd"
                    @change="reload"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
            </el-form-item>
        </el-form>

        <div style="font-size: 14px; color: rgb(16, 16, 16)">
            {{ searchMethodText }}
        </div>
        <el-table
            :data="list"
            border
            show-summary
            :summary-method="getSummaries"
            :header-cell-class-name="headerCellClassName"
            @sort-change="onSortChange"
        >
            <el-table-column align="center" prop="rd_code" label="采购订单">
            </el-table-column>
            <el-table-column align="center" prop="supplier" label="供应商">
            </el-table-column>
            <el-table-column align="center" prop="bar_code" label="条码">
            </el-table-column>
            <el-table-column
                align="center"
                prop="short_code"
                label="简码"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="goods_name"
                label="商品中文名"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="create_time"
                label="采购时间"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="up_nums"
                label="已上架数"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="pending_nums"
                label="待处理数量（瓶）"
                min-width="130"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="fic_nums"
                label="现存量"
                sortable="custom"
            >
            </el-table-column>
            <el-table-column align="center" prop="residual_rate" label="剩余率">
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
    </div>
</template>

<script>
export default {
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            order_no: "",
            supplier: "",
            bar_code: "",
            short_code: "",
            goods_name: "",
            purchase_stime: "",
            purchase_etime: "",
            sort: [],
            query_status: 0,
        },
        list: [],
        total: 0,
        totalUpNums: 0,
        totalPendingNums: 0,
        totalFicNums: 0,
        statsList: [],
    }),
    computed: {
        purchaseTime: {
            get() {
                if (!this.query.purchase_stime || !this.query.purchase_etime) {
                    return "";
                }
                return [this.query.purchase_stime, this.query.purchase_etime];
            },
            set(range) {
                const [stime = "", etime = ""] = range || [];
                this.query.purchase_stime = stime;
                this.query.purchase_etime = etime;
            },
        },
        searchMethodText({ query, statsList }) {
            let text = "";
            if (query.query_status) {
                text =
                    statsList.find((item) => item.type === query.query_status)
                        ?.title || "";
            } else {
                text = "筛选条件";
            }
            return `搜索方式：${text}`;
        },
    },
    created() {
        this.load();
    },
    methods: {
        load() {
            const sort = this.query.sort.map((item) => ({
                ...item,
                order: item.order === "ascending" ? "asc" : "desc",
            }));
            this.$request.order
                .getPurchaseOrderList({ ...this.query, sort })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        const {
                            list,
                            total,
                            total_up_nums,
                            total_pending_nums,
                            total_fic_nums,
                            order_num_statis,
                        } = res.data.data;
                        this.list = list;
                        this.total = total;
                        this.totalUpNums = total_up_nums;
                        this.totalPendingNums = total_pending_nums;
                        this.totalFicNums = total_fic_nums;

                        const {
                            total_order = { order_num: 0, sku_num: 0 },
                            handled_order = { order_num: 0, sku_num: 0 },
                            check_order = { order_num: 0, sku_num: 0 },
                        } = order_num_statis;
                        this.statsList = [
                            {
                                type: 1,
                                title: "系统入库单",
                                text: `${total_order.order_num} 单 / ${total_order.sku_num} SKU`,
                                background: "rgb(32, 146, 137)",
                            },
                            {
                                type: 2,
                                title: "已处理入库单",
                                text: `${handled_order.order_num} 单 / ${handled_order.sku_num} SKU`,
                                background: "rgb(112, 181, 116)",
                            },
                            {
                                type: 3,
                                title: "清点中入库单",
                                text: `${check_order.order_num} 单 / ${check_order.sku_num} SKU`,
                                background: "rgb(255, 99, 79)",
                            },
                        ];
                    }
                });
        },
        reload() {
            this.query.page = 1;
            this.query.query_status = 0;
            this.load();
        },
        reloadByQueryStatus(queryStatus) {
            this.query = Object.assign({}, this.query, {
                page: 1,
                query_status: queryStatus,
                order_no: "",
                supplier: "",
                bar_code: "",
                short_code: "",
                goods_name: "",
                purchase_stime: "",
                purchase_etime: "",
            });
            this.load();
        },
        getSummaries(param) {
            const { columns } = param;
            const sums = [];
            columns.forEach((column, index) => {
                const { property } = column;
                if (index === 0) {
                    sums[index] = "合计";
                    return;
                }
                switch (property) {
                    case "up_nums":
                        sums[index] = this.totalUpNums;
                        return;
                    case "pending_nums":
                        sums[index] = this.totalPendingNums;
                        return;
                    case "fic_nums":
                        sums[index] = this.totalFicNums;
                        return;
                }
                sums[index] = "";
            });

            return sums;
        },
        headerCellClassName({ column }) {
            const findSort = this.query.sort.find(
                (item) => item.field === column.property
            );
            if (findSort) {
                column.order = findSort.order;
            }
        },
        onSortChange({ prop, order }) {
            const func = () => {
                const findItem = this.query.sort.find(
                    (item) => item.field === prop
                );
                if (findItem) {
                    findItem.order = order;
                } else {
                    this.query.sort.push({ field: prop, order });
                }
            };
            if (order) {
                func();
            } else {
                const findIndex = this.query.sort.findIndex(
                    (item) => item.field === prop
                );
                if (findIndex !== -1) {
                    this.query.sort.splice(findIndex, 1);
                }
            }
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>
