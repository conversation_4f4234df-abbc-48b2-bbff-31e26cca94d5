<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            :inline="true"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
        >
            <el-form-item label="订单号" prop="orderno">
                <el-input v-model="ruleForm.orderno"></el-input>
            </el-form-item>
            <el-form-item label="平台" prop="platform">
                <el-select v-model="ruleForm.platform" placeholder="请选择平台">
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="(item, index) in platformList"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="寄件方式" prop="logistics_type">
                <el-select
                    v-model="ruleForm.logistics_type"
                    @change="ruleForm.logistics_id = ''"
                    placeholder="请选择寄件方式"
                >
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="(item, index) in logisticsTypeOptions"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item
                label="快递公司"
                prop="logistics_id"
                v-if="ruleForm.logistics_type == 1"
            >
                <el-select
                    v-model="ruleForm.logistics_id"
                    placeholder="请选择快递公司"
                >
                    <el-option
                        :label="item.logistics_company"
                        :value="item.id"
                        v-for="(item, index) in expressList"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="是否到付">
                <el-switch v-model="is_topay"></el-switch>
            </el-form-item>

            <el-form-item label="虚拟仓" prop="fictitious_id">
                <el-select
                    v-model="ruleForm.fictitious_id"
                    placeholder="请选择虚拟仓"
                >
                    <el-option
                        :disabled="item.fictitious_pid === 0"
                        :label="item.fictitious_name"
                        :value="item.fictitious_id"
                        v-for="(item, index) in fictitiousList"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="订单类型" prop="order_type">
                <el-select
                    v-model="ruleForm.order_type"
                    placeholder="请选择订单类型"
                >
                    <el-option label="用户订单" :value="0"></el-option>
                    <el-option label="调拨订单" :value="1"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="收件地" prop="selectedOptions">
                <el-cascader
                    size="large"
                    :options="options"
                    style="width:300px"
                    v-model="ruleForm.selectedOptions"
                    @change="handleChange"
                >
                </el-cascader>
            </el-form-item>
            <el-form-item label="详细地址" prop="address">
                <el-input
                    style="width:300px"
                    type="textarea"
                    maxlength="100"
                    show-word-limit
                    :rows="5"
                    v-model="ruleForm.address"
                ></el-input>
            </el-form-item>
            <el-form-item label="支付金额" prop="pay_money">
                <el-input-number
                    :min="0"
                    :max="9999999"
                    v-model="ruleForm.pay_money"
                ></el-input-number>
                元
            </el-form-item>

            <el-form-item label="收件人姓名" prop="receiver_name">
                <el-input v-model="ruleForm.receiver_name"></el-input>
            </el-form-item>
            <el-form-item label="商品信息" prop="product">
                <el-button type="success" @click="addGoods">添加商品</el-button>
                <el-card
                    v-for="(item, index) in ruleForm.attach_info"
                    :key="index"
                >
                    <el-tag v-if="item.goods_name"
                        >商品名称：{{ item.goods_name }}</el-tag
                    >
                    <el-tag v-if="item.bar_code"
                        >商品条码：{{ item.bar_code }}</el-tag
                    >
                    <el-tag v-if="item.short_code"
                        >商品简码：{{ item.short_code }}</el-tag
                    >
                    <el-tag type="warning">商品数量：{{ item.number }}</el-tag>

                    <el-tag type="danger" v-if="item.jd_emg_code"
                        >京东EMG码：{{ item.jd_emg_code }}</el-tag
                    >
                    <el-button
                        @click="removeInfo(item, index)"
                        size="mini"
                        type="danger"
                        icon="el-icon-close"
                        circle
                    ></el-button>
                </el-card>
                <el-card v-for="(item, index) in ruleForm.product" :key="index">
                    <el-tag v-if="item.bar_code"
                        >商品条码：{{ item.bar_code }}</el-tag
                    >
                    <el-tag type="info" v-if="item.goods_id"
                        >商品期数：{{ item.goods_id }}</el-tag
                    >

                    <el-tag type="warning">商品数量：{{ item.number }}</el-tag>
                    <el-tag type="danger" v-if="item.jd_emg_code"
                        >京东EMG码：{{ item.jd_emg_code }}</el-tag
                    >
                    <el-button
                        @click="removeGoods(item, index)"
                        size="mini"
                        type="danger"
                        icon="el-icon-close"
                        circle
                    ></el-button>
                </el-card>
            </el-form-item>
            <el-form-item label="收件人手机号" prop="receiver_phone">
                <el-input v-model="ruleForm.receiver_phone"></el-input>
            </el-form-item>

            <div class="center">
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >立即创建</el-button
                >
                <el-button @click="$emit('close')">取消</el-button>
            </div>
        </el-form>
        <el-dialog
            title="添加商品"
            append-to-body
            :visible.sync="addGoodsStatus"
            width="30%"
        >
            <el-form
                :model="goodsData"
                :rules="goodsDataRules"
                ref="goodsData"
                label-width="130px"
                class="demo-goodsData"
            >
                <el-form-item label="添加类型">
                    <el-radio
                        @change="addGoodsTypeChange"
                        v-model="addGoodsType"
                        :label="1"
                        >商品信息</el-radio
                    >
                    <el-radio
                        @change="addGoodsTypeChange"
                        v-model="addGoodsType"
                        :label="2"
                        >附加信息</el-radio
                    >
                </el-form-item>

                <el-form-item
                    v-if="addGoodsType == 2"
                    label="商品名称"
                    prop="goods_name"
                >
                    <el-input v-model="goodsData.goods_name"></el-input>
                </el-form-item>
                <el-form-item label="商品条码" prop="bar_code">
                    <el-input v-model="goodsData.bar_code"></el-input>
                </el-form-item>
                <el-form-item
                    label="商品简码"
                    prop="short_code"
                    v-if="addGoodsType == 2"
                >
                    <el-input v-model="goodsData.short_code"></el-input>
                </el-form-item>
                <el-form-item
                    label="商品期数"
                    prop="goods_id"
                    v-if="addGoodsType == 1"
                >
                    <el-input v-model="goodsData.goods_id"></el-input>
                </el-form-item>
                <!-- <el-form-item label="京东EMG码" prop="jd_emg_code">
                    <el-input
                        placeholder="京东EMG码"
                        v-model="goodsData.jd_emg_code"
                    ></el-input>
                </el-form-item> -->
                <el-form-item label="数量" prop="number">
                    <el-input-number
                        v-model="goodsData.number"
                        :step="1"
                        :min="1"
                    ></el-input-number>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addGoodsStatus = false">取 消</el-button>
                <el-button type="primary" @click="submitFormGoods('goodsData')"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { regionData, CodeToText } from "element-china-area-data";

export default {
    data() {
        return {
            options: regionData,
            goodsData: {
                goods_name: "",
                jd_emg_code: "",
                // eslint-disable-next-line camelcase
                goods_id: "",
                number: 1,
                // eslint-disable-next-line camelcase
                bar_code: ""
            },
            addGoodsType: 1,
            // attach_info: {
            //     // 附加信息
            //     goods_name: "",
            //     jd_emg_code: "",
            //     // eslint-disable-next-line camelcase
            //     goods_id: "",
            //     number: 1,
            //     // eslint-disable-next-line camelcase
            //     bar_code: ""
            // },
            addGoodsStatus: false,
            goodsDataRules: {
                short_code: [
                    {
                        required: false,
                        message: "请正确输入简码",
                        trigger: "blur"
                    }
                ],
                number: [
                    {
                        required: true,
                        message: "请正确输入数量",
                        trigger: "blur"
                    }
                ],
                goods_name: [
                    {
                        required: false,
                        message: "请输入商品名称",
                        trigger: "blur"
                    }
                ],
                // eslint-disable-next-line camelcase
                bar_code: [
                    {
                        required: true,
                        message: "商品条码不能为空",
                        trigger: "blur"
                    }
                ]
            },
            logisticsTypeOptions: [
                {
                    name: "快递",
                    id: 1
                },
                {
                    name: "物流",
                    id: 2
                }
            ],
            is_topay: false,
            ruleForm: {
                selectedOptions: [],
                orderno: "",
                attach_info: [],
                logistics_type: 1,
                province: "",
                product: [],
                platform: "",
                // eslint-disable-next-line camelcase
                // eslint-disable-next-line camelcase
                order_type: 0,
                // eslint-disable-next-line camelcase
                logistics_id: "",
                // eslint-disable-next-line camelcase
                fictitious_id: "",
                city: "",
                is_topay: 0,
                town: "",
                // eslint-disable-next-line camelcase
                receiver_name: "",
                address: "",
                // eslint-disable-next-line camelcase
                receiver_phone: "",
                // eslint-disable-next-line camelcase
                pay_money: 0
            },
            fictitiousList: [],
            platformList: [],
            expressList: [],
            rules: {
                logistics_type: [
                    {
                        required: true,
                        message: "请选择寄件方式",
                        trigger: "blur"
                    }
                ],
                // eslint-disable-next-line camelcase
                receiver_phone: [
                    {
                        required: true,
                        message: "请填写收件人手机号",
                        trigger: "blur"
                    }
                ],
                // eslint-disable-next-line camelcase
                receiver_name: [
                    {
                        required: true,
                        message: "请填写收件人姓名",
                        trigger: "blur"
                    }
                ],
                // eslint-disable-next-line camelcase
                pay_money: [
                    {
                        required: true,
                        message: "请输入金额",
                        trigger: "blur"
                    }
                ],
                orderno: [
                    {
                        required: true,
                        message: "请输入订单号",
                        trigger: "blur"
                    }
                ],
                platform: [
                    {
                        required: true,
                        message: "请选择平台",
                        trigger: "change"
                    }
                ],
                // eslint-disable-next-line camelcase

                // eslint-disable-next-line camelcase
                logistics_id: [
                    {
                        required: true,
                        message: "请选择快递公司",
                        trigger: "change"
                    }
                ],
                // eslint-disable-next-line camelcase
                fictitious_id: [
                    {
                        required: true,
                        message: "请选择虚拟仓库",
                        trigger: "change"
                    }
                ],
                // eslint-disable-next-line camelcase
                order_type: [
                    {
                        required: true,
                        message: "请选择订单类型",
                        trigger: "change"
                    }
                ],
                product: [
                    {
                        type: "array",
                        required: true,
                        message: "商品不能为空",
                        trigger: "change"
                    }
                ],
                selectedOptions: [
                    {
                        type: "array",
                        required: true,
                        message: "请选择收件地",
                        trigger: "change"
                    }
                ],

                address: [
                    {
                        required: true,
                        message: "请输入详细地址",
                        trigger: "change"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getPlatformList();
        this.getFictitiousList();
        this.getExpressList();
    },
    methods: {
        addGoodsTypeChange(val) {
            console.log(val);
            if (val == 1) {
                this.goodsDataRules.goods_name[0].required = false;
            } else {
                this.goodsDataRules.goods_name[0].required = true;
            }
        },
        addGoods() {
            this.addGoodsStatus = true;
            this.goodsData = {
                // eslint-disable-next-line camelcase
                goods_id: "",
                goods_name: "",
                short_code: "",
                number: 1,
                jd_emg_code: "",
                // eslint-disable-next-line camelcase
                bar_code: ""
            };
        },

        handleChange(value) {
            value.map((item, index) => {
                console.log(index);
                switch (index) {
                    case 0:
                        this.ruleForm.province = CodeToText[item];
                        break;
                    case 1:
                        this.ruleForm.city = CodeToText[item];
                        break;
                    case 2:
                        this.ruleForm.town = CodeToText[item];
                        break;
                }
            });
        },
        getFictitiousList() {
            let data = {
                page: 1,
                limit: 5000
            };
            this.$request.stock.getVirtualList(data).then(res => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.fictitiousList = res.data.data.list;
                }
            });
        },
        removeGoods(item, index) {
            console.log(item);
            this.ruleForm.product.splice(index, 1);
        },
        removeInfo(item, index) {
            console.log(item);
            this.ruleForm.attach_info.splice(index, 1);
        },
        getPlatformList() {
            this.$request.order.getPlatformList().then(res => {
                if (res.data.errorCode == 0) {
                    this.platformList = res.data.data.list;
                }
            });
        },
        getExpressList() {
            this.$request.order.getExpressList().then(res => {
                if (res.data.errorCode == 0) {
                    this.expressList = res.data.data.list;
                }
            });
        },
        submitFormGoods(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    if (this.addGoodsType == 1) {
                        this.ruleForm.product.push(this.goodsData);
                    } else {
                        this.ruleForm.attach_info.push(this.goodsData);
                    }

                    this.addGoodsStatus = false;
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    if (this.is_topay) {
                        // eslint-disable-next-line camelcase
                        this.ruleForm.is_topay = 1;
                    } else {
                        // eslint-disable-next-line camelcase
                        this.ruleForm.is_topay = 0;
                    }
                    this.$request.order
                        .createOutBound(this.ruleForm)
                        .then(res => {
                            if (res.data.errorCode == 0) {
                                console.log(res);
                                this.$message.success("新增订单成功");
                                this.$emit("close");
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.el-input {
    width: 195px !important;
}
.el-card {
    position: relative;
    .el-button {
        position: absolute;
        right: 8px;
        top: 6px;
    }
    margin-top: 5px;
    .el-tag {
        margin-right: 10px;
    }
    width: 100%;
}
.el-form-item {
    width: 48%;
}
.center {
    text-align: center;
}
</style>
