<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            :inline="true"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
        >
            <el-form-item label="订单号" prop="ordersn">
                <el-input v-model="ruleForm.ordersn" disabled></el-input>
            </el-form-item>
            <el-form-item label="平台" prop="platform">
                <el-select v-model="ruleForm.platform" placeholder="请选择平台">
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="(item, index) in platformList"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="寄件方式" prop="logistics_type">
                <el-select
                    v-model="ruleForm.logistics_type"
                    @change="ruleForm.logistics_id = ''"
                    placeholder="请选择寄件方式"
                >
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="(item, index) in logisticsTypeOptions"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item label="快递公司" prop="logistics_id">
                <el-select
                    v-model="ruleForm.logistics_id"
                    placeholder="请选择快递公司"
                >
                    <el-option
                        :label="item.logistics_company"
                        :value="item.id"
                        v-for="(item, index) in expressList"
                        :key="index"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="是否到付">
                <el-switch
                    v-model="ruleForm.is_topay"
                    active-color="#13ce66"
                    :active-value="1"
                    :inactive-value="0"
                >
                </el-switch>
            </el-form-item>
            <el-form-item label="是否加急">
                <el-switch
                    v-model="ruleForm.is_expedited"
                    active-color="#13ce66"
                    :active-value="1"
                    :inactive-value="0"
                ></el-switch>
            </el-form-item>
            <el-form-item label="是否暂存">
                <el-switch
                    v-model="ruleForm.storage"
                    active-color="#13ce66"
                    :active-value="1"
                    :inactive-value="0"
                ></el-switch>
            </el-form-item>
            <el-form-item label="省" prop="province">
                <el-input v-model="ruleForm.province"></el-input>
            </el-form-item>
            <el-form-item label="市" prop="city">
                <el-input v-model="ruleForm.city"></el-input>
            </el-form-item>
            <el-form-item label="区" prop="town">
                <el-input v-model="ruleForm.town"></el-input>
            </el-form-item>
            <el-form-item label="详细地址" prop="address">
                <el-input
                    style="width: 300px"
                    type="textarea"
                    maxlength="100"
                    show-word-limit
                    :rows="5"
                    v-model="ruleForm.address"
                ></el-input>
            </el-form-item>
            <!-- <el-form-item label="收件地" prop="selectedOptions">
                <el-cascader
                    size="large"
                    :options="options"
                    style="width:300px"
                    v-model="ruleForm.selectedOptions"
                    @change="handleChange"
                >
                </el-cascader>
            </el-form-item> -->

            <el-form-item label="收件人姓名" prop="receiver_name">
                <el-input v-model="ruleForm.receiver_name"></el-input>
            </el-form-item>
            <el-form-item label="收件人手机号" prop="receiver_phone">
                <el-input
                    v-model="ruleForm.receiver_phone"
                    :placeholder="beforeUpdatePhone"
                ></el-input>
            </el-form-item>
            <div class="center">
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确认</el-button
                >
                <el-button @click="$emit('editClose')">取消</el-button>
            </div>
        </el-form>
    </div>
</template>
<script>
export default {
    props: ["data"],
    data() {
        return {
            beforeUpdatePhone: "",
            addGoodsType: 1,
            // attach_info: {
            //     // 附加信息
            //     goods_name: "",
            //     jd_emg_code: "",
            //     // eslint-disable-next-line camelcase
            //     goods_id: "",
            //     number: 1,
            //     // eslint-disable-next-line camelcase
            //     bar_code: ""
            // },

            logisticsTypeOptions: [
                {
                    name: "快递",
                    id: 1,
                },
                {
                    name: "物流",
                    id: 2,
                },
            ],

            ruleForm: {
                ordersn: "",
                platform: "",
                province: "",
                city: "",
                town: "",
                is_topay: false,

                is_expedited: false,
                storage: false,

                // eslint-disable-next-line camelcase
                // eslint-disable-next-line camelcase
                // eslint-disable-next-line camelcase
                logistics_id: "",
                // eslint-disable-next-line camelcase
                // eslint-disable-next-line camelcase
                receiver_name: "",
                address: "",
                // eslint-disable-next-line camelcase
                receiver_phone: "",
                // eslint-disable-next-line camelcase
            },
            fictitiousList: [],
            platformList: [],
            expressList: [],
            rules: {
                logistics_type: [
                    {
                        required: true,
                        message: "请选择寄件方式",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                // receiver_phone: [
                //     {
                //         required: true,
                //         message: "请填写收件人手机号",
                //         trigger: "blur"
                //     }
                // ],
                // eslint-disable-next-line camelcase
                receiver_name: [
                    {
                        required: true,
                        message: "请填写收件人姓名",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                pay_money: [
                    {
                        required: true,
                        message: "请输入金额",
                        trigger: "blur",
                    },
                ],
                id: [
                    {
                        required: true,
                        message: "请输入订单号",
                        trigger: "blur",
                    },
                ],
                platform: [
                    {
                        required: true,
                        message: "请选择平台",
                        trigger: "change",
                    },
                ],
                // eslint-disable-next-line camelcase

                // eslint-disable-next-line camelcase
                logistics_id: [
                    {
                        required: true,
                        message: "请选择快递公司",
                        trigger: "change",
                    },
                ],
                // eslint-disable-next-line camelcase
                fictitious_id: [
                    {
                        required: true,
                        message: "请选择虚拟仓库",
                        trigger: "change",
                    },
                ],
                // eslint-disable-next-line camelcase
                order_type: [
                    {
                        required: true,
                        message: "请选择订单类型",
                        trigger: "change",
                    },
                ],
                product: [
                    {
                        type: "array",
                        required: true,
                        message: "商品不能为空",
                        trigger: "change",
                    },
                ],
                selectedOptions: [
                    {
                        type: "array",
                        required: true,
                        message: "请选择收件地",
                        trigger: "change",
                    },
                ],

                town: [
                    {
                        required: true,
                        message: "请输入收货地址-区",
                        trigger: "change",
                    },
                ],
                city: [
                    {
                        required: true,
                        message: "请输入收货地址-市",
                        trigger: "change",
                    },
                ],
                province: [
                    {
                        required: true,
                        message: "请输入收货地址-省",
                        trigger: "change",
                    },
                ],
                address: [
                    {
                        required: true,
                        message: "请输入详细地址",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    mounted() {
        this.ruleForm = this.data;
        this.ruleForm.platform = Number(this.ruleForm.platform);

        this.beforeUpdatePhone = this.data.receiver_phone;
        this.ruleForm.receiver_phone = "";
        this.getPlatformList();
        this.getFictitiousList();

        this.getExpressList();
    },
    methods: {
        getFictitiousList() {
            let data = {
                page: 1,
                limit: 500,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.fictitiousList = res.data.data.list;
                }
            });
        },

        getPlatformList() {
            this.$request.order.getPlatformList().then((res) => {
                if (res.data.errorCode == 0) {
                    this.platformList = res.data.data.list;
                }
            });
        },
        getExpressList() {
            this.$request.order.getExpressList().then((res) => {
                if (res.data.errorCode == 0) {
                    this.expressList = res.data.data.list;
                }
            });
        },

        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let soure = this.ruleForm;
                    let data = {
                        id: soure.id,
                        province: this.ruleForm.province,
                        city: this.ruleForm.city,
                        town: this.ruleForm.town,
                        // receiver_phone: soure.receiver_phone,
                        receiver_name: soure.receiver_name,
                        address: soure.address,
                        platform: soure.platform,
                        logistics_id: soure.logistics_id,
                        is_topay: soure.is_topay,
                        is_expedited: soure.is_expedited,
                        storage: soure.storage,
                    };
                    if (this.ruleForm.receiver_phone) {
                        data.receiver_phone = this.ruleForm.receiver_phone;
                    }
                    console.log(data);
                    this.$request.order.updateOutOrder(data).then((res) => {
                        if (res.data.errorCode == 0) {
                            console.log(res);
                            this.$message.success("编辑成功");
                            this.$emit("editClose");
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.el-input {
    width: 195px !important;
}
.el-card {
    position: relative;
    .el-button {
        position: absolute;
        right: 8px;
        top: 6px;
    }
    margin-top: 5px;
    .el-tag {
        margin-right: 10px;
    }
    width: 100%;
}
.el-form-item {
    width: 48%;
}
.center {
    text-align: center;
}
</style>
