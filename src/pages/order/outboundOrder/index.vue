<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <div style="width: 100%">
                    <CompanySelectGroup
                        v-model="form.corp"
                    ></CompanySelectGroup>
                    <el-input
                        v-model="form.orderno"
                        clearable
                        placeholder="请输入商家订单号"
                    ></el-input>
                    <el-input
                        v-model="form.ordersn"
                        clearable
                        placeholder="请输入订单号"
                    ></el-input>

                    <el-input
                        v-model="form.logistics_no"
                        clearable
                        placeholder="请输入运单号"
                    ></el-input>
                    <el-input
                        v-model="form.goods_name"
                        clearable
                        placeholder="请输入商品名称"
                    ></el-input>
                    <el-select
                        v-model="form.status"
                        clearable
                        placeholder="请选择状态"
                    >
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-model="form.order_type"
                        clearable
                        placeholder="订单类型"
                    >
                        <el-option key="0" label="用户订单" :value="0">
                        </el-option>
                        <el-option key="1" label="调拨订单" :value="1">
                        </el-option>
                        <el-option key="2" label="合并订单" :value="2">
                        </el-option>
                    </el-select>
                    <el-select
                        v-model="form.logistics_id"
                        clearable
                        placeholder="快递公司"
                        multiple
                    >
                        <el-option
                            v-for="(item, index) in logisticsOptions"
                            :key="index"
                            :value="item.id"
                            :label="item.logistics_company"
                        >
                        </el-option>
                    </el-select>
                    <el-input
                        v-model="form.province"
                        clearable
                        placeholder="省"
                    ></el-input>
                    <el-input
                        v-model="form.city"
                        clearable
                        placeholder="市"
                    ></el-input>
                    <el-input
                        v-model="form.town"
                        clearable
                        placeholder="区"
                    ></el-input>
                    <el-input
                        v-model="form.receiver_name"
                        clearable
                        placeholder="请输入收件人姓名"
                    ></el-input
                    ><el-input
                        v-model="form.receiver_phone"
                        clearable
                        placeholder="请输入收件人手机"
                    ></el-input
                    ><el-input
                        v-model="form.goods_id"
                        clearable
                        placeholder="请输入商品期数"
                    ></el-input>
                    <el-input
                        v-model="form.bar_code"
                        clearable
                        placeholder="请输入商品条码"
                    ></el-input>
                    <el-input
                        v-model="form.short_code"
                        clearable
                        placeholder="请输入商品简码"
                    ></el-input>
                    <el-select
                        clearable
                        v-model="form.platform"
                        multiple
                        filterable
                        placeholder="请选择平台"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-model="form.fictitious_id"
                        clearable
                        filterable
                        multiple
                        placeholder="请选择虚拟仓"
                    >
                        <el-option
                            :disabled="item.fictitious_pid === 0"
                            :label="item.fictitious_name"
                            :value="item.fictitious_id"
                            v-for="(item, index) in fictitiousList"
                            :key="index"
                        ></el-option>
                    </el-select>
                    <el-select
                        clearable
                        v-model="form.is_expedited"
                        placeholder="是否加急"
                    >
                        <el-option key="0" label="否" :value="0"> </el-option>
                        <el-option key="1" label="是" :value="1">
                        </el-option> </el-select
                    ><el-select
                        v-model="form.is_topay"
                        clearable
                        placeholder="是否到付"
                    >
                        <el-option key="0" label="否" :value="0"> </el-option>
                        <el-option key="1" label="是" :value="1"> </el-option>
                    </el-select>
                    <el-select
                        v-model="form.storage"
                        clearable
                        placeholder="是否暂存"
                    >
                        <el-option key="0" label="否" :value="0"> </el-option>
                        <el-option key="1" label="是" :value="1"> </el-option>
                    </el-select>

                    <el-select
                        v-model="form.quality_type"
                        filterable
                        clearable
                        placeholder="请选择品质类型"
                    >
                        <el-option
                            v-for="item in quality_typeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-model="form.platform_type"
                        clearable
                        filterable
                        placeholder="请选择箱子类型"
                    >
                        <el-option
                            v-for="item in platformTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-model="form.is_cancel_order"
                        clearable
                        placeholder="撤单状态"
                    >
                        <el-option
                            v-for="(item, index) in isCancelOrderOptions"
                            :key="index"
                            :value="item.value"
                            :label="item.label"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-model="form.outbound_status"
                        clearable
                        placeholder="出库状态"
                    >
                        <el-option
                            v-for="(item, index) in outboundOrderOptions"
                            :key="index"
                            :value="item.value"
                            :label="item.label"
                        >
                        </el-option>
                    </el-select>
                    <el-date-picker
                        v-model="times"
                        @change="timesChange"
                        type="datetimerange"
                        range-separator="-"
                        style="margin-right: 12px; margin-top: 6px"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="创建-开始日期"
                        end-placeholder="创建-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-date-picker
                        v-model="finishTimes"
                        @change="finishTimesChange"
                        type="datetimerange"
                        range-separator="-"
                        style="margin-right: 12px; margin-top: 6px"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="出库-开始日期"
                        end-placeholder="出库-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <el-button @click="reset" style="margin-top: 6px;">重置</el-button>

                    <div class="btn">
                        <div>
                            <el-button type="primary" @click="add"
                                >新增发货单</el-button
                            >
                            <el-button
                                style="
                                    background-color: #00acac;
                                    color: #fff;
                                    border-color: #00acac;
                                "
                                @click="openImportStatus = true"
                                >导入订单</el-button
                            >

                            <el-button type="success" @click="exportTable"
                                >导出</el-button
                            >
                            <el-button
                                @click="cancelOrder"
                                :disabled="!multipleSelection.length"
                                type="danger"
                                >取消订单</el-button
                            >
                            <el-checkbox
                                v-model="fixedOperation"
                                @change="handleFixedOperationChange"
                                style="margin-left: 15px;"
                                >固定操作栏</el-checkbox
                            >
                        </div>
                        <div>
                            <el-button type="warning" @click="search"
                                >查询</el-button
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="area-main">
            <el-table
                @selection-change="handleSelectionChange"
                :data="tableData"
                border
                style="width: 100%"
            >
                <el-table-column type="selection" width="55" align="center">
                    <!-- <template v-if="row.row.is_cancel_order == 0" slot-scope="row">
                    
                </template> -->
                </el-table-column>
                <!-- <CompanyTableColumn></CompanyTableColumn> -->
                <el-table-column
                    prop="ordersn"
                    label="订单号"
                    width="550"
                    align="left"
                >
                    <template slot-scope="row">
                        <div style="text-align: left;">
                            {{ row.row.ordersn }}
                            <el-tooltip class="item" effect="dark" content="打印订单清单" placement="top-start">
                                <el-button type="primary" circle size="mini" icon="el-icon-s-order" @click="getChecklist(row.row.ordersn)"></el-button>
                            </el-tooltip>
                            <span v-if="row.row.orderno" style="margin-left: 10px; color: #666;">商家订单号：{{ row.row.orderno }}</span>
                        </div>
                        <div style="margin-top: 5px; text-align: left;">
                            <el-tag size="mini" type="danger" style="margin: 2px">{{ row.row.corp_name }}</el-tag>
                            <el-tag size="mini" type="success" style="margin: 2px">{{ row.row.platform_name }}</el-tag>
                            <el-tag v-if="row.row.logistics_company" size="mini" type="primary" style="margin: 2px">{{ row.row.logistics_company }}</el-tag>
                            <el-tag v-if="row.row.is_topay === 1" size="mini" type="warning" style="margin: 2px">到付</el-tag>
                            <el-tag v-if="row.row.is_expedited === 1" size="mini" type="danger" style="margin: 2px">加急</el-tag>
                            <el-tag v-if="row.row.storage === 1" size="mini" type="info" style="margin: 2px">暂存</el-tag>
                            <el-tag size="mini" :type="row.row.status === 5 ? 'success' : 'warning'" style="margin: 2px">{{ row.row.status | statusFormat }}</el-tag>
                            <el-tag v-if="row.row.is_cancel_order !== 0" size="mini" type="danger" style="margin: 2px">{{ row.row.is_cancel_order | cancalFormat }}</el-tag>
                            <el-tag v-if="row.row.outbound_status !== 0" size="mini" type="warning" style="margin: 2px">{{ row.row.outbound_status | outbound_statusFormat }}</el-tag>
                            <el-tag size="mini" :type="row.row.order_type === 0 ? 'primary' : 'warning'" style="margin: 2px">{{ row.row.order_type | typeFormat }}</el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="运单号" width="150" align="center">
                    <template slot-scope="row" v-if="row.row.logistics_no">
                        <el-popover
                            v-if="
                                row.row.logistics_no &&
                                JSON.parse(row.row.logistics_no) &&
                                JSON.parse(row.row.logistics_no).length > 1
                            "
                            placement="top-start"
                            width="200"
                            trigger="click"
                        >
                            <div
                                v-for="(item, index) in JSON.parse(
                                    row.row.logistics_no
                                )"
                                :key="index"
                            >
                                {{ item }}
                            </div>
                            <el-button
                                slot="reference"
                                size="mini"
                                type="primary"
                                >查看运单号</el-button
                            >
                        </el-popover>
                        <div v-else>
                            <div
                                v-for="(item, index) in JSON.parse(
                                    row.row.logistics_no
                                )"
                                :key="index"
                            >
                                {{ item }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="waybill_status"
                    label="运单状态"
                    width="100"
                >
                </el-table-column>
                <el-table-column align="center" label="子运单号" width="250">
                    <template slot-scope="row">
                        <div
                            v-for="(item, index) in row.row.son_logistics_no"
                            :key="index"
                        >
                            {{ item }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="退货运单号" width="150">
                    <template slot-scope="row">
                        <div
                            v-for="(item, index) in row.row.return_waybillno"
                            :key="index"
                        >
                            {{ item }}
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    align="center"
                    prop="package_nums"
                    label="总包裹数"
                    width="100"
                >
                </el-table-column>

                <el-table-column
                    align="center"
                    prop="logistics_company"
                    label="快递方式"
                    width="120"
                >
                </el-table-column>

                <el-table-column
                    align="center"
                    prop="fictitious_name"
                    label="虚拟仓"
                    min-width="200"
                >
                </el-table-column>

                <el-table-column
                    show-overflow-tooltip
                    align="center"
                    prop="receiver_name"
                    label="收件人"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    align="center"
                    prop="receiver_phone"
                    label="收件人电话"
                    width="120"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    show-overflow-tooltip
                    prop="province"
                    label="省"
                    width="110"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="city"
                    show-overflow-tooltip
                    label="市"
                    width="110"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="town"
                    show-overflow-tooltip
                    label="区"
                    width="110"
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    align="center"
                    prop="address"
                    label="收件人详细地址"
                    width="400"
                >
                </el-table-column>

                <el-table-column align="center" label="订单总重量" width="120">
                    <template slot-scope="row">
                        {{ row.row.weight }}kg
                    </template>
                </el-table-column>

                <el-table-column
                    align="center"
                    prop="create_time"
                    label="创建时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="complete_time"
                    label="出库时间"
                    width="180"
                >
                </el-table-column>

                <el-table-column
                    label="操作"
                    width="510"
                    align="center"
                    :fixed="fixedOperation ? 'right' : false"
                >
                    <template slot-scope="row">
                        <el-button size="mini" @click="openRemark(row.row)"
                            >查看备注</el-button
                        >
                        <el-button
                            type="warning"
                            size="mini"
                            @click="editOrder(row.row)"
                            >编辑</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="viewGoods(row.row)"
                            >查看商品</el-button
                        >
                        <el-button
                            type="info"
                            size="mini"
                            @click="viewInstruct(row.row)"
                            >查看指令</el-button
                        >

                        <el-button
                            type="danger"
                            size="mini"
                            v-if="row.row.outbound_status == 0"
                            @click="breakOutOrder(row.row)"
                            >暂存</el-button
                        >
                        <el-button
                            type="success"
                            size="mini"
                            v-if="row.row.outbound_status != 0"
                            @click="regainOutOrder(row.row)"
                            >恢复出库</el-button
                        >
                        <el-button
                            v-if="
                                row.row.is_cancel_order === 0 &&
                                row.row.order_type === 2
                            "
                            size="mini"
                            style="
                                background-color: #00acac;
                                color: #fff;
                                border-color: #00acac;
                            "
                            @click="repealOrder(row.row)"
                            >拆单</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div>
                <el-dialog
                    center
                    title="商品信息"
                    :visible.sync="viewGoodsTableStatus"
                    width="80%"
                >
                    <el-table
                        border
                        :data="viewGoodsTableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            align="center"
                            width="130"
                            label="商品条码"
                            prop="bar_code"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            label="商品简码"
                            width="130"
                            prop="short_code"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="goods_name"
                            label="商品中文名"
                            width="400"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="en_goods_name"
                            label="商品英文名"
                            min-width="400"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="goods_years"
                            label="年份"
                            width="100"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            label="重量(kg)"
                            prop="weight"
                            width="120"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="goods_id"
                            label="商品期数"
                            width="100"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="nums"
                            label="订单商品数量"
                            width="130"
                        >
                        </el-table-column>
                    </el-table>
                </el-dialog>
            </div>

            <el-dialog
                center
                title="请选择需要导入的订单类型"
                :visible.sync="openImportStatus"
                width="40%"
            >
                <el-radio v-model="importValue" label="1"
                    >萌牙订单
                    <el-link
                        href="https://images.vinehoo.com/wms/order/发货单模版.xlsx"
                        target="_blank"
                        type="warning"
                        >（下载模版<i
                            class="el-icon-download el-icon--right"
                        ></i
                        >）
                    </el-link>
                </el-radio>
                <el-radio v-model="importValue" label="2">T+销货单</el-radio>
                <el-radio v-model="importValue" label="3">猫超订单</el-radio>
                <el-radio v-model="importValue" label="4">视频号订单</el-radio>
                <el-radio v-model="importValue" label="5">聚水潭订单</el-radio>
                <div style="margin-top: 20px">
                    <el-radio v-model="importOrderType" label="0"
                        >用户订单</el-radio
                    >
                    <el-radio v-model="importOrderType" label="1"
                        >调拨订单</el-radio
                    >
                </div>
                <div class="el-upload__tip">只能上传xlsx文件，且不超过10MB</div>
                <span slot="footer" class="dialog-footer">
                    <div>
                        <el-button @click="openImportStatus = false"
                            >取 消</el-button
                        >
                    </div>
                    <el-upload
                        class="upload-demo"
                        action="/admin/outbound/upload/annex"
                        :multiple="false"
                        :show-file-list="false"
                        :data="uploadData"
                        :headers="headers"
                        :limit="999"
                        :on-success="onSuccessAnnex"
                        :before-upload="beforeAvatarUploadAnnex"
                    >
                        <!-- :on-success="onSuccess" -->

                        <el-button type="primary">确 定</el-button>
                    </el-upload>
                </span>
            </el-dialog>
            <div class="block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog
            center
            title="备注"
            :visible.sync="cancelOrderDialogStatus"
            width="30%"
        >
            <el-input
                type="textarea"
                placeholder="请输入备注"
                v-model="remark"
                :autosize="{ minRows: 6, maxRows: 7 }"
                maxlength="99"
                show-word-limit
            >
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelOrderDialogStatus = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="cancelOrderSubmit"
                    >确 定</el-button
                >
            </span>
        </el-dialog>

        <el-dialog
            center
            @close="getOutboundList"
            title="发货单信息"
            :close-on-click-modal="false"
            :visible.sync="editOrderDialogStatus"
            width="70%"
        >
            <edit
                @editClose="editClose"
                :data="editRowData"
                v-if="editOrderDialogStatus"
            ></edit>
        </el-dialog>
        <el-dialog
            center
            title="创建发货单"
            :close-on-click-modal="false"
            :visible.sync="addOrderDialogStatus"
            width="70%"
        >
            <add @close="close" v-if="addOrderDialogStatus"></add>
        </el-dialog>
        <el-dialog
            center
            title="备注列表"
            :visible.sync="remarkDialogStatus"
            width="50%"
        >
            <el-table :data="remarkList" border style="width: 100%">
                <el-table-column
                    prop="description"
                    label="备注内容"
                    min-width="200"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="operation_reason"
                    label="操作原因"
                    min-width="300"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="operator"
                    label="操作人"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    label="操作时间"
                    width="170"
                    align="center"
                >
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog
            center
            title="查看指令商品"
            :visible.sync="viewInstructDialogStatus"
            width="40%"
        >
            <el-dialog
                width="360px"
                title="新增指令商品"
                :visible.sync="addInstructDialogStatus"
                append-to-body
                center
            >
                <div style="margin-left: 20px;">
                    <div  style="margin-bottom: 10px; width: 300px">
                        <!-- <el-input
                            v-model="addInstructData.bar_code"
                            placeholder="指令条码"
                        ></el-input> -->
                        <el-select
                    v-model="addInstructData.bar_code"
                    placeholder="指令条码"
                    clearable
                    filterable
                    remote
                    :remote-method="getGoodsList"
                   @change="barCodeChange"
                >
                    <el-option
                        v-for="item in goodsList"
                        :key="item.id"
                        :label="item.bar_code"
                        :value="item.bar_code"
                    />
                </el-select>
                <div style="margin-top: 10px;">简码：{{ short_code }}</div>
                <div  style="margin-top: 10px;">商品名：{{ goodName }}</div>
                    </div>

                    <div>
                        <el-input-number
                            v-model="addInstructData.nums"
                            :precision="0"
                            :step="1"
                            :min="1"
                        ></el-input-number>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="addInstructDialogStatus = false"
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="addInstructSubmit"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
            <el-button type="success" size="mini" @click="addInstruct"
                >新增指令商品</el-button
            >
            <el-table :data="instructList" style="width: 100%">
                <el-table-column
                    prop="goods_name"
                    align="center"
                    label="指令商品"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    prop="bar_code"
                    label="条码"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column prop="short_code" label="简码" align="center">
                </el-table-column>
                <el-table-column prop="nums" label="数量" align="center">
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            type="danger"
                            @click="deleteInstruct(row.row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog
            center
            title="拆单"
            :visible.sync="repealOrderDialogStatus"
            width="450px"
        >
            <el-input
                v-model="repealOrderData.oper_desc"
                placeholder="拆单原因"
                type="textarea"
                show-word-limit
            ></el-input>
            <div style="margin-top: 10px">
                <el-radio v-model="repealOrderData.is_merge" :label="1"
                    >支持再次合并</el-radio
                >
                <el-radio v-model="repealOrderData.is_merge" :label="0"
                    >不支持再次合并</el-radio
                >
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="repealOrderDialogStatus = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="repealOrderRequest"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import fileDownload from "js-file-download";
import add from "./add.vue";
import edit from "./edit.vue";
import { Loading } from "element-ui";
import Cookies from "js-cookie";
import CompanySelectGroup from "@/components/CompanySelectGroup";
// import CompanyTableColumn from "@/components/CompanyTableColumn";
export default {
    components: {
        add,
        edit,
        CompanySelectGroup,
        // CompanyTableColumn,
    },
    filters: {
        outbound_statusFormat(val) {
            switch (val) {
                case 0:
                    return "正常";
                case 1:
                    return "暂存";
                case 2:
                    return "滞留";
                case 3:
                    return "冻结";
                default:
                    return "未知";
            }
        },

        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未捡货";
                case 1:
                    return "捡货中";
                case 2:
                    return "已捡货";
                case 3:
                    return "复核中";
                case 4:
                    return "已复核";
                case 5:
                    return "已出库";
                default:
                    return "";
            }
        },
        typeFormat(val) {
            switch (val) {
                case 0:
                    return "用户订单";
                case 1:
                    return "调拨订单";
                case 2:
                    return "合并订单";
                default:
                    return "";
            }
        },
        cancalFormat(val) {
            switch (val) {
                case 0:
                    return "正常";
                case 1:
                    return "撤单成功";
                case 3:
                    return "撤单中";
                case 4:
                    return "撤单失败";
                case 5:
                    return "已合并";
                default:
                    return "未知";
            }
        },
        isFormat(val) {
            if (val) {
                return "是";
            } else {
                return "否";
            }
        },
    },

    data() {
        return {
            repealOrderData: {
                order_id: "",
                oper_desc: "",
                is_merge: 1,
            },
            repealOrderDialogStatus: false,
            remarkDialogStatus: false,
            outboundOrderOptions: [
                {
                    label: "正常",
                    value: 0,
                },
                {
                    label: "暂存",
                    value: 1,
                },
                {
                    label: "滞留",
                    value: 2,
                },
                {
                    label: "冻结",
                    value: 3,
                },
            ],
            isCancelOrderOptions: [
                {
                    label: "正常",
                    value: 0,
                },
                {
                    label: "撤单成功",
                    value: 1,
                },
                {
                    label: "撤单中",
                    value: 3,
                },
                {
                    label: "撤单失败",
                    value: 4,
                },
                {
                    label: "已合并",
                    value: 5,
                },
            ],
            editRowData: {},
            editOrderDialogStatus: false,
            viewInstructDialogStatus: false,
            instructList: [],
            addOrderDialogStatus: false,
            importOrderType: "1",
            finishTimes: [],
            addInstructDialogStatus: false,
            viewGoodsTableStatus: false,
            viewGoodsTableData: [],
            logisticsOptions: [],
            uploadData: {
                // eslint-disable-next-line camelcase
                file_key: "file",
            },
            headers: {
                warehousecheckval: Cookies.get("stock_id"),
                securitycheckval: Cookies.get("token"),
            },
            importValue: "1",
            options: [],
            statusOptions: [
                {
                    value: "0",
                    label: "未捡货",
                },
                {
                    value: "1",
                    label: "捡货中",
                },
                {
                    value: "2",
                    label: "已捡货",
                },
                {
                    value: "3",
                    label: "复核中",
                },
                {
                    value: "4",
                    label: "已复核",
                },
                {
                    value: "5",
                    label: "已出库",
                },
            ],
            times: [],
            fictitiousList: [],
            cancelOrderDialogStatus: false,
            openImportStatus: false,
            form: {
                corp: "",
                ordersn: "", // 订单号
                storage: "",
                complete_stime: "",
                complete_etime: "",
                orderno: "", //商家订单号
                fictitious_id: [], // Initialize as empty array
                receiver_name: "", //联系人名称
                receiver_phone: "", //联系人电话
                outbound_status: "",
                logistics_no: "", // 快递单号
                platform: [], // Initialize as empty array
                logistics_id: [], // Initialize as empty array
                is_expedited: "", //是否加急
                is_topay: "", // 是否到付
                status: "", //状态（0未捡货，1捡货中，2已捡货，3复核中，4已复核）
                is_cancel_order: "", //是否撤单（0否 1是）
                order_type: "", // 订单类型（0用户订单，1调拨订单）
                stime: "",
                etime: "",
                goods_id: "", //商品期数
                bar_code: "",
                goods_name: "",
                province: "",
                city: "",
                town: "",
                short_code: "",
                quality_type: "",
                platform_type: ""
            },
            page: 1,
            order_id: "",
            remarkList: [],
            limit: 10,
            addInstructData: {
                bar_code: "",
                nums: 1,
            },
            short_code:'',
            goodName:"",
            goodsList:[],
            total: 0,
            remark: "",
            tableData: [],
            formLabelWidth: "120px",
            platformTypeList: [],
            multipleSelection: [],
            quality_typeList: [],
            fixedOperation: Cookies.get('fixed_operation') === 'true',
        };
    },
    destroyed() {
        document.onkeyup = null;
    },
    mounted() {
        this.getExpressList();
        this.getPlatformList();
        this.enterKeyup();
        this.getOutboundList();
        this.getQuality_typeList();
        this.getFictitiousList();
        this.getPlatformType();
    },
    methods: {
        handleFixedOperationChange(val) {
            Cookies.set('fixed_operation', val);
        },
        async repealOrder(row) {
            this.repealOrderDialogStatus = true;
            this.repealOrderData.order_id = row.id;
        },
        async repealOrderRequest() {
            const res = await this.$request.order.SplitConsolidatedOrder(
                this.repealOrderData
            );
            if (res.data.errorCode == 0) {
                this.repealOrderData = {
                    order_id: "",
                    oper_desc: "",
                    is_merge: 1,
                };
                this.$message.success("拆单成功");
                this.getOutboundList();
            }
        },
        // quality_typeFormat(val) {
        //     console.log(val);
        //     this.quality_typeList.map((item) => {
        //         console.log(item);
        //         if (item.id == val) {
        //             return item.name;
        //         }
        //     });
        // },
        getPlatformType() {
            this.$request.order.getPlatformType().then((res) => {
                this.platformTypeList = res.data.data.list;
            });
        },
        getQuality_typeList() {
            this.$request.order.getQuality_typeList().then((res) => {
                if (res.data.errorCode == 0) {
                    this.quality_typeList = res.data.data.list;
                }
            });
        },
        async getChecklist(ordersn) {
            const loading = this.$loading({
                lock: true,
                text: "正在准备打印中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });

            console.log(loading);
            let data = {
                ordersn,
            };
            const res = await this.$request.order.getChecklist(data);
            if (res.data.errorCode == 0) {
                const expressData = {
                    data: res.data.data.data,
                    express_type: res.data.data.company,
                };
                this.$request.order
                    .print(expressData)
                    .then((res) => {
                        loading.close();
                        if (res.data.errorCode == 0) {
                            this.$message.success("打印清单成功");
                        } else {
                            this.$message({
                                dangerouslyUseHTMLString: true,
                                type: "error",
                                duration: 5000,
                                message:
                                    '<span style="font-size:36px">打印清单失败，请重新打印</span>',
                            });
                        }
                    })
                    .catch(() => {
                        loading.close();
                        this.$message({
                            dangerouslyUseHTMLString: true,
                            type: "error",
                            duration: 5000,
                            message:
                                '<span style="font-size:36px">打印清单失败，请重新打印</span>',
                        });
                    });
            } else {
                loading.close();
                this.$message({
                    dangerouslyUseHTMLString: true,
                    type: "error",
                    duration: 5000,
                    message:
                        '<span style="font-size:36px">打印清单失败，获取订单信息失败</span>',
                });
            }
        },
        getFictitiousList() {
            let data = {
                page: 1,
                limit: 5000,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.fictitiousList = res.data.data.list;
                }
            });
        },
        breakOutOrder(row) {
            this.$prompt("请输入操作原因", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPattern: /^.+$/,
                inputErrorMessage: "请输入操作原因",
            })
                .then(({ value }) => {
                    let data = {
                        order_id: [row.id],
                        operation_reason: value,
                    };
                    this.$request.order.breakOutOrder(data).then((res) => {
                        if (res.data.errorCode == 0) {
                            this.$message.success("操作成功");
                            this.getOutboundList();
                        }
                    });
                })
                .catch(() => {});
        },
        regainOutOrder(row) {
            console.log(row.id);
            let data = {
                order_id: row.id,
            };
            this.$request.order.regainOutOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.$message.success("操作成功");
                    this.getOutboundList();
                }
            });
        },
        openRemark(row) {
            console.log(row);
            this.remarkDialogStatus = true;
            const data = {
                orderid: row.id,
            };
            this.remarkList = [];
            this.$request.order.getOrderRemark(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.remarkList = res.data.data.list;
                }
            });
        },
        addInstructSubmit() {
            let data = {
                order_id: this.order_id,
                ...this.addInstructData,
            };
            this.$request.order.addInstruct(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.addInstructDialogStatus = false;
                    this.getInstructList();
                }
            });
        },
        deleteInstruct(row) {
            console.log(this.order_id, row);
            let data = {
                order_id: this.order_id,
                bar_code: row.bar_code,
            };
            this.$request.order.deleteInstruct(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.$message.success("删除成功");

                    this.getInstructList();
                }
            });
        },
        editOrder(row) {
            this.editOrderDialogStatus = true;
            console.log(row);
            this.editRowData = row;
        },
        editClose() {
            this.editOrderDialogStatus = false;
            this.getOutboundList();
        },
        close() {
            this.addOrderDialogStatus = false;
            this.search();
        },
        enterKeyup() {
            // 监听回车
            document.onkeyup = (e) => {
                // 兼容FF和IE和Opera
                var event = e || window.event;
                var key = event.which || event.keyCode || event.charCode;
                console.log(key);
                if (key == 13) {
                    this.search();
                }
            };
        },
        getExpressList() {
            this.$request.order.getExpressList().then((res) => {
                if (res.data.errorCode == 0) {
                    this.logisticsOptions = res.data.data.list;
                }
            });
        },
        beforeAvatarUploadAnnex(file) {
            const type = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log(type);
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M || type !== "xlsx") {
                if (!isLt10M) {
                    this.$message.error("上传文件大小不能超过 10MB!");
                } else {
                    this.$message.error("上传文件类型错误 请重新上传!");
                }
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading",
                });
            }
            return isLt10M && type === "xlsx";
        },
        viewInstruct(row) {
            let data = {
                order_id: row.id,
            };
            console.log(data);
            this.$request.order.getInstrList(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.order_id = row.id;
                    this.viewInstructDialogStatus = true;
                    console.log(res.data.data);
                    this.instructList = res.data.data.list;
                }
            });
        },
        getInstructList() {
            let data = {
                order_id: this.order_id,
            };
            this.$request.order.getInstrList(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res.data.data);
                    this.instructList = res.data.data.list;
                }
            });
        },
        addInstruct() {
            this.addInstructDialogStatus = true;
            this.addInstructData = {
                bar_code: "",
                nums: 1,
            };
            this.goodName = '';
            this.short_code = '';
        },
        onSuccessAnnex(res, file) {
            if (res.errorCode == 0) {
                console.log(file.response.data);
                this.importOrder(file.response.data);
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        importOrder(url) {
            let data = {
                url,
                file_type: this.importValue,
                order_type: Number(this.importOrderType),
            };
            this.$request.order.importOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res.data.data);
                    this.openImportStatus = false;
                }
                this.getOutboundList();
            });
            console.log(url, this.importValue);
        },
        add() {
            this.$message.warning("该功能已禁用，请在中台制单");
            // this.addOrderDialogStatus = true;
        },
        exportTable() {
            let random_str = new Date();
            let data = {
                column: this.column,
                ...this.form,
                random_str,
            };
            this.$request.order.exportTableAction(data).then((res) => {
                if (res.data.size < 1024) {
                    this.$message.error("没有权限");
                    console.log("false");
                } else {
                    this.$message.success("导出成功");
                    fileDownload(res.data, "发货单.xlsx");
                }
            });
        },
        cancelOrderSubmit() {
            if (!this.remark) {
                this.$message.error("请填写备注信息");
                return;
            }
            this.$confirm("此操作将永久取消选择的发货订单, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                if (this.multipleSelection.length === 0) {
                    this.$message.error("请先选择需要取消的发货订单");
                    return;
                }
                let order_id = [];
                this.multipleSelection.map((i) => {
                    order_id.push(i.id);
                });
                let data = {
                    order_id,
                    cancel_desc: this.remark,
                };
                this.$request.order.cancelOrder(data).then((res) => {
                    console.log(res);
                    if (res.data.errorCode == 0) {
                        this.$message.success("操作完成");
                        this.remark = "";
                        this.cancelOrderDialogStatus = false;
                        this.getOutboundList();
                    }
                });
            });
        },
        cancelOrder() {
            this.cancelOrderDialogStatus = true;
        },

        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection);
        },
        reset() {
            this.times = [];
            this.finishTimes = [];
            this.form = {
                corp: "",
                ordersn: "", // 订单号
                storage: "",
                complete_stime: "",
                complete_etime: "",
                orderno: "", //商家订单号
                fictitious_id: [], // 初始化 as empty array
                receiver_name: "", //联系人名称
                receiver_phone: "", //联系人电话
                outbound_status: "",
                logistics_no: "", // 快递单号
                platform: [], // Initialize as empty array
                logistics_id: [], // Initialize as empty array
                is_expedited: "", //是否加急
                is_topay: "", // 是否到付
                status: "", //状态（0未捡货，1捡货中，2已捡货，3复核中，4已复核）
                is_cancel_order: "", //是否撤单（0否 1是）
                order_type: "", // 订单类型（0用户订单，1调拨订单）
                stime: "",
                etime: "",
                goods_id: "", //商品期数
                bar_code: "",
                goods_name: "",
                province: "",
                city: "",
                town: "",
                short_code: "",
                quality_type: "",
                platform_type: ""
            };
        },
        viewGoods(row) {
            console.log(row);
            this.viewGoodsTableStatus = true;
            this.viewGoodsTableData = row.goods;
        },
        getPlatformList() {
            this.$request.order.getPlatformList().then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.options = res.data.data.list;
                }
            });
        },
        search() {
            this.page = 1;
            this.getOutboundList();
        },
        platformFormat(val) {
            let result = this.options.find((item) => val == item.id);
            if (result.name) {
                return result.name;
            } else {
                return "未知平台";
            }
        },
        getOutboundList() {
            let data = {
                page: this.page,
                limit: this.limit,
                column: this.column,
                ...this.form,
                logistics_id: this.form.logistics_id.join(","),
            };

            data.fictitious_id = this.form.fictitious_id.join(",");
            data.platform = this.form.platform.join(",");
            this.$request.order.getOutboundList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        timesChange(val) {
            console.log(val);
            if (val) {
                this.form.stime = val[0];
                this.form.etime = val[1];
            } else {
                this.form.stime = "";
                this.form.etime = "";
            }
        },
        finishTimesChange(val) {
            console.log(val);
            if (val) {
                this.form.complete_stime = val[0];
                this.form.complete_etime = val[1];
            } else {
                this.form.complete_etime = "";
                this.form.complete_stime = "";
            }
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
            this.page = 1;

            this.limit = val;
            this.getOutboundList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getOutboundList();
        },
        getGoodsList(val) {
            let data = {
                page:1,
                limit: 20,
                is_stock: 0,
                nums:this.addInstructData.nums,
                bar_code:val,
                is_instruction:1
            };
            
            this.$request.goods.getGoodsList(data).then((res) => {
                console.log(res.data);
                if (res.data.errorCode == 0) {
                   
                    this.goodsList = res.data.data.list;
                }
            });
        },
        barCodeChange(val) {
          console.log(val);
          this.goodsList.find((i) => {
            if (i.bar_code == val) {
              this.goodName = i.goods_name;
              this.short_code = i.short_code;
            }
          });
        },
    },
};
</script>
<style lang="scss" scoped>
.area-layout {
    .dialog-footer {
        display: flex;
        justify-content: center;
        .el-button {
            margin: 0 10px;
        }
    }
    /deep/ .el-collapse-item__header,
    /deep/ .el-collapse-item__wrap,
    .el-collapse {
        border: 0 !important;
    }
    /deep/ .el-collapse-item__content {
        padding-bottom: 0 !important;
    }
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            display: flex;
            .btn {
                cursor: auto;
                margin-top: 10px;
                justify-content: space-between;
                padding: 0;
                display: flex;
                align-items: center;
            }
            /deep/ .el-input {
                margin-top: 6px;
                margin-right: 12px;
                width: 230px !important;
            }
        }
    }
}
.f-c {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}
</style>
