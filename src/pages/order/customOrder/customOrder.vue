<template>
    <div class="area-layout">
        <div class="form">
            <div style="margin-top: 10px"></div>
            <div class="actions-from">
                <el-button
                    style="
                        background-color: #00acac;
                        color: #fff;
                        border-color: #00acac;
                    "
                    @click="openImportStatus = true"
                    >客户订单导入</el-button
                >
            </div>
        </div>
        <el-dialog
            center
            title="请上传需要导入的客户订单"
            :visible.sync="openImportStatus"
            width="40%"
        >
            <el-link
                href="https://images.vinehoo.com/wms/order/发货单模版.xlsx"
                target="_blank"
                type="warning"
                >下载模版<i class="el-icon-download el-icon--right"></i>
            </el-link>
            <div class="el-upload__tip">只能上传xlsx文件，且不超过10MB</div>

            <span slot="footer" class="dialog-footer">
                <div>
                    <el-button @click="openImportStatus = false"
                        >取 消</el-button
                    >
                </div>
                <el-upload
                    class="upload-demo"
                    action="/admin/outbound/upload/annex"
                    :multiple="false"
                    :show-file-list="false"
                    :data="uploadData"
                    :headers="headers"
                    :limit="999"
                    :on-success="onSuccessAnnex"
                    :before-upload="beforeAvatarUploadAnnex"
                >
                    <!-- :on-success="onSuccess" -->

                    <el-button type="primary">上传</el-button>
                </el-upload>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { Loading } from "element-ui";
import Cookies from "js-cookie";
export default {
    components: {},
    data() {
        return {
            openImportStatus: false,
            uploadData: {
                // eslint-disable-next-line camelcase
                file_key: "file",
            },
            headers: {
                warehousecheckval: Cookies.get("stock_id"),
                securitycheckval: Cookies.get("token"),
            },
            importOrderType: "0",
            importValue: "1",
        };
    },
    mounted() {},
    methods: {
        closeGoodsView() {
            this.viewGoodsDialogStatus = false;
        },

        beforeAvatarUploadAnnex(file) {
            const type = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log(type);
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M || type !== "xlsx") {
                if (!isLt10M) {
                    this.$message.error("上传文件大小不能超过 10MB!");
                } else {
                    this.$message.error("上传文件类型错误 请重新上传!");
                }
            } else {
                Loading.service({
                    fullscreen: true,
                    background: "rgba(0, 0, 0, 0.7)",
                    lock: true,
                    text: "正在上传",
                    spinner: "el-icon-loading",
                });
            }
            return isLt10M && type === "xlsx";
        },
        onSuccessAnnex(res, file) {
            console.log(13333);
            if (res.errorCode == 0) {
                console.log(file.response.data);
                this.importOrder(file.response.data);
            } else {
                this.$message.error(res.msg);
            }
            Loading.service({ fullscreen: true }).close();
        },
        importOrder(url) {
            let data = {
                url,
                file_type: this.importValue,
                order_type: Number(this.importOrderType),
            };
            console.log(data);
            this.$request.order.importOrder(data).then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res.data.data);
                    this.$message.success("导入成功");
                    this.openImportStatus = false;
                }
            });
            console.log(url, this.importValue);
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.area-layout {
    .dialog-footer {
        display: flex;
        justify-content: center;
        .el-button {
            margin: 0 10px;
        }
    }
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            & > div {
                display: inline-block;
                margin-bottom: 10px;
                margin-right: 10px;
            }
            /deep/ .el-input {
                width: 230px !important;
            }
        }
    }
}
</style>
