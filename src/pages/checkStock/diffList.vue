<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-select
                    v-model="query.area_id"
                    placeholder="区域"
                    clearable
                    @change="query.location_id = ''"
                >
                    <el-option
                        v-for="item in areaLocationList"
                        :key="item.area_id"
                        :label="item.area_name"
                        :value="item.area_id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.location_id"
                    placeholder="库位"
                    clearable
                >
                    <el-option
                        v-for="item in locationList"
                        :key="item.location_id"
                        :label="item.location_code"
                        :value="item.location_id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.status"
                    clearable
                    placeholder="处理状态"
                >
                    <el-option
                        v-for="item in statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
                <el-button type="warning" @click="onExport">导出</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="list" border>
            <el-table-column
                align="center"
                prop="check_no"
                label="盘点计划单号"
            >
            </el-table-column>
            <el-table-column align="center" prop="task_no" label="任务单号">
            </el-table-column>
            <el-table-column
                align="center"
                prop="area_location"
                label="区域库位"
            >
            </el-table-column>
            <el-table-column align="center" prop="$statusText" label="处理状态">
            </el-table-column>
            <el-table-column align="center" prop="short_code" label="简码">
            </el-table-column>
            <el-table-column
                align="center"
                prop="goods_name"
                label="中文名"
                min-width="150"
            >
            </el-table-column>
            <el-table-column align="center" prop="capacity" label="规格">
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点数量">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkNumsColor }">
                        {{ scope.row.check_nums }}
                    </div>
                </template>
            </el-table-column>

            <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.status === 1"
                        type="text"
                        @click="
                            () => {
                                currRow = scope.row;
                                diffCheckDialogVisible = true;
                            }
                        "
                        >审核</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <DiffCheckDialog
            :visible.sync="diffCheckDialogVisible"
            :row="currRow"
            @load="load"
        ></DiffCheckDialog>
    </div>
</template>

<script>
import fileDownload from "js-file-download";
import * as checkStockApi from "@/services/checkStock";
import DiffCheckDialog from "@/components/checkStock/DiffCheckDialog";
export default {
    components: {
        DiffCheckDialog,
    },
    data: () => ({
        areaLocationList: [],
        statusOptions: [
            {
                label: "未处理",
                value: 1,
            },
            {
                label: "盘盈",
                value: 2,
            },
            {
                label: "盘亏",
                value: 3,
            },
        ],
        query: {
            page: 1,
            limit: 10,
            area_id: "",
            location_id: "",
            short_code: "",
            status: 1,
        },
        list: [],
        total: 0,
        diffCheckDialogVisible: false,
        currRow: null,
    }),
    computed: {
        locationList({ areaLocationList, query }) {
            return (
                areaLocationList.find(
                    ({ area_id }) => query.area_id === area_id
                )?.location || []
            );
        },
    },
    created() {
        this.initAreaLocationList();
        this.load();
    },
    methods: {
        initAreaLocationList() {
            checkStockApi.getAreaLocationList().then((res) => {
                if (res.data.errorCode == 0) {
                    const list = res?.data?.data?.list || [];
                    this.areaLocationList = list;
                }
            });
        },
        load() {
            checkStockApi.getDiffGoodsList(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list, total } = res.data.data;
                    list.forEach((item) => {
                        item.$statusText =
                            this.statusOptions.find(
                                ({ value }) => value === item.status
                            )?.label || "";
                        item.$checkNumsColor = "";
                        if (item.check_nums > item.inventory_nums) {
                            item.$checkNumsColor = "rgb(129, 179, 55)";
                        } else if (item.check_nums < item.inventory_nums) {
                            item.$checkNumsColor = "rgb(212, 9, 9)";
                        }
                    });
                    this.list = list;
                    this.total = total;
                }
            });
        },
        onExport() {
            checkStockApi.exportDiffGoodsList(this.query).then((res) => {
                this.$message.success("导出成功");
                fileDownload(res.data, "差异.xlsx");
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>
