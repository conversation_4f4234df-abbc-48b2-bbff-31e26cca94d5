<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.check_no"
                    placeholder="盘点计划单号"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.check_status"
                    clearable
                    placeholder="盘存状态"
                >
                    <el-option
                        v-for="item in checkStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
                <el-button
                    type="warning"
                    @click="planCreateDialogVisible = true"
                    >创建盘点计划</el-button
                >
            </el-form-item>
        </el-form>

        <el-table :data="list" border>
            <el-table-column
                align="center"
                prop="check_no"
                label="盘点计划单号"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点模式">
                <template slot-scope="scope">
                    {{ scope.row.check_mode ? "盲盘" : "明盘" }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="盘点状态">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkStatusTextColor }">
                        {{ scope.row.$checkStatusText }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="expected_sku_count"
                label="预计SKU"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" prop="check_nums" label="差异数量">
            </el-table-column>
            <el-table-column align="center" label="区域库位">
                <template slot-scope="scope">
                    <div
                        style="white-space: pre"
                        v-html="scope.row.area_location.join('\n')"
                    ></div>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="created_time"
                label="盘点时间"
                min-width="120"
            >
            </el-table-column>
            <el-table-column align="center" prop="creator" label="盘点人员">
            </el-table-column>
            <el-table-column align="center" label="操作" min-width="150">
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.check_status !== 3"
                        type="text"
                        @click="
                            $router.push(
                                `/checkStockTaskList?check_no=${scope.row.check_no}`
                            )
                        "
                        >查看任务</el-button
                    >
                    <el-button
                        v-if="scope.row.check_status === 2"
                        type="text"
                        @click="
                            () => {
                                currId = scope.row.id;
                                planViewDiffDialogVisible = true;
                            }
                        "
                        >查看差异</el-button
                    >
                    <el-button
                        v-if="scope.row.check_status === 0"
                        type="text"
                        @click="cancleTask(scope.row.id)"
                        >取消任务</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>

        <PlanCreateDialog
            :visible.sync="planCreateDialogVisible"
            @load="load"
        ></PlanCreateDialog>
        <PlanViewDiffDialog
            :visible.sync="planViewDiffDialogVisible"
            :id="currId"
        ></PlanViewDiffDialog>
    </div>
</template>

<script>
import * as checkStockApi from "@/services/checkStock";
import PlanCreateDialog from "@/components/checkStock/PlanCreateDialog";
import PlanViewDiffDialog from "@/components/checkStock/PlanViewDiffDialog";
export default {
    components: {
        PlanCreateDialog,
        PlanViewDiffDialog,
    },
    data: () => ({
        checkStatusOptions: [
            { label: "未盘点", value: 0 },
            { label: "盘点中", value: 1, color: "rgb(22, 132, 252)" },
            { label: "盘点完成", value: 2, color: "rgb(129, 179, 55)" },
            { label: "盘点取消", value: 3, color: "rgb(212, 9, 9)" },
        ],
        query: {
            page: 1,
            limit: 10,
            check_no: "",
            check_status: "",
        },
        list: [],
        total: 0,
        planCreateDialogVisible: false,
        planViewDiffDialogVisible: false,
        currId: 0,
    }),
    computed: {},
    created() {
        this.load();
    },
    methods: {
        load() {
            checkStockApi.getPlanList(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list, total } = res.data.data;
                    list.forEach((item) => {
                        const findItem = this.checkStatusOptions.find(
                            ({ value }) => value === item.check_status
                        ) || { label: "", color: "" };
                        item.$checkStatusText = findItem.label;
                        item.$checkStatusTextColor = findItem.color;
                    });
                    this.list = list;
                    this.total = total;
                }
            });
        },
        cancleTask(checkId) {
            checkStockApi.cancleTaskRequest({check_id:checkId}).then((res) => {
                if (res.data.errorCode == 0) {
                    this.$message.success("操作成功！");
                    this.load();
                }
            });
            
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>
