<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-input
                    v-model="query.check_no"
                    placeholder="盘点计划单号"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="query.status" placeholder="状态" clearable>
                    <el-option
                        v-for="item in statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-input
                    v-model="query.personnel"
                    placeholder="盘点人员"
                    clearable
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="reload">查询</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="list" border>
            <el-table-column
                align="center"
                prop="check_no"
                label="盘点计划单号"
            >
            </el-table-column>
            <el-table-column align="center" prop="task_no" label="任务单号">
            </el-table-column>
            <el-table-column align="center" prop="$statusText" label="任务状态">
            </el-table-column>
            <el-table-column align="center" label="盘点方式">
                <template slot-scope="scope">
                    {{ scope.row.check_mode ? "盲盘" : "明盘" }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="是否复盘">
                <template slot-scope="scope">
                    {{ scope.row.is_replay ? "是" : "否" }}
                </template>
            </el-table-column>
            <el-table-column align="center" prop="recipients" label="执行人">
            </el-table-column>
            <el-table-column align="center" label="盘点时间">
                <template slot-scope="scope">
                    <div>开始：{{ scope.row.created_time }}</div>
                    <div>领取：{{ scope.row.recipien_time }}</div>
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="
                            () => {
                                currId = scope.row.id;
                                taskDetailDialogVisible = true;
                            }
                        "
                        >查看详情</el-button
                    >
                    <!-- <el-button
                        v-if="scope.row.status === 2"
                        type="text"
                        @click="
                            () => {
                                currId = scope.row.id;
                                taskCheckDialogVisible = true;
                            }
                        "
                        >审核</el-button
                    > -->
                    <el-button
                        type="text"
                        @click="
                            () => {
                                currId = scope.row.id;
                                taskCheckDialogVisible = true;
                            }
                        "
                        >审核</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
        <TaskDetailDialog
            :visible.sync="taskDetailDialogVisible"
            :id="currId"
        ></TaskDetailDialog>

        <TaskCheckDialog
            :visible.sync="taskCheckDialogVisible"
            :id="currId"
            @load="load"
        ></TaskCheckDialog>
    </div>
</template>

<script>
import * as checkStockApi from "@/services/checkStock";
import TaskDetailDialog from "@/components/checkStock/TaskDetailDialog";
import TaskCheckDialog from "@/components/checkStock/TaskCheckDialog";
export default {
    components: {
        TaskDetailDialog,
        TaskCheckDialog,
    },
    data: () => ({
        statusOptions: [
            { label: "待领取", value: 0 },
            { label: "已领取", value: 1 },
            { label: "待审核", value: 2 },
            { label: "已完成", value: 3 },
            { label: "已取消", value: 4 },
        ],
        query: {
            page: 1,
            limit: 10,
            check_no: "",
            status: "",
            personnel: "",
        },
        list: [],
        total: 0,
        taskDetailDialogVisible: false,
        taskCheckDialogVisible: false,
        currId: 0,
    }),
    computed: {},
    created() {
        const { check_no = "" } = this.$route.query;
        this.query.check_no = check_no;
        this.load();
    },
    methods: {
        load() {
            checkStockApi.getTaskList(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list, total } = res.data.data;
                    list.forEach((item) => {
                        item.$statusText =
                            this.statusOptions.find(
                                ({ value }) => value === item.status
                            )?.label || "";
                    });
                    this.list = list;
                    this.total = total;
                }
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>
