<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
        >
            <el-form-item label="唯一标识" prop="identification">
                <el-input v-model="ruleForm.identification"></el-input>
            </el-form-item>
            <el-form-item label="路径" prop="path">
                <el-input v-model="ruleForm.path"></el-input>
            </el-form-item>
            <el-form-item label="标题" prop="title">
                <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
            <el-form-item label="是否开启" prop="status">
                <el-switch v-model="ruleForm.status"></el-switch>
            </el-form-item>
            <el-form-item label="是否为目录" prop="status">
                <el-switch
                    v-model="ruleForm.display"
                    @change="displayChange"
                ></el-switch>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input-number
                    v-model="ruleForm.sort"
                    :min="0"
                    :max="99999"
                    label="描述文字"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="上级权限" prop="parent_id">
                <el-tree
                    :highlight-current="true"
                    :data="tree.list"
                    :default-expand-all="true"
                    ref="tree"
                    node-key="id"
                    :expand-on-click-node="false"
                    :props="tree.defaultProps"
                    @node-click="handleNodeClick"
                ></el-tree>
            </el-form-item>

            <el-form-item>
                <el-button
                    v-if="!$route.query.resources"
                    type="primary"
                    @click="submitForm('ruleForm')"
                    >立即创建</el-button
                >
                <el-button v-else type="primary" @click="editForm('ruleForm')"
                    >确认提交</el-button
                >
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
export default {
    data() {
        return {
            tree: {
                defaultProps: {
                    children: "children",
                    label: "title"
                },
                list: []
            },
            ruleForm: {
                identification: "",
                path: "",
                display: true,
                title: "",
                // eslint-disable-next-line camelcase
                parent_id: "",
                status: true,
                sort: 0
            },
            details: {},
            rules: {
                // eslint-disable-next-line camelcase
                parent_id: [
                    {
                        required: true,
                        message: "请选择上级权限",
                        trigger: "blur"
                    }
                ],
                identification: [
                    {
                        required: true,
                        message: "请输入唯一标识",
                        trigger: "blur"
                    }
                ],

                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.isEdit();
        this.getPermissionsTree();
    },
    methods: {
        isEdit() {
            if (this.$route.query.resources) {
                this.details = JSON.parse(this.$route.query.resources);

                console.log(this.details.parent_id);
                this.$nextTick(function() {
                    setTimeout(() => {
                        if (this.details.parent_id == null) {
                            // eslint-disable-next-line camelcase
                            this.details.parent_id = 0;
                        }
                        this.$refs.tree.setCurrentKey(this.details.parent_id);
                        // eslint-disable-next-line camelcase
                        this.ruleForm.parent_id = this.details.parent_id;
                    }, 200);
                });
                // eslint-disable-next-line camelcase
                this.ruleForm.identification = this.details.identification;
                this.ruleForm.path = this.details.path;
                this.ruleForm.title = this.details.title;
                if (this.details.status === 1) {
                    this.ruleForm.status = true;
                } else {
                    this.ruleForm.status = false;
                }
                if (this.details.display === 1) {
                    this.ruleForm.display = true;
                } else {
                    this.ruleForm.display = false;
                }
                this.ruleForm.sort = this.details.sort;
            }
        },

        displayChange(val) {
            if (val) {
                this.rules.path[0].required = true;
            } else {
                this.rules.path[0].required = false;
            }
        },
        handleNodeClick(data) {
            console.log(data.id);
            // eslint-disable-next-line camelcase
            this.ruleForm.parent_id = data.id;
        },
        async getPermissionsTree() {
            let data = {
                type: this.$route.query.type === "admin" ? 0 : 1
            };
            let res = await this.$request.permission.permissionsTree(data);
            console.log(res.data.data);
            this.tree.list = res.data.data;
        },
        editForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        id: this.details.id,
                        status: this.ruleForm.status,
                        display: this.ruleForm.display,
                        identification: this.ruleForm.identification,
                        path: this.ruleForm.path,
                        title: this.ruleForm.title,
                        // eslint-disable-next-line camelcase
                        parent_id: this.ruleForm.parent_id,
                        sort: this.ruleForm.sort,
                        type: this.$route.query.type === "admin" ? 0 : 1
                    };
                    console.log(data);
                    this.$request.permission
                        .updatePermissionDetails(data)
                        .then(res => {
                            console.log(res.data.errorCode);
                            if (res.data.errorCode == 0) {
                                this.$message({
                                    message: "操作成功",
                                    type: "success"
                                });
                                this.$router.push({ path: "/permissionsList" });
                            }
                        });

                    console.log(data);
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        status: this.ruleForm.status,
                        display: this.ruleForm.display,
                        identification: this.ruleForm.identification,
                        path: this.ruleForm.path,
                        title: this.ruleForm.title,
                        // eslint-disable-next-line camelcase
                        parent_id: this.ruleForm.parent_id,
                        sort: this.ruleForm.sort,
                        type: this.$route.query.type === "admin" ? 0 : 1
                    };
                    this.$request.permission
                        .createPermission(data)
                        .then(res => {
                            console.log(res.data.errorCode);
                            if (res.data.errorCode == 0) {
                                this.$message({
                                    message: "操作成功",
                                    type: "success"
                                });
                                this.$router.push({ path: "/permissionsList" });
                            }
                        });

                    console.log(data);
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        }
    }
};
</script>
<style lang="scss" scoped>
.add-permission-layout {
}
.input-200 {
    margin-right: 10px;
    width: 200px;
}
</style>
