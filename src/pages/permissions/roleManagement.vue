<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-input
                    v-model="keyword"
                    @keyup.enter.native="search"
                    clearable
                    placeholder="请输入角色名称"
                ></el-input>
                <el-button
                    style="margin-left: 10px"
                    @click="search"
                    type="warning"
                    >查询</el-button
                >
                <el-button style="margin-left: 10px" @click="add" type="primary"
                    >新增角色</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    prop="name"
                    label="角色名称"
                    width="200"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="warehouseStockNameStr"
                    label="所属仓库"
                    width="300"
                    align="center"
                >
                </el-table-column>
                <el-table-column prop="description" label="描述">
                </el-table-column>
                <el-table-column
                    prop="create_time"
                    label="创建时间"
                    align="center"
                    width="180"
                >
                </el-table-column>
                <el-table-column label="操作" width="190" align="center">
                    <template slot-scope="row" v-if="row.row.id !== 1">
                        <el-button-group>
                            <el-button
                                v-if="!row.row.status"
                                size="mini"
                                @click="changeStatus(row.row, 1)"
                                type="success"
                                >启用</el-button
                            >
                            <el-button
                                v-else
                                size="mini"
                                @click="changeStatus(row.row, 0)"
                                type="info"
                                >禁用</el-button
                            >
                            <el-button
                                size="mini"
                                @click="edit(row.row)"
                                type="primary"
                                >编辑</el-button
                            >
                            <el-button
                                size="mini"
                                @click="deleteRole(row.row)"
                                type="danger"
                                >删除</el-button
                            >
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    background
                >
                </el-pagination>
            </div>
        </div>
        <div class="dialog">
            <el-dialog title="角色信息" :visible.sync="dialogFormVisible">
                <el-form :model="form" ref="form" :rules="rules">
                    <el-form-item
                        label="角色名称"
                        prop="name"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.name"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="所属仓库"
                        :label-width="formLabelWidth"
                        prop="stockIdList"
                    >
                        <el-select v-model="form.stockIdList" multiple>
                            <el-option
                                v-for="item in wareList"
                                :key="item.stock_id"
                                :label="item.stock_name"
                                :value="item.stock_id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="虚拟仓"
                        :label-width="formLabelWidth"
                        prop="fictitious_id"
                    >
                        <el-select
                            v-model="form.fictitious_id"
                            multiple
                            filterable
                        >
                            <el-option
                                v-for="item in currFictitiousList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="公司"
                        :label-width="formLabelWidth"
                        prop="corp"
                    >
                        <el-select v-model="form.corp" multiple filterable>
                            <el-option
                                v-for="item in currCorpList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item
                        label="角色描述"
                        :label-width="formLabelWidth"
                        prop="description"
                    >
                        <el-input
                            v-model="form.description"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="权限"
                        :label-width="formLabelWidth"
                        prop="permissions"
                    >
                        <el-tree
                            ref="tree"
                            :data="permissionsList"
                            show-checkbox
                            default-expand-all
                            node-key="id"
                            :props="defaultProps"
                        >
                        </el-tree>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submitForm" type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            defaultProps: {
                children: "children",
                label: "title",
            },
            form: {
                // eslint-disable-next-line camelcase
                name: "",
                stockIdList: [],
                fictitious_id: [],
                corp: [],
                // eslint-disable-next-line camelcase
                // stock_id: "",
                // eslint-disable-next-line camelcase
                description: "",
                // eslint-disable-next-line camelcase
                permissions: [],
            },
            permissionsList: [],
            wareList: [],
            column: "area_name",
            keyword: "",
            dialogFormVisible: false,
            fictitiousList: [],
            type: "",
            page: 1,
            rules: {
                // eslint-disable-next-line camelcase
                stockIdList: [
                    {
                        required: true,
                        message: "请选择所属仓库",
                        trigger: "change",
                    },
                ],
                // eslint-disable-next-line camelcase
                name: [
                    {
                        required: true,
                        message: "请输入角色名称",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                description: [
                    {
                        required: false,
                        message: "请输入角色描述",
                        trigger: "blur",
                    },
                ],
            },
            limit: 10,
            total: 0,
            tableData: [],
            formLabelWidth: "120px",
            currFictitiousList: [],
            currCorpList: [],
            corpList: [],
        };
    },
    watch: {
        "form.stockIdList"(newVal) {
            // Filter fictitious list based on selected warehouses
            this.currFictitiousList = this.fictitiousList.filter(({ id }) => {
                const [stockId] = id.split("#");
                return newVal.includes(stockId);
            });
            this.form.fictitious_id = this.form.fictitious_id.filter((id) => {
                const [stockId] = id.split("#");
                return newVal.includes(stockId);
            });
            this.currCorpList = this.corpList.filter(({ id }) => {
                const [stockId] = id.split("#");
                return newVal.includes(stockId);
            });
            this.form.corp = this.form.corp.filter((id) => {
                const [stockId] = id.split("#");
                return newVal.includes(stockId);
            });
        },
    },
    mounted() {
        this.getWarehouseList();
        this.getRoleList();
        this.getPermissionsList();
    },
    methods: {
        getWarehouseList() {
            this.$request.permission.getWarehouseList().then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    this.wareList = Object.freeze(res.data.data.list);
                    const params = {
                        stock_id: this.wareList.map(({ stock_id }) => stock_id),
                    };
                    const corpList = [];
                    this.$request.permission
                        .getFictitiousList(params)
                        .then((res) => {
                            if (res.data.errorCode == 0) {
                                const fictitiousList = [];
                                res.data.data.list.forEach((item) => {
                                    const { stock_id } = item;
                                    item.fictitious.forEach(
                                        ({
                                            fictitious_id,
                                            fictitious_name,
                                        }) => {
                                            fictitiousList.push({
                                                id: `${stock_id}#${fictitious_id}`,
                                                name: fictitious_name,
                                            });
                                        }
                                    );

                                    // Process corp data
                                    if (item.corp && item.corp.length) {
                                        item.corp.forEach(({ id, name }) => {
                                            corpList.push({
                                                id: `${stock_id}#${id}`,
                                                name: name,
                                            });
                                        });
                                    }
                                });

                                this.fictitiousList =
                                    Object.freeze(fictitiousList);
                                this.corpList = Object.freeze(corpList);
                                console.log(this.fictitiousList, this.corpList);
                            }
                        });
                }
            });
        },
        getPermissionsList() {
            this.$request.user.getPermissionList().then((res) => {
                if (res.data.errorCode == 0) {
                    console.log(res);
                    let routes = [];
                    let client = [];
                    res.data.data.routes.map((i) => {
                        i.title = "WMS管理系统 - " + i.title;
                        routes.push(i);
                    });
                    res.data.data.client.map((i) => {
                        i.title = "PDA客户端 - " + i.title;
                        client.push(i);
                    });
                    this.permissionsList = routes.concat(client);
                }
            });
        },
        deleteRole(row) {
            this.$confirm("此操作将永久该角色, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let data = {
                        id: row.id,
                    };
                    this.$request.user.deleteRole(data).then((res) => {
                        console.log(res);
                        if (res.data.errorCode == 0) {
                            this.$message.success("操作成功");
                            this.getRoleList();
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        changeStatus(row, status) {
            let data = {
                id: row.id,
                status,
            };
            this.$request.user.updateRoleStatus(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.$message.success("操作成功");
                    this.getRoleList();
                }
            });
        },
        submitForm() {
            this.$refs.form.validate((valid) => {
                console.log(valid);
                if (valid) {
                    this.submit();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        async submit() {
            const { name, description, stockIdList, fictitious_id, corp } =
                this.form;
            const warehouse = stockIdList.map((stock_id) => {
                return {
                    stock_id,
                    fictitious_id: fictitious_id
                        .map((id) => id.split("#"))
                        .filter((arr) => arr[0] === stock_id)
                        .map((arr) => arr[1]),
                    corp: corp
                        .map((id) => id.split("#"))
                        .filter((arr) => arr[0] === stock_id)
                        .map((arr) => arr[1]), // Add corp data to each warehouse
                };
            });
            if (this.type === "edit") {
                let data = {
                    id: this.form.id,
                    name: this.form.name,
                    description: this.form.description,
                    permissions: [],
                    // eslint-disable-next-line camelcase
                    // stock_id: this.form.stock_id,
                    status: this.form.status,
                    warehouse,
                };
                data.permissions = this.$refs.tree
                    .getCheckedKeys()
                    .concat(this.$refs.tree.getHalfCheckedKeys());

                let editRes = await this.$request.user.updateRole(data);
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getRoleList();
                }
            } else {
                let data = {
                    name,
                    description,
                    permissions: [],
                    // eslint-disable-next-line camelcase
                    // stock_id: this.form.stock_id,
                    status: 1,
                    warehouse,
                };

                data.permissions = this.$refs.tree
                    .getCheckedKeys()
                    .concat(this.$refs.tree.getHalfCheckedKeys());

                console.log(data);
                let addRes = await this.$request.user.createRole(data);
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getRoleList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getRoleList();
        },
        resetForm() {
            this.form = {
                // eslint-disable-next-line camelcase
                name: "",
                stockIdList: [],
                fictitious_id: [],
                corp: [],
                // eslint-disable-next-line camelcase
                description: "",
                // eslint-disable-next-line camelcase
                permissions: [],
            };
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        add() {
            this.type = "add";
            this.resetForm();
            this.dialogFormVisible = true;
            this.$nextTick(() => {
                this.$refs.tree.setCheckedKeys([]);
            });
        },
        edit(row) {
            console.log(row);
            this.type = "edit";
            // eslint-disable-next-line camelcase
            // this.form.stock_id = row.stock_id;
            // eslint-disable-next-line camelcase
            this.form.permissions = row.permissions;
            const { warehouse } = row;
            this.form.stockIdList = warehouse.map(({ stock_id }) => stock_id);
            this.form.fictitious_id = warehouse
                .map(({ stock_id, fictitious_id }) =>
                    fictitious_id.map((item) => `${stock_id}#${item}`)
                )
                .reduce((prev, curr) => prev.concat(curr), []);
            this.form.corp = warehouse
                .map(({ stock_id, corp }) =>
                    corp.map((item) => `${stock_id}#${item}`)
                )
                .reduce((prev, curr) => prev.concat(curr), []);
            // eslint-disable-next-line camelcase
            this.form.name = row.name;
            // eslint-disable-next-line camelcase
            this.form.id = row.id;
            this.form.description = row.description;
            this.form.status = row.status;

            this.dialogFormVisible = true;
            console.log(this.permissionsList, this.form.permissions);

            this.$nextTick(() => {
                this.$refs.tree.setCheckedKeys([]); // 清空

                this.form.permissions.map((i) => {
                    let node = this.$refs.tree.getNode(i);
                    console.log(node, i);
                    if (node && node.isLeaf) {
                        //设置某个节点的勾选状态
                        this.$refs.tree.setChecked(node, true);
                    }
                });
            });
        },
        getRoleList() {
            let data = {
                page: this.page,
                limit: this.limit,
                name: this.keyword,
            };
            this.$request.user.getRoleList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    res.data.data.list.forEach((item) => {
                        item.warehouseStockNameStr = item.warehouse
                            .map(({ stock_name }) => stock_name)
                            .join();
                    });
                    console.log(res.data.data.list);
                    this.tableData = res.data.data.list;
                }
            });
        },
        handleSizeChange(val) {
            this.limit = val;
            this.page = 1;
            this.getRoleList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getRoleList();
        },
    },
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
