<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-select v-model="column" placeholder="搜索条件">
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="keyword"
                    clearable
                    @keyup.enter.native="search"
                    placeholder="请输入关键字"
                ></el-input>
                <el-select
                    style="margin-left: 10px"
                    v-model="role_id"
                    clearable
                    filterable
                    placeholder="请选择角色"
                >
                    <el-option
                        label="超级管理员"
                        v-if="userInfo.super"
                        :value="1"
                    >
                    </el-option>
                    <el-option
                        v-for="item in Roleoptions"
                        :key="item.id"
                        :label="item.stock_name + '-' + item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <!-- <el-select
                    style="margin-left: 10px"
                    v-model="status"
                    clearable
                    filterable
                    placeholder="是否禁用"
                >
                    <el-option :value="1" label="可用"></el-option>
                    <el-option :value="0" label="禁用"></el-option>
                </el-select> -->
                <el-button
                    style="margin-left: 10px"
                    @click="search"
                    type="warning"
                    >查询</el-button
                >
                <el-button style="margin-left: 10px" @click="add" type="primary"
                    >新增用户</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    prop="username"
                    label="用户名"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="nickname"
                    label="昵称"
                    width="100"
                    align="center"
                >
                </el-table-column>
                <el-table-column prop="role_name" label="角色名称">
                    <template slot-scope="row">
                        <el-tag
                            style="margin-left: 4px"
                            v-for="(item, index) in roles(row.row.roles)"
                            :key="index"
                        >
                            {{ item.role_name }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="phone"
                    label="手机号"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="last_login_time"
                    label="最近登陆时间"
                    align="center"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="last_login_ip"
                    label="最近登陆IP"
                    width="150"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="create_time"
                    label="创建时间"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="is_informal"
                    label="非正式工"
                    width="100"
                >
                    <template slot-scope="row">
                        <el-tag
                            :type="
                                row.row.is_informal == 1 ||
                                row.row.is_informal === true
                                    ? 'warning'
                                    : 'success'
                            "
                        >
                            {{
                                row.row.is_informal == 1 ||
                                row.row.is_informal === true
                                    ? "是"
                                    : "否"
                            }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="140" align="center">
                    <template slot-scope="row">
                        <el-button-group>
                            <el-button
                                v-if="row.row.status"
                                size="mini"
                                @click="changeStatus(row.row, 0)"
                                type="info"
                                >禁用</el-button
                            >
                            <el-button
                                size="mini"
                                v-else
                                @click="changeStatus(row.row, 1)"
                                type="success"
                                >启用</el-button
                            >
                            <el-button
                                size="mini"
                                @click="edit(row.row)"
                                type="primary"
                                >编辑</el-button
                            >

                            <!-- <el-button
                                size="mini"
                                @click="deleteUser(row.row)"
                                type="danger"
                                >删除</el-button
                            > -->
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <div class="dialog">
            <el-dialog
                title="用户信息"
                :visible.sync="dialogFormVisible"
                width="50%"
            >
                <el-form :model="form" ref="form" :rules="rules">
                    <el-form-item
                        label="用户名"
                        prop="username"
                        :onKeyUp="
                            (form.username = form.username.replace(/[\W]/g, ''))
                        "
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.username"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="密码"
                        prop="password"
                        v-if="type == 'add'"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            show-password
                            v-model="form.password"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="昵称"
                        prop="nickname"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.nickname"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="角色"
                        prop="role_id"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-if="!isSuper"
                            v-model="form.role_id"
                            filterable
                            style="width: 500px"
                            :disabled="isSuper"
                            multiple
                            placeholder="请选择角色"
                        >
                            <el-option
                                v-for="item in Roleoptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                        <el-checkbox
                            v-if="userInfo.super"
                            @change="checkboxChange"
                            v-model="isSuper"
                        >
                            设置超级管理员
                        </el-checkbox>
                    </el-form-item>
                    <el-form-item
                        label="邮箱"
                        :label-width="formLabelWidth"
                        prop="email"
                    >
                        <el-input
                            v-model="form.email"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="手机号"
                        :label-width="formLabelWidth"
                        prop="phone"
                    >
                        <el-input
                            v-model="form.phone"
                            autocomplete="off"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="非正式工"
                        :label-width="formLabelWidth"
                    >
                        <el-checkbox v-model="form.is_informal">
                            非正式工
                        </el-checkbox>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button @click="submitForm" type="primary"
                        >确 定</el-button
                    >
                    <!-- updateStockAreaList -->
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import { mapState } from "vuex";
export default {
    computed: {
        ...mapState(["userInfo"]),
    },
    data() {
        return {
            Roleoptions: [],
            form: {
                username: "",
                password: "",
                nickname: "",
                // eslint-disable-next-line camelcase
                role_id: [],
                email: "",
                phone: "",
                is_informal: false,
            },
            searchOptions: [
                {
                    value: "username",
                    label: "用户名",
                },
                {
                    value: "nickname",
                    label: "昵称",
                },
                {
                    value: "phone",
                    label: "手机号",
                },
            ],
            column: "username",
            keyword: "",
            // status: "",
            role_id: "",
            dialogFormVisible: false,
            type: "",
            page: 1,
            rules: {
                // eslint-disable-next-line camelcase
                role_id: [
                    {
                        required: true,
                        message: "请选择角色",
                        trigger: "blur",
                    },
                ],
                nickname: [
                    {
                        required: true,
                        message: "请输入昵称",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                username: [
                    {
                        required: true,
                        message: "请输入用户名",
                        trigger: "blur",
                    },
                ],
                // eslint-disable-next-line camelcase
                password: [
                    {
                        required: true,
                        message: "请输入密码",
                        trigger: "blur",
                    },
                ],
            },
            limit: 10,
            isSuper: false,
            total: 0,
            tableData: [],
            formLabelWidth: "120px",
        };
    },
    mounted() {
        this.getRoleoptions();
        this.getUserList();
    },
    methods: {
        deleteUser(row) {
            this.$confirm("此操作将永久该用户, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let data = {
                        id: row.id,
                    };
                    this.$request.user.deleteUser(data).then((res) => {
                        console.log(res);
                        if (res.data.errorCode == 0) {
                            this.$message.success("操作成功");
                            this.getUserList();
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        getRoleoptions() {
            this.$request.user.getRoleSelectList().then((res) => {
                if (res.data.errorCode == 0) {
                    console.error(res);
                    this.Roleoptions = res.data.data;
                }
            });
        },
        roles(roles) {
            if (roles && roles.length) {
                return Array.from(new Set(roles));
            } else {
                return [];
            }
        },
        changeStatus(row, status) {
            let data = {
                id: row.id,
                status,
            };
            this.$request.user.updateUserStatus(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.$message.success("操作成功");
                    this.getUserList();
                }
            });
        },
        submitForm() {
            this.$refs.form.validate((valid) => {
                console.log(valid);
                if (valid) {
                    this.submit();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        async submit() {
            if (this.type === "edit") {
                let data = {
                    id: this.form.id,
                    username: this.form.username,
                    nickname: this.form.nickname,
                    status: this.form.status,
                    // eslint-disable-next-line camelcase
                    role_id: this.form.role_id,
                    email: this.form.email,
                    phone: this.form.phone,
                    is_informal: this.form.is_informal ? 1 : 0,
                };
                let editRes = await this.$request.user.updateUserInfo(data);
                if (editRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getUserList();
                }
            } else {
                let data = {
                    username: this.form.username,
                    password: this.form.password,
                    nickname: this.form.nickname,
                    status: 1,
                    // eslint-disable-next-line camelcase
                    role_id: this.form.role_id,
                    email: this.form.email,
                    phone: this.form.phone,
                    is_informal: this.form.is_informal ? 1 : 0,
                };
                let addRes = await this.$request.user.createUser(data);
                if (addRes.data.errorCode == 0) {
                    this.dialogFormVisible = false;
                    this.getUserList();
                }
            }
        },
        search() {
            this.page = 1;
            this.getUserList();
        },
        resetForm() {
            this.form = {
                username: "",
                password: "",
                nickname: "",
                // eslint-disable-next-line camelcase
                role_id: [],
                email: "",
                phone: "",
                is_informal: false,
            };
            this.isSuper = false;
            // this.$refs.formDom.resetFields();
            // this.form.map(i => {
            //     console.log(i);
            // });
        },
        checkboxChange(val) {
            if (val) {
                // eslint-disable-next-line camelcase
                this.form.role_id = [1];
            } else {
                // eslint-disable-next-line camelcase
                this.form.role_id = [];
            }
        },
        add() {
            this.type = "add";
            this.resetForm();
            this.dialogFormVisible = true;
        },
        edit(row) {
            // eslint-disable-next-line camelcase
            this.form.role_id = [];
            console.log(row);

            this.type = "edit";
            // eslint-disable-next-line camelcase
            this.form.id = row.id;
            // eslint-disable-next-line camelcase
            this.form.username = row.username;
            // eslint-disable-next-line camelcase
            this.form.password = row.password;
            this.form.nickname = row.nickname;
            this.form.status = row.status;
            // eslint-disable-next-line camelcase
            row.roles.map((i) => {
                this.form.role_id.push(i.role_id);
            });
            this.form.role_id = [...new Set(this.form.role_id)];
            console.log(this.form.role_id);
            if (this.form.role_id.length == 1 && this.form.role_id[0] == 1) {
                this.isSuper = true;
            } else {
                this.isSuper = false;
            }
            this.form.email = row.email;
            this.form.phone = row.phone;
            this.form.is_informal =
                row.is_informal == 1 || row.is_informal === true;
            this.dialogFormVisible = true;
        },
        getUserList() {
            let data = {
                page: this.page,
                limit: this.limit,
                role_id: this.role_id,
            };
            // if (typeof this.status == "number") {
            //     data.status = this.status;
            // }
            console.log(data);
            switch (this.column) {
                case "username":
                    data.username = this.keyword;
                    break;
                case "nickname":
                    data.nickname = this.keyword;
                    break;
                case "phone":
                    data.phone = this.keyword;
                    break;
                default:
                    break;
            }
            this.$request.user.getUserList(data).then((res) => {
                console.log(res);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        handleSizeChange(val) {
            this.limit = val;
            this.page = 1;
            this.getUserList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getUserList();
        },
    },
};
</script>
<style lang="scss" scoped>
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
