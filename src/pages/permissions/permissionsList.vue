<template>
    <div class="permission-layout">
        <div class="permission-form">
            <el-input
                v-model="identification"
                class="input-200"
                placeholder="唯一标识"
                clearable
            ></el-input>
            <el-input
                v-model="title"
                clearable
                class="input-200"
                placeholder="标题"
            ></el-input>
            <el-button @click="getPermissionList" type="warning">查询</el-button>
            <el-button type="primary" @click="add">添加</el-button>
        </div>
        <div class="permissions-main">
            <el-tabs
                v-model="activeTab"
                type="card"
                @tab-click="getPermissionList"
            >
                <el-tab-pane label="后台系统权限" name="admin"> </el-tab-pane>
                <el-tab-pane label="PDA系统权限" name="pda"></el-tab-pane>
            </el-tabs>
            <el-table
                :data="tableData"
                style="width: 100%;margin-bottom: 20px;"
                row-key="id"
                border
                :tree-props="{
                    children: 'children',
                    hasChildren: 'hasChildren'
                }"
            >
                <el-table-column
                    prop="title"
                    label="标题"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    prop="identification"
                    label="唯一标识"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    prop="path"
                    label="路径"
                    width="180"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    label="导航菜单"
                    width="90"
                >
                    <template slot-scope="row">
                        <el-tag v-if="row.row.display" type="success">
                            {{ row.row.display | displayFormat }}
                        </el-tag>
                        <el-tag v-else type="danger">{{
                            row.row.display | displayFormat
                        }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="80" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status" type="success"
                            >启动</el-tag
                        >
                        <el-tag v-else type="info">禁用</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="240">
                    <template slot-scope="scope">
                        <el-button-group>
                            <el-button
                                type="success"
                                size="mini"
                                icon="el-icon-unlock"
                                v-if="!scope.row.status"
                                @click="changeStatus(scope.row, 1)"
                                >启用</el-button
                            >
                            <el-button
                                type="info"
                                size="mini"
                                @click="changeStatus(scope.row, 0)"
                                icon="el-icon-lock"
                                v-else
                                >禁用</el-button
                            >
                            <el-button
                                size="mini"
                                type="primary"
                                icon="el-icon-edit"
                                @click="goEdit(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                type="danger"
                                size="mini"
                                @click="deletePermission(scope.row)"
                                icon="el-icon-delete"
                                >删除</el-button
                            >
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            activeTab: "admin",
            identification: "",
            title: "",
            tableData: []
        };
    },
    mounted() {
        this.getPermissionList();
    },
    filters: {
        displayFormat(val) {
            if (val) {
                return "显示";
            } else {
                return "隐藏";
            }
        }
    },
    methods: {
        goEdit(row) {
            let details = JSON.stringify(row);
            this.$router.push({
                path:
                    "addPermission?type=" +
                    this.activeTab +
                    "&resources=" +
                    details
            });
        },
        async changeStatus(row, status) {
            let data = {
                id: row.id,
                status
            };
            let res = await this.$request.permission.updatePermissionStatus(
                data
            );
            console.log(res);

            this.getPermissionList();
        },
        async deletePermission(row) {
            this.$confirm("此操作将永久删除该权限, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    console.log(row);
                    let data = { id: row.id };
                    this.$request.permission
                        .deletePermission(data)
                        .then(res => {
                            console.log(res);

                            this.getPermissionList();
                        });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除"
                    });
                });
        },
        add() {
            this.$router.push({
                path: "addPermission?type=" + this.activeTab
            });
        },
        getPermissionList() {
            let type;
            if (this.activeTab === "admin") {
                type = 0;
            } else {
                type = 1;
            }
            let data = {
                type: type,
                identification: this.identification,
                title: this.title
            };
            this.$request.permission.getPermissionList(data).then(res => {
                console.log(res);
                this.tableData = res.data.data.list;
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.input-200 {
    margin-right: 10px;
    width: 200px;
}
.permission-layout {
    .permission-form {
        display: flex;
    }
    .permissions-main {
        margin-top: 20px;
    }
}
</style>
