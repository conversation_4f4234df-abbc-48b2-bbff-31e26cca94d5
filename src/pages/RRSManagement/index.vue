<template>
    <div>
        <div style="margin-bottom: 10px">
            <el-date-picker
                v-model="times"
                type="daterange"
                start-placeholder="业务开始时间"
                end-placeholder="业务结束时间"
                value-format="yyyy-MM-dd"
            ></el-date-picker>
            <el-select
                style="margin-left: 10px; width: 80px"
                v-model="summary_type"
                @change="summaryChange"
                placeholder="请选择类型"
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-if="summary_type === 1"
                v-model="ware_id"
                filterable
                placeholder="请选择仓库"
            >
                <el-option
                    v-for="item in optionsList"
                    :key="item.fictitious_id"
                    :label="item.fictitious_name"
                    :value="item.fictitious_id"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="corp_id"
                filterable
                placeholder="请选择公司"
                v-if="summary_type === 2"
            >
                <el-option
                    v-for="item in companyList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
            <el-input
                    style="margin-left: 10px; width: 160px;"
                    placeholder="请输入简码"
                    v-model="short_code"
                >
                </el-input>
            <el-button type="primary" style="margin-left: 20px" @click="getList"
                >查询</el-button
            >
            <el-button
                type="warning"
                style="margin-left: 10px"
                @click="exportFile"
                >导出</el-button
            >
        </div>
        <el-table :data="list" border style="width: 100%">
            <el-table-column
                prop="fictitious_name"
                label="仓库名称"
                width="240"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="corp_name"
                label="公司"
                width="180"
                align="center"
            >
            </el-table-column>

            <el-table-column
                prop="goods_name"
                label="存货名称"
                width="240"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="short_code"
                align="center"
                label="存货编码"
                width="150"
            >
            </el-table-column
            ><el-table-column
                align="center"
                prop="opening_balance"
                label="期初存结"
                width="120"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="income_nums"
                label="本期收入"
                min-width="120"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="deliver_nums"
                label="本期发出"
                min-width="120"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="closing_balance"
                label="期末结存"
                min-width="120"
            >
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import fileDownload from "js-file-download";
export default {
    data() {
        return {
            summary_type: 1,
            ware_id: "",
            short_code:'',
            options: [
                {
                    value: 1,
                    label: "仓库",
                },
                {
                    value: 2,
                    label: "公司",
                },
            ],
            optionsList: [],
            times: [],
            list: [],
            corp_id: "",
            companyList: [],
        };
    },

    mounted() {
        this.getVirtualList();
        this.getCompanyList();
    },
    methods: {
        summaryChange(val) {
            this.companyList = [];
            this.optionsList = [];
            this.corp_id = "";
            this.ware_id = "";
            if (val === 1) {
                this.getVirtualList();
            } else {
                this.getCompanyList();
            }
        },
        getCompanyList() {
            this.$request.order.getCompanyList().then((res) => {
                if (res.data.errorCode == 0) {
                    this.companyList = res.data.data;
                }
            });
        },
        getVirtualList() {
            let data = {
                page: 1,
                limit: 5000,
            };
            this.$request.stock.getVirtualList(data).then((res) => {
                if (res.data.errorCode == 0) {
                    this.optionsList = res.data.data.list;
                }
            });
        },
        exportFile() {
            if (this.times.length === 2) {
                let data = {
                    start_time: this.times[0],
                    end_time: this.times[1],
                    summary_type: this.summary_type,
                    short_code:this.short_code
                };
                if (this.summary_type === 1) {
                    data.fictitious_id = this.ware_id ? this.ware_id : 0;
                } else {
                    data.corp = this.corp_id ? this.corp_id : 0;
                }
                this.$request.order.ExportSfcData(data).then((res) => {
                    if (res.data.size < 1024) {
                        this.$message.error("没有权限");
                    } else {
                        this.$message.success("导出成功");
                        fileDownload(res.data, "收发存.xlsx");
                    }
                });
            } else {
                this.$message.info("请选择时间");
            }
        },
        async getList() {
            let data = {
                start_time: this.times[0],
                end_time: this.times[1],
                summary_type: this.summary_type,
                short_code:this.short_code
            };
            if (this.summary_type === 1) {
                data.fictitious_id = this.ware_id;
            } else {
                data.corp = this.corp_id;
            }
            if (
                this.times.length &&
                this.summary_type &&
                (this.ware_id || this.corp_id)
            ) {
                const res = await this.$request.order.getSfcData(data);
                if (res.data.error_code == 0) {
                    this.list = res.data.data.list;
                }
            } else {
                this.$message.warning("请选择查询条件");
            }
        },
    },
};
</script>
<style lang="scss" scoped></style>
