<template>
    <div class="area-layout">
        <div class="form">
            <div class="search">
                <el-cascader
                    style="margin-right:10px"
                    :options="options"
                    filterable
                    v-model="cascaderValue"
                    placeholder="分区名称/库位编号"
                    @change="getCheckedNodes"
                    :props="{ checkStrictly: true }"
                    clearable
                ></el-cascader>
                <el-select v-model="column" placeholder="搜索条件">
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="keyword"
                    clearable
                    @keyup.enter.native="search"
                    placeholder="请输入关键字"
                ></el-input>
                <el-button
                    style="margin-left:10px"
                    type="warning"
                    @click="search"
                    >查询</el-button
                >
            </div>
        </div>
        <div class="area-main">
            <el-table :data="tableData" stripe border style="width: 100%">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-form
                            label-position="left"
                            inline
                            class="demo-table-expand"
                        >
                            <el-form-item label="分区名称">
                                <span>{{ props.row.area_name }}</span>
                            </el-form-item>
                            <el-form-item label="分区编号">
                                <span>{{ props.row.area_code }}</span>
                            </el-form-item>

                            <el-form-item label="库位编号">
                                <span>{{ props.row.location_code }}</span>
                            </el-form-item>
                            <el-form-item label="库位预警值">
                                <span>{{ props.row.warning }}</span>
                            </el-form-item>
                            <!-- <el-form-item label="采摘年份">
                                <span>{{ props.row.goods_years }}年</span>
                            </el-form-item>

                            <el-form-item label="保质期">
                                <span>{{ props.row.shelf_life }}天</span>
                            </el-form-item>
                            <el-form-item label="灌装日期">
                                <span>{{ props.row.produce_date }}</span>
                            </el-form-item>
                            <el-form-item label="容量">
                                <span>{{ props.row.capacity }}</span>
                            </el-form-item> -->
                            <!-- <el-form-item label="商品形态">
                                <span>{{
                                    propertyFormat(
                                        "liquid",
                                        props.row.goods_form
                                    )
                                }}</span>
                            </el-form-item> -->
                            <!-- <el-form-item label="是否赠品">
                                <span>{{
                                    propertyFormat(
                                        "give",
                                        props.row.is_giveaway
                                    )
                                }}</span>
                            </el-form-item> -->

                            <el-form-item label="实际量">
                                <span>{{ props.row.actual_count }}</span>
                            </el-form-item>
                            <el-form-item label="可用量">
                                <span>{{ props.row.available_count }}</span>
                            </el-form-item>

                            <el-form-item label="创建时间">
                                <span>{{ props.row.create_time }}</span>
                            </el-form-item>
                        </el-form>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="bar_code"
                    width="160"
                    align="center"
                    label="条码"
                >
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    width="160"
                    label="简码"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="goods_name"
                    label="中文品名"
                    min-width="300"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="en_goods_name"
                    label="英文品名"
                    min-width="280"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <div class="block">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            options: [],
            cascaderValue: [],
            keyword: "",
            searchOptions: [
                {
                    value: "bar_code",
                    label: "商品条码"
                },
                {
                    value: "short_code",
                    label: "商品简码"
                },
                {
                    value: "goods_name",
                    label: "商品中文名称"
                },
                {
                    value: "en_goods_name",
                    label: "商品英文名称"
                }
            ],
            column: "bar_code",
            page: 1,
            limit: 10,
            // eslint-disable-next-line camelcase
            area_id: "",
            // eslint-disable-next-line camelcase
            location_id: "",
            total: 0,
            tableData: [],
            formLabelWidth: "120px"
        };
    },
    mounted() {
        if (this.$route.query.area_id) {
            this.getRouterId();
        } else {
            this.getStorageGoodsPhysical();
        }
        this.getStockLinkage();
    },
    methods: {
        getRouterId() {
            this.cascaderValue = [this.$route.query.area_id];
            // eslint-disable-next-line camelcase
            this.area_id = this.$route.query.area_id;
            if (this.$route.query.location_id) {
                this.cascaderValue = [
                    this.$route.query.area_id,
                    this.$route.query.location_id
                ];
                // eslint-disable-next-line camelcase
                this.location_id = this.$route.query.location_id;
            }

            this.getStorageGoodsPhysical();
            // this.$route.query.area_id;
        },
        getCheckedNodes(val) {
            if (val[0]) {
                // eslint-disable-next-line camelcase
                this.area_id = val[0];
            } else {
                // eslint-disable-next-line camelcase
                this.area_id = "";
            }
            if (val[1]) {
                // eslint-disable-next-line camelcase
                this.location_id = val[1];
            } else {
                // eslint-disable-next-line camelcase
                this.location_id = "";
            }
        },
        getStockLinkage() {
            this.$request.stock.getStockLinkage(1).then(res => {
                this.options = res.data.data;
            });
        },
        search() {
            this.page = 1;
            this.getStorageGoodsPhysical();
        },
        getStorageGoodsPhysical() {
            let data = {
                page: this.page,
                limit: this.limit,
                keyword: this.keyword,
                column: this.column,
                // eslint-disable-next-line camelcase
                area_id: this.area_id,
                // eslint-disable-next-line camelcase
                location_id: this.location_id
            };
            this.$request.goods.getStorageGoodsPhysical(data).then(res => {
                console.log(res.data);
                if (res.data.errorCode == 0) {
                    this.total = res.data.data.totalnum;
                    this.tableData = res.data.data.list;
                }
            });
        },
        propertyFormat(type, val) {
            if (type === "liquid") {
                if (val) {
                    return "固体";
                } else {
                    return "液体";
                }
            } else {
                if (val) {
                    return "是";
                } else {
                    return "否";
                }
            }
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);

            this.limit = val;
            this.page = 1;
            this.getStorageGoodsPhysical();
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getStorageGoodsPhysical();
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .demo-table-expand {
    font-size: 0 !important;
}
/deep/ .demo-table-expand label {
    width: 90px !important;
    color: #99a9bf !important;
}
/deep/ .demo-table-expand .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    width: 50% !important;
}
.product-imgs {
    display: flex;
    & > div {
        text-align: center;
        margin-right: 10px;
    }
}
.area-layout {
    .area-main {
        .block {
            margin-top: 10px;
            display: flex;
            justify-content: center;
        }
    }
    .form {
        margin-bottom: 10px;
        .search {
            /deep/ .el-input {
                width: 180px !important;
            }
        }
    }
}
</style>
