import * as mapperModel from "./mapperModel";

export const MTransferGDTypeText = Object.freeze([
    {
        value: mapperModel.MTransferGDType.GoodToGood,
        text: "良转良",
    },
    {
        value: mapperModel.MTransferGDType.DefectiveToGood,
        text: "次转良",
    },
    {
        value: mapperModel.MTransferGDType.GoodToDefective,
        text: "良转次",
    },
    {
        value: mapperModel.MTransferGDType.DefectiveToDefective,
        text: "次转次",
    },
]);

export const MTransferGDStatusText = Object.freeze([
    {
        value: mapperModel.MTransferGDStatus.PendCheck,
        text: "待审核",
    },
    {
        value: mapperModel.MTransferGDStatus.PendReceive,
        text: "待领取",
    },
    {
        value: mapperModel.MTransferGDStatus.Received,
        text: "已领取",
    },
    {
        value: mapperModel.MTransferGDStatus.Completed,
        text: "已完成",
    },
    {
        value: mapperModel.MTransferGDStatus.Canceled,
        text: "已取消",
    },
]);

export const MQualityAttributeText = Object.freeze([
    {
        value: mapperModel.MQualityAttribute.Good,
        text: "良品",
    },
    {
        value: mapperModel.MQualityAttribute.Defective,
        text: "次品",
    },
]);

export const MFuncAttributeText = Object.freeze([
    {
        value: mapperModel.MFuncAttribute.Storage,
        text: "存储",
    },
    {
        value: mapperModel.MFuncAttribute.PickGoods,
        text: "捡货",
    },
]);
