import axios from "axios";

function getPermissionList(data) {
    // 获取权限列表
    return axios({
        url: "/api/permissions/list",
        method: "get",
        params: data,
    });
}
function permissionsTree(data) {
    // 获取权限树
    return axios({
        url: "/api/permissions/trees",
        method: "get",
        params: data,
    });
}
function createPermission(data) {
    // 创建权限
    return axios({
        url: "/api/permissions/create",
        method: "post",
        data: data,
    });
}
function deletePermission(data) {
    // 删除权限
    return axios({
        url: "/api/permissions/delete",
        method: "post",
        data: data,
    });
}
function updatePermissionStatus(data) {
    // 更改权限状态
    return axios({
        url: "/api/permissions/edit",
        method: "post",
        data: data,
    });
}
function updatePermissionDetails(data) {
    // 更改权限内容
    return axios({
        url: "/api/permissions/update",
        method: "post",
        data: data,
    });
}
function getWarehouseList() {
    // 新增编辑权限的仓库列表
    return axios({
        url: "/api/roles/warehouse",
        method: "get",
    });
}
function getFictitiousList(data) {
    return axios({
        url: "/api/roles/fictitious",
        method: "post",
        data,
    });
}

export default {
    getWarehouseList,
    updatePermissionDetails,
    updatePermissionStatus,
    getPermissionList,
    deletePermission,
    createPermission,
    permissionsTree,
    getFictitiousList,
};
