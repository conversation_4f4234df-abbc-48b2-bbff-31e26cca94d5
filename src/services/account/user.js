import axios from "axios";

function login(data) {
    // 登陆
    return axios({
        url: "/auth/login",
        method: "post",
        data: data
    });
}
function getCaptcha(data) {
    // 生成验证码图片
    return axios({
        url: "/auth/getCaptcha?randstr=" + data,
        method: "get"
    });
}
function getPermission(data) {
    // getUserInfo
    return axios({
        url: "/api/admins/data",
        method: "get",
        params: data
    });
}
function logout(data) {
    return axios({
        url: "/auth/logout",
        method: "post",
        data: data
    });
}
function getRoleList(data) {
    return axios({
        url: "/api/roles/list",
        method: "get",
        params: data
    });
}
function updateRoleStatus(data) {
    return axios({
        url: "/api/roles/edit",
        method: "post",
        data: data
    });
}
function deleteRole(data) {
    return axios({
        url: "/api/roles/delete",
        method: "post",
        data: data
    });
}
function getPermissionList() {
    return axios({
        url: "/api/roles/permissions",
        method: "get"
    });
}
function createRole(data) {
    return axios({
        url: "/api/roles/create",
        method: "post",
        data
    });
}
function updateRole(data) {
    return axios({
        url: "/api/roles/update",
        method: "post",
        data
    });
}
function getUserList(data) {
    // 用户列表
    return axios({
        url: "/api/admins/list",
        method: "get",
        params: data
    });
}
function updateUserStatus(data) {
    // 更改用户状态
    return axios({
        url: "/api/admins/edit",
        method: "post",
        data
    });
}
function deleteUser(data) {
    // 删除用户
    return axios({
        url: "/api/admins/delete",
        method: "post",
        data
    });
}
function createUser(data) {
    // 创建用户
    return axios({
        url: "/api/admins/create",
        method: "post",
        data
    });
}
function getRoleSelectList() {
    // 新增用户获取角色列表
    return axios({
        url: "/api/admins/roles",
        method: "get"
    });
}
function updateUserInfo(data) {
    // 修改用户
    return axios({
        url: "/api/admins/update",
        method: "post",
        data
    });
}
function updatePassword(data) {
    // 自主修改密码
    return axios({
        url: "/api/admins/update_pwd",
        method: "post",
        data
    });
}
function getBoardData() {
    // 看板
    return axios({
        url: "/admin/outbound/statistics/workBoard",
        method: "get"
    });
}

export default {
    login,
    updateUserStatus,
    getBoardData,
    deleteUser,
    createUser,
    getRoleSelectList,
    updateUserInfo,
    updatePassword,
    getUserList,
    getPermissionList,
    createRole,
    updateRoleStatus,
    getRoleList,
    getCaptcha,
    updateRole,
    logout,
    deleteRole,
    getPermission
};
