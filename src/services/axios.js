import axios from "axios";
import { Message, Loading } from "element-ui";

import router from "@/config/PageRoutes";
import Cookies from "js-cookie";

axios.interceptors.request.use(
    (config) => {
        if (!config.isCloseLoading) {
            Loading.service({
                lock: true,
                fullscreen: true,
                text: "拼命加载中",
                background: "Transparent",
            });
        }

        if (
            config.url !== "http://127.0.0.1:1981/print" &&
            config.url !== "http://127.0.0.1:1981/print_barcode" &&
            config.url !== "http://127.0.0.1:8888/PrinterLodop"
        ) {
            let token = Cookies.get("token");
            // eslint-disable-next-line camelcase
            let stock_id = Cookies.get("stock_id");
            if (token) {
                config.headers = {
                    ...config.headers,
                    // eslint-disable-next-line camelcase
                    warehousecheckval: stock_id,
                    system: "WMS 1.0.0_MS 1.0.0",
                    securitycheckval: token,
                };
            } else {
                config.headers = {
                    ...config.headers,
                    system: "WMS 1.0.0_MS 1.0.0",
                };
            }
            if (config.url === "/admin/outbound/ordertask/generate") {
                const { $warehousecheckval } = config.data;
                if ($warehousecheckval) {
                    config.headers.warehousecheckval = $warehousecheckval;
                }
            }
            console.log(config);
            return config;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error.response);
    }
);

axios.interceptors.response.use(
    (response) => {
        if (!response.config.isCloseLoading) {
            Loading.service().close();
        }
        if (response.data.status === "fail") {
            switch (response.data.errorCode) {
                // case "501":
                //     // 没有权限访问
                //     break;
                case "401":
                    // 没有登陆 or token失效
                    router.push({ path: "/" });
                    Cookies.remove("token");
                    Cookies.remove("stock_id");
                    break;
                case "4001":
                    // 没有登陆 or token失效
                    router.push({ path: "/" });
                    Cookies.remove("token");
                    Cookies.remove("stock_id");
                    break;
            }
            if (
                response.data.msg !== "容器编码不能为空" &&
                response.data.msg !== "容器编码错误"
            ) {
                Message.error(response.data.msg);
            }
        }
        return response;
    },
    (error) => {
        Loading.service().close();
        if (error.response.status) {
            switch (error.response.status) {
                case 500:
                    if (
                        error.response.data.errorCode == 401 ||
                        error.response.data.errorCode == 4001
                    ) {
                        router.push({ path: "/" });
                        Cookies.remove("token");
                        Cookies.remove("stock_id");
                        Message.error("登录失效，请重新登录");
                    } else {
                        Message.error(
                            "TaT ,我们的程序员好像出小差了 , 请联系客服 (服务状态码 500）"
                        );
                    }
                    break;

                case 401:
                    router.push({ path: "/" });
                    Cookies.remove("token");
                    Cookies.remove("stock_id");
                    Message.error("登录失效，请重新登录");
                    // 没有登陆 or token失效
                    break;

                default:
                    Message.error("系统异常");
                    break;
            }
            return Promise.reject(error.response);
        }
    }
);
