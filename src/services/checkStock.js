import axios from "axios";

export const getPlanList = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/getPlanList",
        params,
    });
};

export const createPlan = (data) => {
    return axios({
        url: "/admin/outbound/inventory_check/createPlan",
        method: "POST",
        data,
    });
};
// 取消盘点计划
export const cancleTaskRequest = (data) => {
    return axios({
        url: "/admin/outbound/inventory_check/cancelPlan",
        method: "POST",
        data,
    });
};

export const getAreaLocationList = () => {
    return axios({
        url: "/admin/outbound/query/getAreaLocationList",
    });
};

export const getLocationGoodsList = (data) => {
    return axios({
        url: "/admin/outbound/query/getLocationGoodsList",
        method: "POST",
        data,
    });
};

export const getPlanGoodsList = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/getPlanGoodsList",
        params,
    });
};

export const exportPlanGoodsList = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/exportPlanGoodsList",
        params,
        responseType: "blob",
    });
};

export const getTaskList = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/getTaskList",
        params,
    });
};

export const getTaskDetails = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/getTaskDetails",
        params,
    });
};

export const reviewTask = (data) => {
    return axios({
        url: "/admin/outbound/inventory_check/reviewTask",
        method: "POST",
        data,
    });
};

export const getDiffGoodsList = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/getPlanDiffGoods",
        params,
    });
};

export const reviewDiffGoods = (data) => {
    return axios({
        url: "/admin/outbound/inventory_check/reviewPlanGoods",
        method: "POST",
        data,
    });
};

export const exportDiffGoodsList = (params) => {
    return axios({
        url: "/admin/outbound/inventory_check/exportPlanDiffGoods",
        params,
        responseType: "blob",
    });
};
