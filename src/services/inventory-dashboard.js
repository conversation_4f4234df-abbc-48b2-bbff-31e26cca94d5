import axios from "axios";

/**
 * 获取库存统计数据
 * 包含当日入库/出库数量、同比增长率、月度和年度数据
 */
function getInventoryBoard() {
    return axios({
        url: "/admin/outbound/statistics/InventoryBoard",
        method: "get",
    });
}

/**
 * 获取联系人和虚拟仓数据
 * 用于筛选器中的姓名和虚拟仓选择
 */
function getContactWarehouse() {
    return axios({
        url: "/admin/outbound/statistics/ContactWarehouse",
        method: "get",
    });
}

/**
 * 获取库存周转率数据
 * @param {Object} params - 查询参数
 * @param {number} params.page - 当前页
 * @param {number} params.limit - 返回条数
 * @param {string} params.contacts_name - 联系人姓名
 * @param {string} params.start_date - 开始日期：2025-01
 * @param {string} params.end_date - 结束日期：2025-06
 * @param {string} [params.short_code] - 简码（可选）
 * @param {number} [params.fictitious_id] - 虚拟仓ID（可选）
 * @param {string} [params.sort] - 排序（周转率）：asc-正序，desc-倒序（可选）
 */
function getInventoryTurnover(params) {
    return axios({
        url: "/admin/outbound/statistics/InventoryTurnover",
        method: "get",
        params,
    });
}

/**
 * 导出库存周转率数据
 * @param {Object} params - 导出参数，与查询参数一致
 */
function exportInventoryTurnover(params) {
    return axios({
        url: "/admin/outbound/statistics/exportInventoryTurnover",
        method: "get",
        params,
        responseType: "blob", // 用于文件下载
    });
}

export default {
    getInventoryBoard,
    getContactWarehouse,
    getInventoryTurnover,
    exportInventoryTurnover,
};
