import axios from "axios";
// import qs from "qs";
function getWarehousingList(data) {
    // 获取商品档案列表
    return axios({
        url: "/admin/stock/warehousing/list",
        method: "get",
        params: data,
    });
}

function addWarehousing(data) {
    // 新增入库单
    return axios({
        url: "/admin/stock/warehousing/addWarehousing",
        method: "post",
        data,
    });
}
function addWarehousingOrder(data) {
    // 新 新增入库单
    return axios({
        url: "/admin/stock/storage/add",
        method: "post",
        data,
    });
}
function CancelReturnReceiptOrder(data) {
    // 撤销退货单
    return axios({
        url: "/admin/outbound/order/CancelReturnReceiptOrder",
        method: "post",
        data,
    });
}

function addInventoryTask(data) {
    // 生成清点任务
    return axios({
        url: "/admin/stock/warehousing/addInventoryTask",
        method: "post",
        data: data,
    });
}
function upTask(data) {
    // 生成上架任务
    return axios({
        url: "/admin/stock/warehousing/upTask",
        method: "post",
        data: data,
    });
}
function getGoodsList(data) {
    // 查看入库单的商品列表
    return axios({
        url: "/admin/stock/warehousing/goodsList",
        method: "get",
        params: data,
    });
}
function getGoodsListOrder(data) {
    // 新 查看入库单的商品列表
    return axios({
        url: "/admin/stock/storage/goodsList",
        method: "get",
        params: data,
    });
}

function stopWarehousing(data) {
    // 终止入库单操作
    return axios({
        url: "/admin/stock/warehousing/stop",
        method: "post",
        data: data,
    });
}
function getClearList(data) {
    // 获取清点任务列表
    return axios({
        url: "/admin/stock/warehousing/getInventoryTaskList",
        method: "get",
        params: data,
    });
}
function getUpTaskList(data) {
    // 获取上架任务列表
    return axios({
        url: "/admin/stock/warehousing/getUpTaskList",
        method: "get",
        params: data,
    });
}
function cancelInventoryTask(data) {
    // 取消清点
    return axios({
        url: "/admin/stock/warehousing/cancelInventoryTask",
        method: "post",
        data: data,
    });
}
function cancelUpTask(data) {
    // 取消上架
    return axios({
        url: "/admin/stock/warehousing/cancelUpTask",
        method: "post",
        data: data,
    });
}
function getExpressList() {
    // 快递公司列表
    return axios({
        url: "/admin/outbound/ogisticscompanies/all_list",
        method: "get",
    });
}
function createGenerate(data) {
    // 生成波次
    return axios({
        url: "/admin/outbound/ordertask/generate",
        method: "post",
        data,
    });
}
function getOrderTaskList(data) {
    // 波次列表
    return axios({
        url: "/admin/outbound/ordertask/list",
        method: "get",
        params: data,
    });
}
function cancelWave(data) {
    // 取消波次
    return axios({
        url: "/admin/outbound/ordertask/cancel",
        method: "post",
        data: data,
    });
}
function containerOrderList(data) {
    // 打包复核-容器订单列表
    return axios({
        url: "/admin/outbound/review/shiptaskorder?container_code=" + data,
        method: "get",
    });
}
function reviewoper(data) {
    // 打包复核-复核操作
    return axios({
        url: "/admin/outbound/review/reviewoper",
        method: "post",
        data: data,
    });
}
// eslint-disable-next-line camelcase
function print(data) {
    // 打印
    return axios({
        url: "http://127.0.0.1:1981/print",
        method: "post",
        config: {
            headers: {},
        },
        data,
    });
}
function printerLodop(data) {
    // 打印
    return axios({
        url: "http://127.0.0.1:8888/PrinterLodop",
        method: "post",
        config: {
            headers: {},
        },
        data,
    });
}

function printBarCode(data) {
    // 打印条码
    return axios({
        url: "http://127.0.0.1:1981/print_barcode",
        method: "post",
        config: {
            headers: {},
        },
        data,
    });
}

function getPrintInfo(data) {
    // 获取打印面单数据
    return axios({
        url: "/admin/outbound/review/face_sheet",
        method: "get",
        params: data,
    });
}
function getPlatformList(data) {
    // 获取平台列表
    return axios({
        url: "/admin/outbound/order/platform_list",
        method: "get",
        params: data,
    });
}
function DownloadTracingSourceCode(data) {
    return axios({
        url: "/admin/outbound/ordertask/DownloadTracingSourceCode",
        method: "get",
        params: data,
        responseType: "blob",
    });
}

function getOutboundList(data) {
    // 获取出库单列表
    return axios({
        url: "/admin/outbound/order/list",
        method: "get",
        params: data,
    });
}
function getHistroyPrint(data) {
    // 获取订单的运单列表
    return axios({
        url: "/admin/outbound/order/mail_no",
        method: "get",
        params: data,
    });
}
function getFaceSheetFormOrderId(data) {
    // 运单数组获取打印面单
    return axios({
        url: "/admin/outbound/order/mail_face_sheet",
        method: "post",
        data,
    });
}
function cancelOrder(data) {
    // 取消新品入库单（批量）
    return axios({
        url: "/admin/outbound/order/cancel",
        method: "post",
        data,
    });
}
function commodityError(data) {
    // 入库单商品异常详情列表
    return axios({
        url: "/admin/stock/errTask/list",
        method: "get",
        params: data,
    });
}

function getSituationCount() {
    // 订单完成数量
    return axios({
        url: "/admin/outbound/review/situation",
        method: "get",
    });
}
function resetOrder(data) {
    // 重新复核订单
    return axios({
        url: "/admin/outbound/review/reset",
        method: "post",
        data,
    });
}
function createOutBound(data) {
    // 创建出库单
    return axios({
        url: "/admin/outbound/order/create",
        method: "post",
        data,
    });
}
function importOrder(data) {
    // 发货订单导入
    return axios({
        url: "/admin/outbound/order/import",
        method: "post",
        data,
    });
}
function refundOrderList(data) {
    // 退货订单列表
    return axios({
        url: "/admin/stock/returns/list",
        method: "get",
        params: data,
    });
}
function returnInventoryList(data) {
    // 退货清点列表
    return axios({
        url: "/admin/stock/returns/returnInventoryList",
        method: "get",
        params: data,
    });
}
function getPosition(data) {
    // 获取存放位置
    return axios({
        url: "/admin/stock/returns/getPosition",
        method: "get",
        params: data,
    });
}

function deleteScanRecord(data) {
    // 删除
    return axios({
        url: "/admin/stock/returns/delScanRecord",
        method: "post",
        data,
    });
}
function refundGoodsList(data) {
    // 退货订单商品列表
    return axios({
        url: "/admin/stock/returns/goodsList",
        method: "get",
        params: data,
    });
}
function exportTable(data) {
    // 导出入库单
    return axios({
        url: "/admin/stock/warehousing/export",
        method: "get",
        params: data,
        responseType: "blob",
    });
}
function exportAll(data) {
    return axios({
        url: "/admin/stock/storage/export_all",
        method: "get",
        params: data,
        responseType: "blob",
    });
}

function exportTableOrder(data) {
    // 导出入库单
    return axios({
        url: "/admin/stock/storage/export",
        method: "get",
        params: data,
        responseType: "blob",
    });
}
function exportTableAction(data) {
    // 发送发货单 垃圾接口1
    return axios({
        url: "/admin/outbound/order/send_export",
        method: "get",
        params: data,
        responseType: "blob",
    });
}
function addRefundOrder(data) {
    // 创建退货单
    return axios({
        url: "/admin/stock/returns/add",
        method: "post",
        data,
    });
}
function verifyAuthPassword(data) {
    // 验证重打、增加包裹权限
    return axios({
        url: "/api/warehouse/verifyAuthPassword",
        method: "get",
        params: data,
    });
}
function updateOutOrder(data) {
    // 创建退货单
    return axios({
        url: "/admin/outbound/order/update",
        method: "post",
        data,
    });
}
function intelligentAnalysis() {
    // 智能分析
    return axios({
        url: "/admin/outbound/ordertask/assist_analysis",
        method: "get",
    });
}
function wareHousingOrderList(data) {
    // 新版入库单
    return axios({
        url: "/admin/stock/storage/list",
        method: "get",
        params: data,
    });
}

function stopWarehousingOrder(data) {
    // 新终止入库单操作
    return axios({
        url: "/admin/stock/storage/stop",
        method: "post",
        data: data,
    });
}
function createClearTaskOrder(data) {
    // 新 生成清点任务
    return axios({
        url: "/admin/stock/storage/addInTask",
        method: "post",
        data,
    });
}
function viewClearTaskOrder(data) {
    // 新 查看清点任务
    return axios({
        url: "/admin/stock/storage/getInTaskList",
        method: "get",
        params: data,
    });
}
function closeClearTaskOrder(data) {
    // 新 取消清点任务
    return axios({
        url: "/admin/stock/storage/cancelInTask",
        method: "post",
        data,
    });
}
function getUpTaskListOrder(data) {
    // 新 获取上架任务
    return axios({
        url: "/admin/stock/storage/getUpTaskList",
        method: "get",
        params: data,
    });
}
function createUpTaskOrder(data) {
    // 新 生成上架任务
    return axios({
        url: "/admin/stock/storage/addUpTask",
        method: "post",
        data,
    });
}
function cancelUpTaskOrder(data) {
    // 新 取消上架任务
    return axios({
        url: "/admin/stock/storage/cancelUpTask",
        method: "post",
        data,
    });
}
function getInstrList(data) {
    // 指令商品列表
    return axios({
        url: "/admin/outbound/order/get_instr",
        method: "get",
        params: data,
    });
}
function addInstruct(data) {
    // 新增指令商品列表
    return axios({
        url: "/admin/outbound/order/add_instr",
        method: "post",
        data,
    });
}
function deleteInstruct(data) {
    // 删除指令商品列表
    return axios({
        url: "/admin/outbound/order/del_instr",
        method: "post",
        data,
    });
}
function breakOutOrder(data) {
    // 终止发货
    return axios({
        url: "/admin/outbound/order/termination",
        method: "post",
        data,
    });
}
function regainOutOrder(data) {
    // 恢复发货
    return axios({
        url: "/admin/outbound/order/restore_outbound",
        method: "post",
        data,
    });
}
function getChecklist(data) {
    // 获取订单清单信息
    return axios({
        url: "/admin/outbound/order/get_checklist",
        method: "post",
        data,
    });
}
function getPlatformType(data) {
    // 获取箱型列表
    return axios({
        url: "/admin/outbound/order/platform_type",
        method: "get",
        params: data,
    });
}
function ControlAreaInfo(data) {
    // 管控区域信息获取
    return axios({
        url: "/admin/outbound/ordertask/ControlAreaInfo",
        method: "get",
        params: data,
    });
}
function UpdateControlArea(data) {
    // 管控区域信息更新
    return axios({
        url: "/admin/outbound/ordertask/UpdateControlArea",
        method: "post",
        data,
    });
}
function getQuality_typeList() {
    // 获取品质列表
    return axios({
        url: "/admin/outbound/ordertask/quality_type",
        method: "get",
    });
}
function getCancelOrderList(data) {
    // 获取撤单上架任务列表
    return axios({
        url: "/admin/outbound/cancel_order_onshelf/list",
        method: "get",
        params: data,
    });
}
function CancelOrderTask(data) {
    // 取消撤单上架任务
    return axios({
        url: "/admin/outbound/cancel_order_onshelf/cancel",
        method: "get",
        params: data,
    });
}
function destroyOrder(data) {
    // 作废运单
    return axios({
        url: "/admin/outbound/review/invalidWaybillNo",
        method: "post",
        data,
    });
}
function initSDKConfig() {
    return axios({
        url: "/admin/outbound/review/getSfConfig",
        method: "get",
        // data,
    });
}
function getOrderRemark(data) {
    return axios({
        url: "/admin/outbound/order/getOrderRemark",
        method: "get",
        params: data,
    });
}
function SplitConsolidatedOrder(data) {
    return axios({
        url: "/admin/outbound/order/SplitConsolidatedOrder",
        method: "post",
        data,
    });
}
function getUnprocessedOrders() {
    return axios({
        url: "/admin/outbound/ordertask/UnprocessedOrders",
        isCloseLoading: true,
    });
}

function getCompanyList() {
    return axios({
        url: "/api/company/all_list",
        isCloseLoading: true,
    });
}

function getPurchaseOrderList(data) {
    return axios({
        url: "/mengya/mengya/v3/board/PurchaseOrders",
        method: "POST",
        data,
    });
}
function getSfcData(data) {
    return axios({
        url: "/mengya/mengya/v3/board/SfcData",
        method: "POST",
        data,
    });
}
function returnExport(data) {
    return axios({
        url: "/admin/stock/returns/export",
        method: "get",
        params: data,
        responseType: "blob",
    });
}
function ExportSfcData(data) {
    return axios({
        url: "/mengya/mengya/v3/board/ExportSfcData",
        method: "post",
        data,
        responseType: "blob",
    });
}
function changeStatus(data) {
    return axios({
        url: "/admin/stock/returns/changeStatus",
        method: "post",
        data,
    });
}

function getUpTaskPushDetail(data) {
    // 获取上架任务推送明细
    return axios({
        url: "/admin/stock/storage/getUpTaskPushDetail",
        method: "get",
        params: data,
    });
}
//重推erp
function pushErp(data) {
    return axios({
        url: "/admin/stock/storage/pushErp",
        method: "POST",
        data,
    });
}
export default {
    changeStatus,
    ExportSfcData,
    getSfcData,
    returnExport,
    SplitConsolidatedOrder,
    getPlatformType,
    destroyOrder,
    UpdateControlArea,
    CancelOrderTask,
    getOrderRemark,
    getCancelOrderList,
    ControlAreaInfo,
    getChecklist,
    breakOutOrder,
    initSDKConfig,
    getQuality_typeList,
    getInstrList,
    getGoodsListOrder,
    regainOutOrder,
    cancelUpTaskOrder,
    createUpTaskOrder,
    getUpTaskListOrder,
    exportTableOrder,
    addInstruct,
    viewClearTaskOrder,
    closeClearTaskOrder,
    createClearTaskOrder,
    wareHousingOrderList,
    importOrder,
    addRefundOrder,
    deleteInstruct,
    updateOutOrder,
    intelligentAnalysis,
    exportTable,
    stopWarehousingOrder,
    refundGoodsList,
    exportTableAction,
    verifyAuthPassword,
    CancelReturnReceiptOrder,
    refundOrderList,
    createGenerate,
    createOutBound,
    cancelOrder,
    resetOrder,
    addWarehousingOrder,
    getOrderTaskList,
    reviewoper,
    getExpressList,
    getSituationCount,
    commodityError,
    getPlatformList,
    getFaceSheetFormOrderId,
    getWarehousingList,
    containerOrderList,
    getHistroyPrint,
    cancelWave,
    getPrintInfo,
    addWarehousing,
    getGoodsList,
    stopWarehousing,
    cancelUpTask,
    cancelInventoryTask,
    getUpTaskList,
    upTask,
    printBarCode,
    getOutboundList,
    getClearList,
    addInventoryTask,
    print,
    printerLodop,
    getUnprocessedOrders,
    getCompanyList,
    getPurchaseOrderList,
    DownloadTracingSourceCode,
    exportAll,
    returnInventoryList,
    deleteScanRecord,
    getPosition,
    getUpTaskPushDetail,
    pushErp,
};
