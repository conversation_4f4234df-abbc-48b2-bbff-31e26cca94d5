import axios from "axios";

function getGoodsList(data) {
    // 获取商品档案列表
    return axios({
        url: "/admin/outbound/goods/list",
        method: "get",
        params: data,
    });
}
function createGoods(data) {
    // 新增商品
    return axios({
        url: "/admin/outbound/goods/add",
        method: "post",
        data: data,
    });
}
function editGoods(data) {
    // 编辑商品
    return axios({
        url: "/admin/outbound/goods/update",
        method: "post",
        data: data,
    });
}
function getStorageGoodsPhysical(data) {
    // 获取物理库位商品列表
    return axios({
        url: "/admin/stock/locationGoods/list",
        method: "get",
        params: data,
    });
}
function getGoodsUnitList() {
    // 获取商品单位列表
    return axios({
        url: "/admin/outbound/goods/unit",
        method: "get",
    });
}
function getGoodstypeList(data) {
    // 获取商品类型列表
    return axios({
        url: "/admin/outbound/goods/stype",
        method: "get",
        params: data,
    });
}

function crateGoodsBarCode(data) {
    // 生成条码
    return axios({
        url: "/admin/outbound/goods/generate_bar_code",
        method: "post",
        data,
    });
}
function getPropertyList(data) {
    // 商品属性
    return axios({
        url: "/admin/outbound/goods/property",
        method: "get",
        params: data,
    });
}
function getGoodstypeSelect() {
    // 商品主类型
    return axios({
        url: "/admin/outbound/goods/stype",
        method: "get",
    });
}
function viewInventory(data) {
    // 查看库存
    return axios({
        url: "/admin/stock/location/locationInfo",
        method: "get",
        params: data,
    });
}
function goodsCommentList(data) {
    // 查询评论备注
    return axios({
        url: "/admin/outbound/goods/comment_list",
        method: "get",
        params: data,
    });
}
function addGoodsComment(data) {
    // 新增评论备注
    return axios({
        url: "/admin/outbound/goods/release_comment",
        method: "post",
        data,
    });
}

function getGoodsTransferGDInfo(params) {
    return axios({
        url: "/admin/outbound/transfer/get_inventory",
        params,
    });
}

function createTransferGDTask(data) {
    return axios({
        url: "/admin/outbound/transfer/created",
        method: "post",
        data,
    });
}

function searchTransferGDList(params) {
    return axios({
        url: "/admin/outbound/transfer/list",
        params,
    });
}

function cancelTransferGD(data) {
    return axios({
        url: "/admin/outbound/transfer/cancel",
        method: "POST",
        data,
    });
}

function getGoodsSecondCategoryList(params) {
    return axios({
        url: "/admin/outbound/goods/SecondaryType",
        params,
    });
}
function getAllocationRecords(params) {
    return axios({
        url: "/admin/stock/fictitious/allocationRecords",
        params,
    });
}
function jionWhitelistOper(data) {
    return axios({
        url: "/admin/outbound/goods/WhitelistOper",
        method: "POST",
        data,
    });
}
function getDefectueousPic(params) {
    return axios({
        url: "/admin/stock/returns/getDefectueousPic",
        method: "get",
        params,
    });
}
function getInOutRecord(params) {
    // 获取出入口记录
    return axios({
        url: "/admin/outbound/statistics/InOutRecord",
        method: "get",
        params,
    });
}

export default {
    addGoodsComment,
    getAllocationRecords,
    getGoodsList,
    getDefectueousPic,
    getInOutRecord,
    goodsCommentList,
    getStorageGoodsPhysical,
    getPropertyList,
    crateGoodsBarCode,
    viewInventory,
    getGoodstypeSelect,
    getGoodstypeList,
    getGoodsUnitList,
    createGoods,
    editGoods,
    getGoodsTransferGDInfo,
    createTransferGDTask,
    searchTransferGDList,
    cancelTransferGD,
    getGoodsSecondCategoryList,
    jionWhitelistOper,
};
