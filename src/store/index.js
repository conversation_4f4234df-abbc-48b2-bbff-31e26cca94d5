import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);
const state = {
    permissions: [],
    userInfo: {},
    stockList: []
};

const getters = {
    permissions: state => state.permissions,
    userInfo: state => state.userInfo,
    stockList: state => state.stockList
};
const mutations = {
    setStockList(state, data) {
        state.stockList = data;
    },
    setUserInfo(state, data) {
        state.userInfo = data;
    },
    setPermissions(state, data) {
        state.permissions = data;
    }
};
const actions = {};

const store = new Vuex.Store({
    state,
    getters,
    mutations,
    actions
});
export default store;
