<template>
    <div
        class="fade page-sidebar-fixed page-header-fixed show page-container"
        v-bind:class="{
            'page-sidebar-minified': pageOptions.pageSidebarMinified,
            'page-content-full-height': pageOptions.pageContentFullHeight,
            'page-without-sidebar': pageOptions.pageWithoutSidebar,
            'page-with-right-sidebar': pageOptions.pageWithRightSidebar,
            'page-with-two-sidebar': pageOptions.pageWithTwoSidebar,
            'page-with-wide-sidebar': pageOptions.pageWithWideSidebar,
            'page-with-light-sidebar': pageOptions.pageWithLightSidebar,
            'page-with-top-menu': pageOptions.pageWithTopMenu,
            'page-sidebar-toggled': pageOptions.pageMobileSidebarToggled,
            'page-right-sidebar-toggled':
                pageOptions.pageMobileRightSidebarToggled ||
                pageOptions.pageRightSidebarToggled,
            'page-right-sidebar-collapsed':
                pageOptions.pageRightSidebarCollapsed,
            'has-scroll': pageOptions.pageBodyScrollTop,
        }"
        v-if="!pageOptions.pageEmpty"
    >
        <Header />
        <TopMenu v-if="pageOptions.pageWithTopMenu" />
        <Sidebar v-if="!pageOptions.pageWithoutSidebar" />
        <SidebarRight v-if="pageOptions.pageWithTwoSidebar" />
        <div
            class="content all_pages"
            id="content"
            v-bind:class="{
                'content-full-width': pageOptions.pageContentFullWidth,
                'content-inverse-mode': pageOptions.pageContentInverseMode,
            }"
        >
            <router-view></router-view>
            <!-- <vue-ins-progress-bar></vue-ins-progress-bar> -->
        </div>
        <Footer v-if="pageOptions.pageWithFooter" />
    </div>
    <div v-else>
        <router-view></router-view>
        <!-- <vue-ins-progress-bar></vue-ins-progress-bar> -->
    </div>
</template>

<script>
import Sidebar from "./components/sidebar/Sidebar.vue";
import SidebarRight from "./components/sidebar-right/SidebarRight.vue";
import Header from "./components/header/Header.vue";
import TopMenu from "./components/top-menu/TopMenu.vue";
import Footer from "./components/footer/Footer.vue";
import PageOptions from "./config/PageOptions.vue";
import { mapMutations, mapState } from "vuex";

export default {
    name: "app",
    components: {
        Sidebar,
        SidebarRight,
        Header,
        TopMenu,
        Footer,
    },
    data() {
        return {
            pageOptions: PageOptions,
        };
    },
    methods: {
        ...mapMutations(["setPermissions", "setUserInfo", "setStockList"]),
        handleScroll: function () {
            PageOptions.pageBodyScrollTop = window.scrollY;
        },
        async getVersion() {
            const res = await this.$request.stock.getVersion();
            if (res.data.errorCode == 0) {
                console.log(res.data.data);
                let wms_version = res.data.data.server_version;
                let localVersion = localStorage.getItem("wms_version");
                if (!localVersion) {
                    localStorage.setItem(
                        "wms_version",
                        res.data.data.server_version
                    );
                } else if (localVersion !== wms_version) {
                    this.$alert(
                        "系统已升级，请更新到最新版本 #" +
                            res.data.data.server_version,
                        "版本更新",
                        {
                            confirmButtonText: "更新版本",
                            showClose: false,
                            callback: (action) => {
                                console.log(action);
                                localStorage.setItem(
                                    "wms_version",
                                    res.data.data.server_version
                                );
                                window.location.reload();
                            },
                        }
                    );
                }
            }
        },
        getPermission() {
            if (this.cookies.get("token")) {
                let permissionData = {
                    token: this.cookies.get("token"),
                    type: 0,
                    // eslint-disable-next-line camelcase
                    stock_id: this.cookies.get("stock_id"),
                };
                this.$request.user.getPermission(permissionData).then((per) => {
                    if (per.data.errorCode == 0) {
                        this.setUserInfo(per.data.data);
                        console.log(per.data.data.warehouse);
                        this.setStockList(per.data.data.warehouse);
                        this.setPermissions(per.data.data.permission_maps);
                    } else {
                        this.$router.push({ path: "/" });
                        this.cookies.remove("token");
                        this.cookies.remove("stock_id");
                    }
                });
            } else {
                this.$router.push({ path: "/" });
            }
        },
    },
    computed: {
        ...mapState(["permissions"]),
    },
    watch: {
        $route(to) {
            console.log(to);
        },
    },
    mounted() {
        this.getVersion();
        this.getPermission();

        // this.$insProgress.finish();
    },
    created() {
        PageOptions.pageBodyScrollTop = window.scrollY;

        window.addEventListener("scroll", this.handleScroll);

        // this.$insProgress.start();

        // this.$router.beforeEach((to, from, next) => {
        //     this.$insProgress.start();
        //     next();
        // });
        // this.$router.afterEach(() => {
        //     this.$insProgress.finish();
        // });
    },
};
</script>
<style>
.el-table__expanded-cell {
    padding: 20px 50px !important;
}
button:focus {
    outline: none;
}
.content {
    margin-left: 232px;
    margin-right: 10px;
    margin-top: 10px;
}
.all_pages {
    background-color: #ffffff;
    padding: 20px 20px 30px 20px;
    height: 100%;
    border-radius: 6px;
}
.page-sidebar-minified .content {
    margin-left: 72px !important;
}
@media (max-width: 767.98px) {
    .content {
        margin-left: 0 !important;
        padding: 20px 20px;
        /* margin-left: 75px !important; */
    }

    .all_pages {
        width: 100%;
    }
}
</style>
