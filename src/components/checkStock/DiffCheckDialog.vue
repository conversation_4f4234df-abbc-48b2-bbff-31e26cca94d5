<template>
    <el-dialog
        :visible="visible"
        title="盘存处理"
        width="70%"
        :before-close="closeDialog"
    >
        <el-table :data="list" border>
            <el-table-column
                align="center"
                prop="area_location"
                label="区域库位"
            >
            </el-table-column>
            <el-table-column align="center" prop="$statusText" label="处理状态">
            </el-table-column>
            <el-table-column align="center" prop="goods_name" label="中文名">
            </el-table-column>
            <el-table-column align="center" prop="capacity" label="规格">
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点数量">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkNumsColor }">
                        {{ scope.row.check_nums }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-form style="margin-top: 20px">
            <el-form-item>
                <el-radio-group v-model="model.status">
                    <el-radio
                        v-for="item in [
                            { label: '盘盈', value: 0 },
                            { label: '盘亏', value: 1 },
                        ]"
                        :key="item.value"
                        :label="item.value"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-input
                    type="textarea"
                    :autosize="{ minRows: 2 }"
                    v-model="model.reason"
                    placeholder="请输入原因"
                >
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import * as checkStockApi from "@/services/checkStock";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        row: {
            type: Object,
            default: () => ({}),
        },
    },
    data: () => ({
        list: [],
        model: {
            check_id: 0,
            status: 0,
            check_goods_id: 0,
            reason: "",
        },
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.model = this.$options.data().model;
                const { id, check_id } = this.row;
                this.model = Object.assign({}, this.model, {
                    check_id,
                    check_goods_id: id,
                });
                this.list = [this.row];
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        onConfirm() {
            checkStockApi.reviewDiffGoods(this.model).then((res) => {
                if (res.data.errorCode == 0) {
                    this.$message.success("操作成功");
                    this.$emit("load");
                    this.closeDialog();
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
