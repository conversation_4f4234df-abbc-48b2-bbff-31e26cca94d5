<template>
    <el-dialog :visible="visible" title="创建" :before-close="closeDialog">
        <el-form label-width="100px">
            <el-form-item label="盘点方式">
                <el-radio-group v-model="model.check_mode">
                    <el-radio
                        v-for="item in [
                            { label: '明盘', value: 0 },
                            { label: '盲盘', value: 1 },
                        ]"
                        :key="item.value"
                        :label="item.value"
                        style="margin-bottom: 0"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item label="区域">
                <el-select
                    v-model="areaIdList"
                    placeholder="请选择区域"
                    multiple
                    @change="onAreaIdListChange"
                >
                    <el-option
                        v-for="item in areaList"
                        :key="item.area_id"
                        :label="item.area_name"
                        :value="item.area_id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="库位">
                <el-select
                    v-model="locationIdList"
                    placeholder="请选择库位"
                    multiple
                    filterable
                    remote
                    :remote-method="locationRemoteMethod"
                    @change="onLocationIdListChange"
                >
                    <el-option
                        v-for="item in locationList"
                        :key="item.location_id"
                        :label="item.location_code"
                        :value="item.location_id"
                    />
                </el-select>
                <div style="color: rgb(246, 76, 59)">
                    如果某区域下库位为空，则表示该区域全部库位
                </div>
            </el-form-item>
            <el-form-item label="指定SKU">
                <el-radio-group v-model="model.check_type">
                    <el-radio
                        v-for="item in [
                            { label: '全部SKU', value: 0 },
                            { label: '指定SKU', value: 1 },
                        ]"
                        :key="item.value"
                        :label="item.value"
                        style="margin-bottom: 0"
                        >{{ item.label }}</el-radio
                    >
                </el-radio-group>
                <div v-if="model.check_type === 0">
                    <el-select
                        value=""
                        placeholder=""
                        multiple
                        filterable
                        remote
                        :remote-method="skuRemoteMethod"
                    >
                        <el-option
                            v-for="item in skuList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <div style="color: rgb(246, 76, 59)">
                        共{{ goodsListCount }}个sku
                    </div>
                </div>
                <div v-else-if="model.check_type === 1">
                    <el-select
                        v-model="model.sku"
                        multiple
                        filterable
                        remote
                        :remote-method="skuRemoteMethod"
                    >
                        <el-option
                            v-for="item in skuList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="任务数量" >
                <div style="width: 200px;">
                    <el-input v-model="model.task_nums" placeholder="请输入大于等于1的数字" type="number">

                    </el-input>     
                </div>
                
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="onCreate">创建</el-button>
        </div>
    </el-dialog>
</template>

<script>
import * as checkStockApi from "@/services/checkStock";
let areaLocationList = [];
let goodsList = [];
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data: () => ({
        model: {
            check_mode: 0,
            location_id: "",
            check_type: 0,
            sku: [],
            task_nums:1,
        },
        areaIdList: [],
        areaList: [],
        locationIdList: [],
        locationList: [],
        skuList: [],
        goodsListCount: 0,
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.initAreaLocationList();
                const {
                    model,
                    areaIdList,
                    locationIdList,
                    skuList,
                    goodsListCount,
                } = this.$options.data();
                this.model = model;
                this.areaIdList = areaIdList;
                this.locationIdList = locationIdList;
                this.skuList = skuList;
                this.goodsListCount = goodsListCount;
            }
        },
        "model.check_type"() {
            this.model.sku = [];
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        initAreaLocationList() {
            // if (areaLocationList.length) return;
            checkStockApi.getAreaLocationList().then((res) => {
                if (res.data.errorCode == 0) {
                    const list = res?.data?.data?.list || [];
                    areaLocationList = list;
                    this.areaList = areaLocationList.map(
                        ({ area_id, area_name }) => ({ area_id, area_name })
                    );
                }
            });
        },
        locationRemoteMethod(query) {
            if (query !== "") {
                this.locationList = areaLocationList
                    .filter((item) => this.areaIdList.includes(item.area_id))
                    .map((item) =>
                        item.location.map(({ location_id, location_code }) => ({
                            location_id: `${item.area_id}&${location_id}`,
                            location_code,
                        }))
                    )
                    .flat()
                    .filter((item) => item.location_code.includes(query))
                    .slice(0, 100);
            } else {
                this.locationList = [];
            }
        },
        onAreaIdListChange(list) {
            this.locationIdList = this.locationIdList.filter((item) => {
                const [areaId] = item.split("&").map((id) => +id);
                return list.includes(areaId);
            });
            const locationIdList = this.getLocationIdList();
            console.log("onAreaIdListChange", list, locationIdList);
            this.loadGoodsList(locationIdList);
        },
        onLocationIdListChange(list) {
            const locationIdList = this.getLocationIdList();
            console.log("onLocationIdListChange", list, locationIdList);
            this.loadGoodsList(locationIdList);
        },
        getLocationIdList() {
            const areaIdList = [];
            const locationIdList = [];
            this.locationIdList.forEach((item) => {
                const [areaId, locationId] = item.split("&").map((id) => +id);
                !areaIdList.includes(areaId) && areaIdList.push(areaId);
                locationIdList.push(locationId);
            });
            const filteredAreaIdList = this.areaIdList.filter(
                (areaId) => !areaIdList.includes(areaId)
            );
            areaLocationList.forEach((item) => {
                if (filteredAreaIdList.includes(item.area_id)) {
                    locationIdList.push(
                        ...item.location.map(({ location_id }) => location_id)
                    );
                }
            });
            return locationIdList;
        },
        loadGoodsList(locationIdList = []) {
            this.loadGoodsListTimer && clearTimeout(this.loadGoodsListTimer);
            this.loadGoodsListTimer = setTimeout(() => {
                this.model.sku = [];
                if (!locationIdList.length) {
                    goodsList = [];
                    this.skuList = [];
                    this.goodsListCount = goodsList.length;
                    return;
                }
                this.model.location_id = locationIdList;
                checkStockApi
                    .getLocationGoodsList({
                        location_id: locationIdList.join(),
                    })
                    .then((res) => {
                        if (res.data.errorCode == 0) {
                            goodsList = res.data.data.list.map((item) => ({
                                value: `${item.location_id}-${item.bar_code}`,
                                label: `${item.location_code}-${item.bar_code}`,
                            }));
                            this.skuList = goodsList.slice(0, 200);
                            this.goodsListCount = goodsList.length;
                        }
                    });
            }, 500);
        },
        skuRemoteMethod(query) {
            if (query !== "") {
                this.skuList = goodsList
                    .filter((item) => item.label.includes(query))
                    .slice(0, 200);
            } else {
                this.skuList = goodsList.slice(0, 200);
            }
        },
        onCreate() {
            if(Number(this.model.task_nums) < 1) {
                this.$message.error("任务数量需大于等于1");
                return;
            }
            console.log(Number(this.model.task_nums));
            const { check_mode, location_id, check_type, sku, task_nums} = this.model;
            checkStockApi
                .createPlan({
                    task_nums:Number(task_nums),
                    check_mode,
                    location_id,
                    check_type,
                    sku: sku.map((item) => {
                        const [location_id, bar_code] = item.split("-");
                        return { location_id, bar_code };
                    }),
                })
                .then((res) => {
                    if (res.data.errorCode == 0) {
                        this.$message.success("创建成功");
                        this.$emit("load");
                        this.closeDialog();
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped></style>
