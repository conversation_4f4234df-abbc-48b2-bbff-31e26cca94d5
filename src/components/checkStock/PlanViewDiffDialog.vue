<template>
    <el-dialog
        :visible="visible"
        title="查看差异"
        width="70%"
        :before-close="closeDialog"
    >
        <el-row
            type="flex"
            justify="space-between"
            align="middle"
            style="margin-bottom: 10px"
        >
            <el-checkbox
                v-model="query.query_type"
                :true-label="1"
                :false-label="0"
                @change="reload"
                >仅看差异</el-checkbox
            >
            <el-button type="warning" @click="onExport">导出</el-button>
        </el-row>
        <el-table :data="list" border>
            <el-table-column
                align="center"
                prop="area_location"
                label="区域库位"
            >
            </el-table-column>
            <el-table-column align="center" prop="short_code" label="简码">
            </el-table-column>
            <el-table-column prop="goods_name" label="中文名">
            </el-table-column>
            <el-table-column align="center" prop="status" label="处理状态">
            </el-table-column>
            <el-table-column align="center" prop="capacity" label="规格">
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点数量">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkNumsColor }">
                        {{ scope.row.check_nums }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
    </el-dialog>
</template>

<script>
import fileDownload from "js-file-download";
import * as checkStockApi from "@/services/checkStock";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        id: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            check_id: "",
            query_type: 0,
        },
        list: [],
        total: 0,
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.query.check_id = this.id;
                this.load();
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        load() {
            checkStockApi.getPlanGoodsList(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list, total } = res.data.data;
                    list.forEach((item) => {
                        item.$checkNumsColor = "";
                        if (item.check_nums > item.inventory_nums) {
                            item.$checkNumsColor = "rgb(129, 179, 55)";
                        } else if (item.check_nums < item.inventory_nums) {
                            item.$checkNumsColor = "rgb(212, 9, 9)";
                        }
                    });
                    this.list = list;
                    this.total = total;
                }
            });
        },
        onExport() {
            checkStockApi.exportPlanGoodsList(this.query).then((res) => {
                this.$message.success("导出成功");
                fileDownload(res.data, "差异.xlsx");
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>

<style lang="scss" scoped></style>
