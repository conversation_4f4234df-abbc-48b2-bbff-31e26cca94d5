<template>
    <el-dialog
        :visible="visible"
        title="任务详情"
        width="70%"
        :before-close="closeDialog"
    >
        <div
            style="
                padding: 20px;
                color: rgb(16, 16, 16);
                background: rgb(253, 223, 223);
            "
        >
            <div style="font-weight: 700; font-size: 14px">盘点进度</div>
            <div style="font-size: 12px">
                <span>总数: {{ totalNums }}</span
                ><span style="margin-left: 30px">已点数: {{ checkNums }}</span>
            </div>
        </div>
        <el-table :data="list" border style="margin-top: 10px">
            <el-table-column
                align="center"
                prop="area_location"
                label="区域库位"
            >
            </el-table-column>
            <el-table-column align="center" prop="short_code" label="简码">
            </el-table-column>
            <el-table-column prop="goods_name" label="中文名">
            </el-table-column>
            <el-table-column align="center" prop="capacity" label="规格">
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点数量">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkNumsColor }">
                        {{ scope.row.check_nums }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
    </el-dialog>
</template>

<script>
import * as checkStockApi from "@/services/checkStock";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        id: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            task_id: "",
        },
        list: [],
        total: 0,
        totalNums: 0,
        checkNums: 0,
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.query.task_id = this.id;
                this.load();
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        load() {
            checkStockApi.getTaskDetails(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list, total, total_nums, check_nums } =
                        res.data.data;
                    list.forEach((item) => {
                        item.$checkNumsColor = "";
                        if (item.check_nums > item.inventory_nums) {
                            item.$checkNumsColor = "rgb(129, 179, 55)";
                        } else if (item.check_nums < item.inventory_nums) {
                            item.$checkNumsColor = "rgb(212, 9, 9)";
                        }
                    });
                    this.list = list;
                    this.total = total;
                    this.totalNums = total_nums;
                    this.checkNums = check_nums;
                }
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>

<style lang="scss" scoped></style>
