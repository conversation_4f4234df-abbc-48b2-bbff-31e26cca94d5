<template>
    <el-dialog :visible="visible" width="70%" :before-close="closeDialog">
        <el-table :data="list" border>
            <el-table-column align="center">
                <template #header>
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        :value="checkAll"
                        @change="onCheckAllChange"
                    ></el-checkbox>
                </template>
                <template slot-scope="scope">
                    <el-checkbox
                        :value="replayIdList.includes(scope.row.id)"
                        @change="onCheckChange(scope.row, $event)"
                    ></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="area_location"
                label="区域库位"
            >
            </el-table-column>
            <el-table-column align="center" prop="short_code" label="简码">
            </el-table-column>
            <el-table-column prop="goods_name" label="中文名">
            </el-table-column>
            <el-table-column align="center" prop="capacity" label="规格">
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点数量">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkNumsColor }">
                        {{ scope.row.check_nums }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-row
            v-if="total"
            type="flex"
            justify="center"
            style="margin-top: 20px"
        >
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </el-row>
        <div
            style="
                display: flex;
                align-items: baseline;
                margin-top: 10px;
                color: rgb(16, 16, 16);
            "
        >
            <div style="font-size: 14px">复盘数据</div>
            <div style="margin-left: 10px; font-size: 12px">
                若不勾选，将会全部复盘
            </div>
        </div>
        <el-table :data="replayList" border style="margin-top: 10px">
            <el-table-column
                align="center"
                prop="area_location"
                label="区域库位"
            >
            </el-table-column>
            <el-table-column align="center" prop="short_code" label="简码">
            </el-table-column>
            <el-table-column prop="goods_name" label="中文名">
            </el-table-column>
            <el-table-column align="center" prop="capacity" label="规格">
            </el-table-column>
            <el-table-column
                align="center"
                prop="inventory_nums"
                label="库存数量"
            >
            </el-table-column>
            <el-table-column align="center" label="盘点数量">
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.$checkNumsColor }">
                        {{ scope.row.check_nums }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer">
            <el-button type="warning" @click="onCheck(0)">复盘</el-button>
            <el-button type="primary" @click="onCheck(1)">通过</el-button>
        </div>
    </el-dialog>
</template>

<script>
import * as checkStockApi from "@/services/checkStock";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        id: {
            type: Number,
            default: 0,
        },
    },
    data: () => ({
        query: {
            page: 1,
            limit: 10,
            task_id: "",
        },
        list: [],
        total: 0,
        replayList: [],
    }),
    computed: {
        idList({ list }) {
            return list.map((item) => item.id);
        },
        replayIdList({ replayList }) {
            return replayList.map((item) => item.id);
        },
        checkAll({ replayIdList, idList }) {
            return (
                !!idList.length &&
                idList.every((id) => replayIdList.includes(id))
            );
        },
        isIndeterminate({ replayIdList, idList, checkAll }) {
            return (
                !!idList.length &&
                idList.some((id) => replayIdList.includes(id)) &&
                !checkAll
            );
        },
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.query.task_id = this.id;
                this.load();
                const { list, total, replayList } = this.$options.data();
                this.list = list;
                this.total = total;
                this.replayList = replayList;
            }
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        load() {
            checkStockApi.getTaskDetails(this.query).then((res) => {
                if (res.data.errorCode == 0) {
                    const { list, total } = res.data.data;
                    list.forEach((item) => {
                        item.$checkNumsColor = "";
                        if (item.check_nums > item.inventory_nums) {
                            item.$checkNumsColor = "rgb(129, 179, 55)";
                        } else if (item.check_nums < item.inventory_nums) {
                            item.$checkNumsColor = "rgb(212, 9, 9)";
                        }
                    });
                    this.list = list;
                    this.total = total;
                }
            });
        },
        onCheckAllChange() {
            if (this.checkAll) {
                this.replayList = this.replayList.filter(
                    (item) => !this.idList.includes(item.id)
                );
            } else {
                const list = this.list.filter(
                    (item) => !this.replayIdList.includes(item.id)
                );
                this.replayList.push(...list);
            }
        },
        onCheckChange(row, check) {
            if (check) {
                this.replayList.push(row);
            } else {
                this.replayList = this.replayList.filter(
                    (item) => item.id !== row.id
                );
            }
        },
        onCheck(status) {
            const params = { task_id: this.id, status };
            if (status === 0) {
                params.task_goods_id = this.replayIdList;
            }
            checkStockApi.reviewTask(params).then((res) => {
                if (res.data.errorCode == 0) {
                    this.$message.success("操作成功");
                    this.$emit("load");
                    this.closeDialog();
                }
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
    },
};
</script>

<style lang="scss" scoped></style>
