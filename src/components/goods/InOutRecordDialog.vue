<template>
    <el-dialog :visible="visible" title="仓库出入口记录" :before-close="closeDialog" width="80%">
        <!-- 搜索区域 -->
        <div class="search-area">
            <el-input
                v-model="searchBarCode"
                placeholder="搜索订单号"
                style="width: 200px"
                clearable
            ></el-input>
            <el-select
                        v-model="fictitious_id"
                        clearable
                        filterable
                       
                        style="margin-left: 10px"
                        placeholder="请选择虚拟仓"
                    >
                        <el-option
                            :disabled="item.fictitious_pid === 0"
                            :label="item.fictitious_name"
                            :value="item.fictitious_id"
                            v-for="(item, index) in fictitiousList"
                            :key="index"
                        ></el-option>
                    </el-select>
            <el-select
                v-model="flowDirection"
                placeholder="全部流向"
                style="width: 120px; margin-left: 10px; margin-right: 10px;"
                clearable
            >
                <el-option label="全部" value="0"></el-option>
                <el-option label="出库" value="1"></el-option>
                <el-option label="入库" value="2"></el-option>
            </el-select>
            <el-button type="primary" @click="search">搜索</el-button>
        </div>

        <!-- 表格 -->
        <el-table
            :data="tableData"
            size="mini"
            stripe
            border
            style="width: 100%; margin-top: 15px"
        >
            <el-table-column
                prop="fictitious_name"
                label="仓库名称"
                align="center"
                min-width="150"
            >
            </el-table-column>
            <el-table-column
                prop="corp_name"
                label="公司"
                align="center"
                min-width="150"
            >
            </el-table-column>
            <el-table-column
                prop="bar_code"
                label="条码"
                align="center"
                width="130"
            >
            </el-table-column>
            <el-table-column
                prop="nums"
                label="数量"
                align="center"
                width="80"
            >
            </el-table-column>
            <el-table-column
                prop="oper_time"
                label="操作时间"
                align="center"
                width="160"
            >
            </el-table-column>
            <el-table-column
                prop="open_desc"
                label="操作说明"
                align="center"
                min-width="120"
            >
            </el-table-column>
            <el-table-column
                prop="operator"
                label="操作人"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column
                prop="orderno"
                label="订单号"
                align="center"
                min-width="180"
            >
            </el-table-column>
            <el-table-column
                label="流向"
                align="center"
                width="80"
            >
                <template slot-scope="scope">
                    <el-tag :type="scope.row.source === 1 ? 'danger' : 'success'" size="mini">
                        {{ scope.row.source === 1 ? '出库' : '入库' }}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="block" style="margin-top: 15px">
            <el-pagination
                background
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                :current-page="page"
                :page-size="limit"
                :page-sizes="[15, 30, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        barCode: {
            required: true,
        },
    },
    data: () => ({
        tableData: [],
        page: 1,
        total: 0,
        limit: 15,
        searchBarCode: '',
        flowDirection: '0', // 0-全部，1-出库，2-入库
        fictitious_id:'',
        fictitiousList:[]
    }),

    watch: {
        visible(val) {
            if (val) {
                 this.getFictitiousList();
                this.searchBarCode = '';
                this.flowDirection = '0';
                this.fictitious_id = '';
               
                this.page = 1;
                
            }
        },
    },
   
    methods: {
        async load() {
            let data = {
                page: this.page,
                limit: this.limit,
                bar_code: this.barCode,
                flow_direction: this.flowDirection,
                fictitious_id:this.fictitious_id
            };
            
            // 如果有搜索条件，添加到请求参数中
            if (this.searchBarCode.trim()) {
                data.order_no = this.searchBarCode.trim();
            }

            const res = await this.$request.goods.getInOutRecord(data);
            if (res.data.errorCode == 0) {
                this.tableData = res.data.data.list || [];
                this.total = res.data.data.total || 0;
            }
        },
       async getFictitiousList() {
            let data = {
                page: 1,
                limit: 200,
            };
            const res = await this.$request.stock.getVirtualList(data);
            if (res.data.errorCode == 0) {
                    this.fictitiousList = res.data.data.list;
            }
            this.load();
        },
        search() {
            this.page = 1;
            this.load();
        },
        handleSizeChange(val) {
            this.page = 1;
            this.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.page = val;
            this.load();
        },
        closeDialog() {
            this.$emit("update:visible", false);
        },
    },
};
</script>

<style lang="scss" scoped>
.search-area {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.block {
    display: flex;
    justify-content: center;
}
</style>
