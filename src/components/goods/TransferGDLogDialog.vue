<template>
    <el-dialog :visible="visible" title="操作记录" :before-close="closeDialog">
        <el-table
            :data="tableData"
            size="mini"
            stripe
            border
            style="width: 100%"
        >
            <el-table-column min-width="230" label="调出仓库" align="center">
                <template slot-scope="scope">
                    {{ scope.row.fictitious_name }}- {{ scope.row.corp_name }}
                </template>
            </el-table-column>
            <el-table-column min-width="230" label="调入仓库" align="center">
                <template slot-scope="scope">
                    {{ scope.row.allocation_fictitious_name }}-
                    {{ scope.row.allocation_corp_name }}
                </template>
            </el-table-column>

            <el-table-column
                prop="push_erp_status"
                width="120"
                label="ERP制单状态"
                align="center"
            >
                <template slot-scope="row">
                    {{ ErpStatusFormat(row.row.push_erp_status) }}
                </template>
            </el-table-column>

            <el-table-column
                prop="number"
                width="80"
                label="数量"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="operator"
                width="90"
                label="操作人"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="create_time"
                width="160"
                label="操作时间"
                align="center"
            >
            </el-table-column>
        </el-table>
        <div class="block">
            <el-pagination
                background
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                :current-page="page"
                :page-size="limit"
                :page-sizes="[15, 30, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        barCode: {
            required: true,
        },
    },
    data: () => ({
        tableData: [],
        page: 1,
        total: 0,
        limit: 15,
    }),

    watch: {
        visible(val) {
            if (val) {
                this.load();
            }
        },
    },
    methods: {
        ErpStatusFormat(val) {
            // 0-未推送，1-推送成功，2推送失败，3-不推送
            if (val === 0) {
                return "未推送";
            } else if (val === 1) {
                return "推送成功";
            } else if (val === 2) {
                return "推送失败";
            } else if (val === 3) {
                return "不推送";
            } else {
                return "未知";
            }
        },
        async load() {
            let data = {
                page: this.page,
                limit: this.limit,
                bar_code: this.barCode,
            };
            const res = await this.$request.goods.getAllocationRecords(data);
            if (res.data.errorCode == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.totalnum;
            }
        },
        handleSizeChange(val) {
            this.page = 1;
            this.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.page = val;
            this.load();
        },
        closeDialog() {
            this.$emit("update:visible", false);
        },
    },
};
</script>

<style lang="scss" scoped>
.mgt-10 {
    margin-top: 10px;
}

.mgb-10 {
    margin-bottom: 10px;
}

/deep/ .el-form-item {
    margin-bottom: 0;
}
.block {
    margin-top: 20px;
    text-align: center;
}
</style>
