<template>
    <el-dialog :visible="visible" :before-close="closeDialog">
        <el-row slot="title" type="flex" justify="start">
            <el-button type="warning" @click="onToggleType">{{
                toggleTypebtnText
            }}</el-button>
        </el-row>
        <div>
            <div
                v-for="(item, index) in fictitious"
                :key="index"
                class="mgb-10"
            >
                <el-button
                    :type="fictitiousId === item.fictitious_id ? 'primary' : ''"
                    size="small"
                    @click="fictitiousId = item.fictitious_id"
                    >虚拟仓名称：{{ item.fictitious_name }}
                    {{ goodsNumPrefixText }}：{{ item.nums }}</el-button
                >
                <div v-if="fictitiousId === item.fictitious_id">
                    <el-row
                        v-for="(outItem, outIndex) in outLocationList"
                        :key="outIndex"
                        type="flex"
                        justify="space-between"
                        class="mgt-10"
                    >
                        <el-button
                            :type="
                                outLocationId === outItem.location_id
                                    ? 'primary'
                                    : 'info'
                            "
                            size="mini"
                            @click="outLocationId = outItem.location_id"
                        >
                            <span
                                >库位编码：{{ outItem.location_code }}（{{
                                    outItem.$attributeText
                                }}）</span
                            >
                            <span
                                >{{ goodsNumPrefixText }}：{{
                                    outItem.nums
                                }}</span
                            >
                        </el-button>
                        <el-form
                            v-if="outLocationId === outItem.location_id"
                            ref="formRef"
                            inline
                            size="small"
                            :model="formModel"
                            :rules="rules"
                        >
                            <el-form-item prop="inLocationId">
                                <el-select
                                    v-model="formModel.inLocationId"
                                    placeholder="请选择货位"
                                    filterable
                                >
                                    <el-option
                                        v-for="(
                                            inItem, inIndex
                                        ) in inLocationList"
                                        :key="inIndex"
                                        :label="`${inItem.location_code}${inItem.$attributeText}`"
                                        :value="inItem.location_id"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="nums">
                                <el-input
                                    v-model="formModel.nums"
                                    placeholder="请输入需要转换的数量"
                                />
                            </el-form-item>
                        </el-form>
                    </el-row>
                </div>
            </div>
        </div>
        <div slot="footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button
                v-if="fictitiousId && outLocationId"
                type="primary"
                @click="confirm"
                >确认</el-button
            >
        </div>
    </el-dialog>
</template>

<script>
import { MTransferGDType } from "@/tools/mapperModel";
const { GoodToDefective, DefectiveToGood } = MTransferGDType;

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        barCode: {
            required: true,
        },
    },
    data: () => ({
        type: MTransferGDType.GoodToDefective,
        fictitious: [],
        outLocationList: [],
        inLocationList: [],
        fictitiousId: "",
        outLocationId: "",
        formModel: {
            inLocationId: "",
            nums: "",
        },
        rules: {
            inLocationId: [
                { required: true, message: "请选择发货点", trigger: "change" },
            ],
            nums: [
                {
                    required: true,
                    message: "请输入需要转换的数量",
                    trigger: "blur",
                },
                {
                    pattern: /^[1-9]\d*$/,
                    message: "请输入正确的需要转换的数量",
                    trigger: "blur",
                },
            ],
        },
    }),
    computed: {
        toggleTypebtnText({ type }) {
            switch (type) {
                case GoodToDefective:
                    return "良转次";
                case DefectiveToGood:
                    return "次转良";
            }
            return "";
        },
        goodsNumPrefixText({ type }) {
            switch (type) {
                case GoodToDefective:
                    return "良品数";
                case DefectiveToGood:
                    return "次品数";
            }
            return "";
        },
    },
    watch: {
        visible(val) {
            if (val) {
                this.load();
            }
        },
        fictitiousId() {
            const { outLocationId, formModel } = this.$options.data();
            this.outLocationId = outLocationId;
            this.formModel = formModel;
        },
        outLocationId() {
            const { formModel } = this.$options.data();
            this.formModel = formModel;
        },
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        confirm() {
            this.$refs.formRef[0].validate((valid) => {
                if (!valid) return;
                const { barCode, type, fictitiousId, outLocationId } = this;
                const { inLocationId, nums } = this.formModel;
                const params = {
                    bar_code: barCode,
                    task_type: type,
                    fictitious_id: fictitiousId,
                    out_location_id: outLocationId,
                    in_location_id: inLocationId,
                    nums,
                };
                this.$request.goods.createTransferGDTask(params).then((res) => {
                    if (res.data.errorCode == 0) {
                        this.$message.success("操作成功");
                        this.$router.push("/transferGDList");
                    }
                });
            });
        },
        onToggleType() {
            switch (this.type) {
                case GoodToDefective:
                    this.type = DefectiveToGood;
                    break;
                case DefectiveToGood:
                    this.type = GoodToDefective;
                    break;
            }
            this.load();
        },
        load() {
            const params = { task_type: this.type, bar_code: this.barCode };
            this.$request.goods.getGoodsTransferGDInfo(params).then((res) => {
                if (res.data.errorCode == 0) {
                    const {
                        fictitious = [],
                        out_location = [],
                        in_location = [],
                    } = res.data.data;
                    const toText = this.$options.filters.toText;
                    out_location.concat(in_location).forEach((item) => {
                        const { quality_attribute, func_attribute } = item;
                        item.$attributeText = `${toText(
                            quality_attribute,
                            "MQualityAttributeText"
                        )}-${toText(func_attribute, "MFuncAttributeText")}`;
                    });
                    this.fictitious = fictitious;
                    this.outLocationList = out_location;
                    this.inLocationList = in_location;

                    const { fictitiousId, outLocationId, formModel } =
                        this.$options.data();
                    this.fictitiousId = fictitiousId;
                    this.outLocationId = outLocationId;
                    this.formModel = formModel;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.mgt-10 {
    margin-top: 10px;
}

.mgb-10 {
    margin-bottom: 10px;
}

/deep/ .el-form-item {
    margin-bottom: 0;
}
</style>
