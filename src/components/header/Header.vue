<template>
    <div>
        <!-- begin #header -->
        <div class="header navbar-default" id="header">
            <!-- begin navbar-header -->
            <div
                class="navbar-header"
                :style="!userInfo.super ? 'width:340px' : 'width:500px'"
            >
                <button
                    class="navbar-toggle pull-left"
                    type="button"
                    v-if="pageOptions.pageWithTwoSidebar"
                    v-on:click="toggleMobileRightSidebar"
                >
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <router-link class="navbar-brand" to=""
                    ><span class="navbar-logo"></span> <b>萌牙WMS</b>
                </router-link>
                <el-select
                    v-model="value"
                    @change="checkStock"
                    placeholder="请选择仓库"
                >
                    <el-option
                        v-for="item in stockList"
                        :key="item.stock_id"
                        :label="item.stock_name"
                        :value="item.stock_id"
                    >
                    </el-option>
                </el-select>
                <el-button
                    v-if="userInfo.super"
                    style="margin-left:10px"
                    @click="centerDialogVisible = true"
                    type="primary"
                    >新增仓库</el-button
                >
                <button
                    class="navbar-toggle pt-0 pb-0 mr-0 collapsed"
                    type="button"
                    v-if="
                        pageOptions.pageWithTopMenu &&
                            !pageOptions.pageWithoutSidebar
                    "
                    v-on:click="toggleMobileTopMenu"
                >
                    <span class="fa-stack fa-lg text-inverse">
                        <i class="far fa-square fa-stack-2x"></i>
                        <i class="fa fa-cog fa-stack-1x"></i>
                    </span>
                </button>
                <button
                    class="navbar-toggle"
                    type="button"
                    v-if="
                        pageOptions.pageWithTopMenu &&
                            pageOptions.pageWithoutSidebar
                    "
                    v-on:click="toggleMobileTopMenu"
                >
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <button
                    class="navbar-toggle p-0 m-r-0"
                    type="button"
                    v-if="pageOptions.pageWithMegaMenu"
                    v-on:click="toggleMobileMegaMenu"
                >
                    <span class="fa-stack fa-lg text-inverse m-t-2">
                        <i class="far fa-square fa-stack-2x"></i>
                        <i class="fa fa-cog fa-stack-1x"></i>
                    </span>
                </button>
                <button
                    class="navbar-toggle"
                    type="button"
                    v-if="!pageOptions.pageWithoutSidebar"
                    v-on:click="toggleMobileSidebar"
                >
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
            </div>
            <!-- end navbar-header -->

            <header-mega-menu
                v-if="pageOptions.pageWithMegaMenu"
            ></header-mega-menu>

            <!-- begin header-nav -->
            <ul class="navbar-nav navbar-right">
                <b-nav-item-dropdown
                    menu-class="media-list dropdown-menu-right"
                    no-caret
                    toggle-class="f-s-14"
                >
                    <template slot="button-content">
                        <i class="fa fa-bell"></i>
                        <span class="label">0</span>
                    </template>
                    <b-dropdown-header>消息中心 (0)</b-dropdown-header>
                    <b-dropdown-item
                        class="text-center width-300"
                        href="javascript:;"
                    >
                        暂无消息
                    </b-dropdown-item>
                </b-nav-item-dropdown>
                <b-nav-item-dropdown
                    menu-class="navbar-language"
                    no-caret
                    v-if="pageOptions.pageWithLanguageBar"
                >
                    <template slot="button-content">
                        <span
                            class="flag-icon flag-icon-us mr-1"
                            title="us"
                        ></span>
                        <span class="name d-none d-sm-inline mr-1">EN</span>
                        <b class="caret"></b>
                    </template>
                    <b-dropdown-item href="javascript:;"
                        ><span class="flag-icon flag-icon-us" title="us"></span>
                        English
                    </b-dropdown-item>
                    <b-dropdown-item href="javascript:;"
                        ><span class="flag-icon flag-icon-cn" title="cn"></span>
                        Chinese
                    </b-dropdown-item>
                    <b-dropdown-item href="javascript:;"
                        ><span class="flag-icon flag-icon-jp" title="jp"></span>
                        Japanese
                    </b-dropdown-item>
                    <b-dropdown-item href="javascript:;"
                        ><span class="flag-icon flag-icon-be" title="be"></span>
                        Belgium
                    </b-dropdown-item>
                    <b-dropdown-divider class="m-b-0"></b-dropdown-divider>
                    <b-dropdown-item class="text-center" href="javascript:;"
                        >more options</b-dropdown-item
                    >
                </b-nav-item-dropdown>
                <b-nav-item-dropdown
                    class="dropdown navbar-user"
                    menu-class="dropdown-menu-right"
                    no-caret
                >
                    <template slot="button-content">
                        <!-- <div class="image image-icon bg-black text-grey-darker">
                            <i class="fa fa-user"></i>
                        </div> -->
                        <span style="font-size: 14px" class=" d-md-inline"
                            >{{ userInfo.nickname }}
                        </span>
                        <b class="caret"></b>
                    </template>
                    <!-- updatePassword -->
                    <b-dropdown-item
                        href="javascript:;"
                        @click="updatePassword"
                    >
                        修改密码
                    </b-dropdown-item>
                    <b-dropdown-item href="javascript:;" @click="logout">
                        退出
                    </b-dropdown-item>
                </b-nav-item-dropdown>
                <li
                    class="divider d-none d-md-block"
                    v-if="pageOptions.pageWithTwoSidebar"
                ></li>
                <li
                    class="d-none d-md-block"
                    v-if="pageOptions.pageWithTwoSidebar"
                >
                    <a
                        class="f-s-14"
                        href="javascript:;"
                        v-on:click="toggleRightSidebarCollapsed"
                    >
                        <i class="fa fa-th"></i>
                    </a>
                </li>
            </ul>
            <!-- end header navigation right -->
        </div>
        <!-- end #header -->
        <el-dialog title="修改密码" :visible.sync="dialogVisible" width="40%">
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="140px"
            >
                <el-form-item label="旧密码" prop="oldPassword">
                    <el-input
                        v-model="ruleForm.oldPassword"
                        show-password
                    ></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                    <el-input
                        show-password
                        v-model="ruleForm.newPassword"
                    ></el-input>
                </el-form-item>
                <el-form-item label="重新输入新密码" prop="resetPassword">
                    <el-input
                        show-password
                        v-model="ruleForm.resetPassword"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="创建仓库"
            :visible.sync="centerDialogVisible"
            width="40%"
            center
        >
            <el-form
                :model="createForm"
                :rules="createrules"
                ref="createrules"
                label-width="140px"
            >
                <el-form-item label="仓库名称" prop="stock_name">
                    <el-input v-model="createForm.stock_name"></el-input>
                </el-form-item>
                <el-form-item label="联系人" prop="contacts_name">
                    <el-input v-model="createForm.contacts_name"></el-input>
                </el-form-item>
                <el-form-item label="联系人电话" prop="contacts_phone">
                    <el-input v-model="createForm.contacts_phone"></el-input>
                </el-form-item>
                <el-form-item label="寄件人或公司" prop="contact">
                    <el-input v-model="createForm.contact"></el-input>
                </el-form-item>
                <el-form-item label="邮编" prop="postCode">
                    <el-input v-model="createForm.postCode"></el-input>
                </el-form-item>
                <el-form-item label="寄件电话" prop="tel">
                    <el-input v-model="createForm.tel"></el-input>
                </el-form-item>
                <el-form-item label="仓库地址" prop="selectedOptions">
                    <el-cascader
                        size="large"
                        :options="options"
                        style="width:300px"
                        v-model="createForm.selectedOptions"
                        @change="handleChange"
                    >
                    </el-cascader>
                </el-form-item>
                <el-form-item label="详细地址" prop="address">
                    <el-input v-model="createForm.address"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="centerDialogVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="createWare('createrules')"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { regionData, CodeToText } from "element-china-area-data";
import { mapState, mapMutations } from "vuex";
import PageOptions from "../../config/PageOptions.vue";
import HeaderMegaMenu from "./HeaderMegaMenu.vue";
import Cookies from "js-cookie";
export default {
    name: "Header",
    components: {
        HeaderMegaMenu
    },
    data() {
        return {
            options: regionData,

            createForm: {
                selectedOptions: [],
                authorization_url: window.location.host + "/",
                address: "",
                county: "",
                city: "",
                province: "",
                tel: "",
                postCode: "",
                contact: "",
                contacts_phone: "",
                contacts_name: "",
                stock_name: ""
            },
            centerDialogVisible: false,
            value: Cookies.get("stock_id"),
            // options: [],
            pageOptions: PageOptions,
            dialogVisible: false,
            ruleForm: {
                // eslint-disable-next-line camelcase
                oldPassword: "",

                newPassword: "",
                // eslint-disable-next-line camelcase
                resetPassword: ""
            },
            createrules: {
                selectedOptions: [
                    {
                        type: "array",
                        required: true,
                        message: "请选择收件地",
                        trigger: "change"
                    }
                ],
                address: [
                    {
                        required: true,
                        message: "请输入详细地址",
                        trigger: "blur"
                    }
                ],
                tel: [
                    {
                        required: true,
                        message: "请输入寄件电话",
                        trigger: "blur"
                    },
                    {
                        pattern: /^1[3-9](\d{9})$/,
                        message: "请输入正确的手机号",
                        trigger: ["change"]
                    },
                    { max: 11, message: "请输入11位手机号", trigger: "change" }
                ],
                postCode: [
                    {
                        required: true,
                        message: "请输入邮政编码",
                        trigger: "blur"
                    }
                ],
                contact: [
                    {
                        required: true,
                        message: "请输入寄件人或公司",
                        trigger: "blur"
                    }
                ],
                contacts_phone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur"
                    },
                    {
                        pattern: /^1[3-9](\d{9})$/,
                        message: "请输入正确的手机号",
                        trigger: ["change"]
                    },
                    { max: 11, message: "请输入11位手机号", trigger: "change" }
                ],

                contacts_name: [
                    {
                        required: true,
                        message: "请输入仓库联系人",
                        trigger: "blur"
                    }
                ],
                stock_name: [
                    {
                        required: true,
                        message: "请输入仓库名称",
                        trigger: "blur"
                    }
                ]
            },
            rules: {
                // eslint-disable-next-line camelcase
                oldPassword: [
                    {
                        required: true,
                        message: "请输入旧密码",
                        trigger: "blur"
                    },
                    {
                        min: 6,
                        max: 16,
                        message: "长度在 6 到 16 个字符",
                        trigger: "blur"
                    }
                ],
                newPassword: [
                    {
                        required: true,
                        message: "请输入新密码",
                        trigger: "blur"
                    },
                    {
                        min: 6,
                        max: 16,
                        message: "长度在 6 到 16 个字符",
                        trigger: "blur"
                    }
                ],
                // eslint-disable-next-line camelcase
                resetPassword: [
                    {
                        required: true,
                        message: "请重新输入新密码",
                        trigger: "blur"
                    },
                    {
                        min: 6,
                        max: 16,
                        message: "长度在 6 到 16 个字符",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {},
    computed: {
        ...mapState(["userInfo", "stockList"])
    },
    methods: {
        ...mapMutations(["setUserInfo", "setPermissions", "setStockList"]),
        //
        // authorization_url
        createWare(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = this.createForm;
                    console.log(data);
                    this.$request.stock.createWare(data).then(res => {
                        console.log(res);
                        if (res.data.errorCode == 0) {
                            this.centerDialogVisible = false;
                        }
                    });
                }
            });
        },
        handleChange(value) {
            value.map((item, index) => {
                console.log(index);
                switch (index) {
                    case 0:
                        this.createForm.province = CodeToText[item];
                        break;
                    case 1:
                        this.createForm.city = CodeToText[item];
                        break;
                    case 2:
                        this.createForm.county = CodeToText[item];
                        break;
                }
            });
        },
        checkStock(val) {
            console.log(val);
            if (this.cookies.get("token")) {
                let permissionData = {
                    token: this.cookies.get("token"),
                    type: 0,
                    // eslint-disable-next-line camelcase
                    stock_id: val
                };
                this.$request.user.getPermission(permissionData).then(per => {
                    if (per.data.errorCode == 0) {
                        this.setUserInfo(per.data.data);
                        console.log(per.data.data.warehouse);
                        this.setStockList(per.data.data.warehouse);
                        this.cookies.set("stock_id", val);
                        this.setPermissions(per.data.data.permission_maps);
                        this.$router.push("/home");
                    } else {
                        this.$router.push({ path: "/" });
                        this.cookies.remove("token");
                        this.cookies.remove("stock_id");
                    }
                });
            } else {
                this.$router.push({ path: "/" });
            }
        },
        logout() {
            this.$confirm("您确定要退出吗, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let data = {
                    token: this.cookies.get("token")
                };
                this.$request.user.logout(data).then(r => {
                    if (r.data.errorCode == 0) {
                        this.$router.push("/");
                        let user = {};
                        let arr = [];
                        this.setUserInfo(user);
                        this.setPermissions(arr);
                        this.setStockList(arr);
                    }
                });
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    if (
                        this.ruleForm.newPassword !==
                        this.ruleForm.resetPassword
                    ) {
                        this.$message.error("俩次密码不一致，请确认后再试");
                        return;
                    }
                    let data = {
                        id: this.userInfo.id,
                        // eslint-disable-next-line camelcase
                        old_password: this.ruleForm.oldPassword,
                        password: this.ruleForm.newPassword
                    };
                    this.$request.user.updatePassword(data).then(res => {
                        console.log(res);
                        if (res.data.errorCode == 0) {
                            this.$message.success(
                                "修改密码成功，正在跳转登录页面。"
                            );
                            setTimeout(() => {
                                this.$router.push("/");
                                let user = {};
                                let arr = [];
                                this.setUserInfo(user);
                                this.setPermissions(arr);
                                this.setStockList(arr);
                            }, 1000);
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        updatePassword() {
            this.dialogVisible = true;
        },
        toggleMobileSidebar() {
            this.pageOptions.pageMobileSidebarToggled = !this.pageOptions
                .pageMobileSidebarToggled;
        },
        toggleMobileRightSidebar() {
            this.pageOptions.pageMobileRightSidebarToggled = !this.pageOptions
                .pageMobileRightSidebarToggled;
        },
        toggleMobileTopMenu() {
            this.pageOptions.pageMobileTopMenu = !this.pageOptions
                .pageMobileTopMenu;
        },
        toggleMobileMegaMenu() {
            this.pageOptions.pageMobileMegaMenu = !this.pageOptions
                .pageMobileMegaMenu;
        },
        toggleRightSidebar() {
            this.pageOptions.pageRightSidebarToggled = !this.pageOptions
                .pageRightSidebarToggled;
        },
        toggleRightSidebarCollapsed() {
            this.pageOptions.pageRightSidebarCollapsed = !this.pageOptions
                .pageRightSidebarCollapsed;
        },
        checkForm: function(e) {
            e.preventDefault();
            this.$router.push({ path: "/extra/search" });
        }
    }
};
</script>
