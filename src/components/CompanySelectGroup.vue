<template>
    <el-select
        :value="value"
        @input="$emit('input', $event)"
        clearable
        placeholder="公司"
    >
        <el-option
            v-for="item in list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        >
        </el-option>
    </el-select>
</template>

<script>
export default {
    props: ["value"],
    data: () => ({
        list: [],
    }),
    created() {
        this.$request.order.getCompanyList().then((res) => {
            if (res.data.errorCode == 0) {
                this.list = res.data.data;
            }
        });
    },
};
</script>
