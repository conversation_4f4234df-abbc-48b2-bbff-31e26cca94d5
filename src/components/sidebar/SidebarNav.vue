<template>
    <!-- begin sidebar nav -->
    <ul class="nav" v-if="menus">
        <li class="nav-search" v-if="pageOptions.pageSidebarSearch">
            <input
                class="form-control"
                placeholder="Sidebar menu filter..."
                type="text"
                v-on:keyup="handleSidebarFilter"
            />
        </li>
        <li class="nav-header" style="font-size:16px"></li>
        <!-- // eslint-disable-next-line vue/no-unused-vars -->
        <template v-for="(menu, index) in menusFormat()">
            <sidebar-nav-list
                :key="index"
                ref="sidebarNavList"
                v-bind:menu="menu"
                v-bind:scrollTop="scrollTop"
                v-bind:status="menu.status"
                v-on:hideFloatSubmenu="handleHideFloatSubmenu"
                v-on:showFloatSubmenu="handleShowFloatSubmenu"
            ></sidebar-nav-list>
        </template>
        <!-- begin sidebar minify button -->
        <li>
            <a
                class="sidebar-minify-btn"
                href="javascript:;"
                v-on:click="handleSidebarMinify()"
                ><i class="fa fa-angle-double-left"></i
            ></a>
        </li>
        <!-- end sidebar minify button -->
    </ul>
    <!-- end sidebar nav -->
</template>

<script>
import { mapState } from "vuex";
// import SidebarMenu from "./SidebarMenu.vue";
import SidebarNavList from "./SidebarNavList.vue";
import PageOptions from "../../config/PageOptions.vue";

export default {
    name: "SidebarNav",
    props: ["scrollTop"],
    components: {
        SidebarNavList
    },
    data() {
        return {
            menus: [],
            pageOptions: PageOptions
        };
    },

    mounted() {
        this.menus = this.permissions;
    },
    computed: {
        ...mapState(["permissions"])
    },
    watch: {
        permissions() {
            this.menus = this.permissions;
        }
    },

    methods: {
        menusFormat() {
            // let obj = {};
            // console.log(this.menus);
            this.menus.map((i, index) => {
                if (i.children && i.children.length) {
                    i.children.map(c => {
                        // console.log(c);
                        if (c.children && c.children.length == 0) {
                            delete c["children"];
                        }
                    });
                } else {
                    console.log(index);
                    this.menus.splice(index, 1);
                }
            });
            return this.menus;
        },
        handleShowFloatSubmenu: function(menu, offset) {
            this.$emit("showFloatSubmenu", menu, offset);
        },
        handleHideFloatSubmenu: function() {
            this.$emit("hideFloatSubmenu");
        },
        handleCollapseOther: function(menu) {
            for (var i = 0; i < this.menus.length; i++) {
                this.$refs.sidebarNavList[i].collapse(menu);
            }
        },
        handleSidebarMinify: function() {
            this.pageOptions.pageSidebarMinified = !this.pageOptions
                .pageSidebarMinified;
            for (let x = 0; x < this.menus.length; x++) {
                this.$refs.sidebarNavList[x].collapse();
            }
        },
        handleSidebarFilter: function(e) {
            var value = e.target.value;
            value = value.toLowerCase();

            if (value) {
                for (var x = 0; x < this.menus.length; x++) {
                    var title = this.menus[x].title.toLowerCase();
                    var children = this.menus[x].children;

                    if (title.search(value) > -1) {
                        this.$refs.sidebarNavList[x].show();

                        if (children) {
                            this.$refs.sidebarNavList[x].searchExpand();
                        }
                    } else {
                        if (children) {
                            var hasActive = false;
                            for (var y = 0; y < children.length; y++) {
                                var title2 = children[y].title.toLowerCase();

                                if (title2.search(value) > -1) {
                                    hasActive = true;
                                    this.$refs.sidebarNavList[
                                        x
                                    ].$refs.sidebarNavList[y].show();
                                    this.$refs.sidebarNavList[x].searchExpand();
                                } else {
                                    if (hasActive) {
                                        this.$refs.sidebarNavList[
                                            x
                                        ].searchExpand();
                                    } else {
                                        this.$refs.sidebarNavList[x].hide();
                                    }
                                    this.$refs.sidebarNavList[
                                        x
                                    ].$refs.sidebarNavList[y].hide();
                                }
                            }
                        } else {
                            this.$refs.sidebarNavList[x].hide();
                        }
                    }
                }
            } else {
                for (var a = 0; a < this.menus.length; a++) {
                    this.$refs.sidebarNavList[a].show();

                    var submenu = this.menus[a].children;
                    if (submenu) {
                        for (var b = 0; b < submenu.length; b++) {
                            this.$refs.sidebarNavList[a].$refs.sidebarNavList[
                                b
                            ].show();
                        }
                    }
                }
            }
            console.log("------");
        }
    }
};
</script>
