<template>
    <ul class="nav">
        <li class="nav-profile">
            <a href="javascript:;" v-on:click="expand()">
                <div class="cover with-shadow"></div>
                <div class="info">
                    <!-- <img class="avatar" :src="userInfo.avatar" alt="" /> -->
                    <p>{{ userInfo.nickname }}</p>
                    <small>{{ userInfo.phone }}</small>
                </div>
            </a>
        </li>
    </ul>
</template>

<script>
import PageOptions from "../../config/PageOptions.vue";
import { mapState } from "vuex";
export default {
    name: "SidebarNavProfile",
    data() {
        return {
            stat: "",
            pageOptions: PageOptions
        };
    },
    computed: {
        ...mapState(["userInfo"])
    },
    updated() {
        // console.log(this.userInfo);
    },
    methods: {
        expand: function() {
            this.stat = this.stat == "expand" ? "collapse" : "expand";
        }
    }
};
</script>
<style lang="scss" scoped>
.avatar {
    margin-bottom: 10px;
    border-radius: 100px;
    width: 30%;
}
p {
    margin: 0;
    padding: 0;
}
</style>
