{"name": "WMS", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "s": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.2", "bootstrap-vue": "^2.21.2", "core-js": "^3.19.0", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.6", "js-base64": "^3.7.2", "js-cookie": "^2.2.1", "js-file-download": "^0.4.12", "moment": "^2.29.1", "node-sass": "^4.14.1", "sass-loader": "^8.0.0", "vue": "^2.6.12", "vue-easy-print": "0.0.8", "vue-router": "^3.5.3", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-service": "^4.5.15", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^1.1.12", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.12"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"curly": ["error", "multi-line"], "semi": "error", "comma-spacing": ["error", {"before": false, "after": true}], "no-multiple-empty-lines": ["error", {"max": 1, "maxEOF": 1}], "brace-style": ["error", "1tbs", {"allowSingleLine": true}]}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}