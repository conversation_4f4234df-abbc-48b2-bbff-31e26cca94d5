module.exports = {
    publicPath: "/",
    outputDir: "dist",
    assetsDir: "assts",
    productionSourceMap: false,
    devServer: {
        port: 80,
        host: "0.0.0.0",
        open: true,
        overlay: {
            warnings: true,
            errors: true,
        },
        proxy: {
            "/auth": {
                // 权限登陆
                target: "https://wms-test.wineyun.com/authority",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },
            "/admin/outbound": {
                // 出库
                target: "https://wms-test.wineyun.com/outbound",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },
            "/other/outbound": {
                // 出库
                target: "https://wms-test.wineyun.com/outbound",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },
            "/api/apk": {
                // 版本
                target: "https://wms-test.wineyun.com/distribute",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },

            "/api/": {
                // 权限模块
                target: "https://wms-test.wineyun.com/authority",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },
            "/admin/stock": {
                // 仓库
                target: "https://wms-test.wineyun.com/area",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },
            "/mengya/mengya": {
                target: "https://wms-test.wineyun.com",
                changeOrigin: true,
                pathRewrite: {
                    "^/": "",
                },
            },
        },
    },
};
